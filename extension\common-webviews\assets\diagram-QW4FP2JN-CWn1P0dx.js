import{p as B}from"./chunk-TMUBEWPD-CCwRZNUk.js";import{D as F,s as P,g as S,q as W,r as z,b as T,c as D,_ as n,l as x,E as $,F as v,x as A,I as E,k as R}from"./AugmentMessage-DN__F6lq.js";import{p as Y}from"./gitGraph-YCYPL57B-Bm-yDQ8z.js";import"./IconButtonAugment-CQzh_Hae.js";import"./SpinnerAugment-uKUHz-bK.js";import"./CardAugment-BqjOeIg4.js";import"./chevron-down-BMBumfK8.js";import"./index-DP6mqmYw.js";import"./message-broker-DdVtH9Vr.js";import"./async-messaging-D4p6YcQf.js";import"./BaseTextInput-BTYl5feP.js";import"./types-CGlLNakm.js";import"./file-paths-CAgP5Fvb.js";import"./partner-mcp-utils-Di7HqDS-.js";import"./folder-opened-D8PSJjEt.js";import"./CalloutAugment-CznTrv4g.js";import"./index-Bcx5x-t6.js";import"./diff-utils-D5NvkEWZ.js";import"./LanguageIcon-DGbwX4zn.js";import"./preload-helper-Dv6uf1Os.js";import"./index-D0JCd9Au.js";import"./keypress-DD1aQVr0.js";import"./await_block-CiMtb4wh.js";import"./go-to-website-mini-B1akcZ0N.js";import"./types-DDm27S8B.js";import"./utils-CThHGEuY.js";import"./ra-diff-ops-model-DLmlSKam.js";import"./CollapseButtonAugment-ffrJmKr6.js";import"./ButtonAugment-D5QDitBR.js";import"./MaterialIcon-ggitH03G.js";import"./CopyButton-D9TioOfA.js";import"./copy-CfR4-ke6.js";import"./ellipsis-Btdwvghx.js";import"./IconFilePath-B4EaQlM7.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-DNGY17t7.js";import"./TextAreaAugment-CoQvc_01.js";import"./index-GYuo8qik.js";import"./pen-to-square-BWYRDHTI.js";import"./check-BgkDRaNt.js";import"./augment-logo-btpqr34Z.js";import"./_baseUniq-BJIleJ2v.js";import"./_basePickBy-C7TCu3yc.js";import"./clone-CmvS_4ia.js";var w={packet:[]},u=structuredClone(w),j=F.packet,H=n(()=>{const t=$({...j,...v().packet});return t.showBits&&(t.paddingY+=10),t},"getConfig"),I=n(()=>u.packet,"getPacket"),f={pushWord:n(t=>{t.length>0&&u.packet.push(t)},"pushWord"),getPacket:I,getConfig:H,clear:n(()=>{A(),u=structuredClone(w)},"clear"),setAccTitle:P,getAccTitle:S,setDiagramTitle:W,getDiagramTitle:z,getAccDescription:T,setAccDescription:D},L=n(t=>{B(t,f);let e=-1,o=[],l=1;const{bitsPerRow:s}=f.getConfig();for(let{start:r,end:a,label:d}of t.blocks){if(a&&a<r)throw new Error(`Packet block ${r} - ${a} is invalid. End must be greater than start.`);if(r!==e+1)throw new Error(`Packet block ${r} - ${a??r} is not contiguous. It should start from ${e+1}.`);for(e=a??r,x.debug(`Packet block ${r} - ${e} with label ${d}`);o.length<=s+1&&f.getPacket().length<1e4;){const[g,p]=q({start:r,end:a,label:d},l,s);if(o.push(g),g.end+1===l*s&&(f.pushWord(o),o=[],l++),!p)break;({start:r,end:a,label:d}=p)}}f.pushWord(o)},"populate"),q=n((t,e,o)=>{if(t.end===void 0&&(t.end=t.start),t.start>t.end)throw new Error(`Block start ${t.start} is greater than block end ${t.end}.`);return t.end+1<=e*o?[t,void 0]:[{start:t.start,end:e*o-1,label:t.label},{start:e*o,end:t.end,label:t.label}]},"getNextFittingBlock"),M={parse:n(async t=>{const e=await Y("packet",t);x.debug(e),L(e)},"parse")},N=n((t,e,o,l)=>{const s=l.db,r=s.getConfig(),{rowHeight:a,paddingY:d,bitWidth:g,bitsPerRow:p}=r,h=s.getPacket(),i=s.getDiagramTitle(),m=a+d,c=m*(h.length+1)-(i?0:a),k=g*p+2,b=E(e);b.attr("viewbox",`0 0 ${k} ${c}`),R(b,c,k,r.useMaxWidth);for(const[y,C]of h.entries())X(b,C,y,r);b.append("text").text(i).attr("x",k/2).attr("y",c-m/2).attr("dominant-baseline","middle").attr("text-anchor","middle").attr("class","packetTitle")},"draw"),X=n((t,e,o,{rowHeight:l,paddingX:s,paddingY:r,bitWidth:a,bitsPerRow:d,showBits:g})=>{const p=t.append("g"),h=o*(l+r)+r;for(const i of e){const m=i.start%d*a+1,c=(i.end-i.start+1)*a-s;if(p.append("rect").attr("x",m).attr("y",h).attr("width",c).attr("height",l).attr("class","packetBlock"),p.append("text").attr("x",m+c/2).attr("y",h+l/2).attr("class","packetLabel").attr("dominant-baseline","middle").attr("text-anchor","middle").text(i.label),!g)continue;const k=i.end===i.start,b=h-2;p.append("text").attr("x",m+(k?c/2:0)).attr("y",b).attr("class","packetByte start").attr("dominant-baseline","auto").attr("text-anchor",k?"middle":"start").text(i.start),k||p.append("text").attr("x",m+c).attr("y",b).attr("class","packetByte end").attr("dominant-baseline","auto").attr("text-anchor","end").text(i.end)}},"drawWord"),_={byteFontSize:"10px",startByteColor:"black",endByteColor:"black",labelColor:"black",labelFontSize:"12px",titleColor:"black",titleFontSize:"14px",blockStrokeColor:"black",blockStrokeWidth:"1",blockFillColor:"#efefef"},Yt={parser:M,db:f,renderer:{draw:N},styles:n(({packet:t}={})=>{const e=$(_,t);return`
	.packetByte {
		font-size: ${e.byteFontSize};
	}
	.packetByte.start {
		fill: ${e.startByteColor};
	}
	.packetByte.end {
		fill: ${e.endByteColor};
	}
	.packetLabel {
		fill: ${e.labelColor};
		font-size: ${e.labelFontSize};
	}
	.packetTitle {
		fill: ${e.titleColor};
		font-size: ${e.titleFontSize};
	}
	.packetBlock {
		stroke: ${e.blockStrokeColor};
		stroke-width: ${e.blockStrokeWidth};
		fill: ${e.blockFillColor};
	}
	`},"styles")};export{Yt as diagram};

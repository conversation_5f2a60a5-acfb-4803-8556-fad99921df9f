import{S as m,i as C,s as g,a as b,b as p,H as B,y as k,z as E,A as U,k as f,e as S,B as z,j,n as v,l as y,d as s,f as x,h,D as A,V as F,Q as V,w as H,t as T,u as $,v as D,T as I,Y as O,Z as P,ah as Q,ai as L}from"./SpinnerAugment-uKUHz-bK.js";import{g as W}from"./utils-CThHGEuY.js";import{s as M}from"./index-GYuo8qik.js";const t1="remoteAgentStore",s1="remoteAgentStore";function e1(r){const t=r;return Array.isArray(t==null?void 0:t.agentOverviews)&&Array.isArray(t==null?void 0:t.activeWebviews)&&((t==null?void 0:t.pinnedAgents)===void 0||typeof t.pinnedAgents=="object")}function Y(r){let t,e,o=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},r[0]],n={};for(let a=0;a<o.length;a+=1)n=b(n,o[a]);return{c(){t=p("svg"),e=new B(!0),this.h()},l(a){t=k(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var l=E(t);e=U(l,!0),l.forEach(f),this.h()},h(){e.a=null,S(t,n)},m(a,l){z(a,t,l),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m134.6 51.7-10.8 140.9c-1.1 14.6-8.8 27.8-20.9 36-23.9 16.2-41.8 40.8-49.1 70.3l-1.3 5.1H168v-88c0-13.3 10.7-24 24-24s24 10.7 24 24v88h115.5l-1.3-5.1c-7.4-29.5-25.2-54.1-49.1-70.2-12.1-8.2-19.8-21.5-20.9-36l-10.8-141c-.1-1.2-.1-2.5-.1-3.7H134.8c0 1.2 0 2.5-.1 3.7zM168 352H32c-9.9 0-19.2-4.5-25.2-12.3s-8.2-17.9-5.8-27.5l6.2-25c10.3-41.3 35.4-75.7 68.7-98.3L83.1 96l3.7-48H56c-4.4 0-8.6-1.2-12.2-3.3C36.8 40.5 32 32.8 32 24 32 10.7 42.7 0 56 0h272c13.3 0 24 10.7 24 24 0 8.8-4.8 16.5-11.8 20.7-3.6 2.1-7.7 3.3-12.2 3.3h-30.8l3.7 48 7.1 92.9c33.3 22.6 58.4 57.1 68.7 98.3l6.2 25c2.4 9.6.2 19.7-5.8 27.5S361.7 352 351.9 352h-136v136c0 13.3-10.7 24-24 24s-24-10.7-24-24V352z"/>',t)},p(a,[l]){S(t,n=j(o,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&l&&a[0]]))},i:v,o:v,d(a){a&&f(t)}}}function q(r,t,e){return r.$$set=o=>{e(0,t=b(b({},t),y(o)))},[t=y(t)]}class a1 extends m{constructor(t){super(),C(this,t,q,Y,g,{})}}function G(r){let t,e;return{c(){t=p("svg"),e=p("path"),s(e,"fill-rule","evenodd"),s(e,"clip-rule","evenodd"),s(e,"d","M5.5 1C5.22386 1 5 1.22386 5 1.5C5 1.77614 5.22386 2 5.5 2H9.5C9.77614 2 10 1.77614 10 1.5C10 1.22386 9.77614 1 9.5 1H5.5ZM3 3.5C3 3.22386 3.22386 3 3.5 3H5H10H11.5C11.7761 3 12 3.22386 12 3.5C12 3.77614 11.7761 4 11.5 4H11V12C11 12.5523 10.5523 13 10 13H5C4.44772 13 4 12.5523 4 12V4L3.5 4C3.22386 4 3 3.77614 3 3.5ZM5 4H10V12H5V4Z"),s(e,"fill","currentColor"),s(t,"width","15"),s(t,"height","15"),s(t,"viewBox","0 0 15 15"),s(t,"fill","none"),s(t,"xmlns","http://www.w3.org/2000/svg")},m(o,n){x(o,t,n),h(t,e)},p:v,i:v,o:v,d(o){o&&f(t)}}}class l1 extends m{constructor(t){super(),C(this,t,null,G,g,{})}}function J(r){let t,e,o,n,a;return{c(){t=p("svg"),e=p("rect"),o=p("path"),n=p("path"),a=p("path"),s(e,"width","16"),s(e,"height","16"),s(e,"fill","currentColor"),s(e,"fill-opacity","0.01"),s(o,"fill-rule","evenodd"),s(o,"clip-rule","evenodd"),s(o,"d","M3.4718 3.46066C3.65925 3.2732 3.96317 3.2732 4.15062 3.46066L7.35062 6.66066C7.44064 6.75068 7.49121 6.87277 7.49121 7.00007C7.49121 7.12737 7.44064 7.24946 7.35062 7.33949L4.15062 10.5395C3.96317 10.7269 3.65925 10.7269 3.4718 10.5395C3.28435 10.352 3.28435 10.0481 3.4718 9.86067L6.33239 7.00007L3.4718 4.13949C3.28435 3.95203 3.28435 3.64812 3.4718 3.46066Z"),s(o,"fill","currentColor"),s(n,"fill-rule","evenodd"),s(n,"clip-rule","evenodd"),s(n,"d","M7.86854 10.6132C7.57399 10.6132 7.33521 10.8519 7.33521 11.1465C7.33521 11.441 7.57399 11.6798 7.86854 11.6798H12.1352C12.4298 11.6798 12.6685 11.441 12.6685 11.1465C12.6685 10.8519 12.4298 10.6132 12.1352 10.6132H7.86854Z"),s(n,"fill","currentColor"),s(a,"fill-rule","evenodd"),s(a,"clip-rule","evenodd"),s(a,"d","M2.13331 1.06665C1.5442 1.06665 1.06664 1.54421 1.06664 2.13332V13.8667C1.06664 14.4558 1.5442 14.9333 2.13331 14.9333H13.8667C14.4558 14.9333 14.9333 14.4558 14.9333 13.8667V2.13332C14.9333 1.54421 14.4558 1.06665 13.8667 1.06665H2.13331ZM2.13331 2.13332H13.8667V13.8667H2.13331V2.13332Z"),s(a,"fill","currentColor"),s(t,"width","16"),s(t,"height","16"),s(t,"viewBox","0 0 16 16"),s(t,"fill","none"),s(t,"xmlns","http://www.w3.org/2000/svg")},m(l,d){x(l,t,d),h(t,e),h(t,o),h(t,n),h(t,a)},p:v,i:v,o:v,d(l){l&&f(t)}}}class o1 extends m{constructor(t){super(),C(this,t,null,J,g,{})}}function Z(r){let t,e,o,n,a;return{c(){t=A("div"),e=O(r[1]),s(t,"class",o="status-label status-label--"+r[2]+" svelte-11be6ut")},m(l,d){x(l,t,d),h(t,e),a=!0},p(l,d){(!a||2&d)&&P(e,l[1]),(!a||4&d&&o!==(o="status-label status-label--"+l[2]+" svelte-11be6ut"))&&s(t,"class",o)},i(l){a||(l&&Q(()=>{a&&(n||(n=L(t,M,{duration:100,axis:"x"},!0)),n.run(1))}),a=!0)},o(l){l&&(n||(n=L(t,M,{duration:100,axis:"x"},!1)),n.run(0)),a=!1},d(l){l&&f(t),l&&n&&n.end()}}}function K(r){let t,e,o,n,a,l,d,w,i=r[3]&&Z(r);return{c(){t=A("div"),e=A("div"),n=F(),i&&i.c(),s(e,"class",o="status-dot status-dot--"+r[2]+" svelte-11be6ut"),s(t,"class","status-indicator-container svelte-11be6ut"),s(t,"role","status"),s(t,"aria-label",a="Agent status: "+r[1]),s(t,"title",l="Status: "+r[1])},m(c,u){x(c,t,u),h(t,e),h(t,n),i&&i.m(t,null),d||(w=[V(t,"mouseenter",r[8]),V(t,"mouseleave",r[9])],d=!0)},p(c,[u]){4&u&&o!==(o="status-dot status-dot--"+c[2]+" svelte-11be6ut")&&s(e,"class",o),c[3]?i?(i.p(c,u),8&u&&H(i,1)):(i=Z(c),i.c(),H(i,1),i.m(t,null)):i&&(T(),$(i,1,1,()=>{i=null}),D()),2&u&&a!==(a="Agent status: "+c[1])&&s(t,"aria-label",a),2&u&&l!==(l="Status: "+c[1])&&s(t,"title",l)},i(c){H(i)},o(c){$(i)},d(c){c&&f(t),i&&i.d(),d=!1,I(w)}}}function N(r,t,e){let o,n,a,{status:l}=t,{workspaceStatus:d}=t,{isExpanded:w=!1}=t,{hasUpdates:i=!1}=t,c=!1;return r.$$set=u=>{"status"in u&&e(4,l=u.status),"workspaceStatus"in u&&e(5,d=u.workspaceStatus),"isExpanded"in u&&e(6,w=u.isExpanded),"hasUpdates"in u&&e(7,i=u.hasUpdates)},r.$$.update=()=>{65&r.$$.dirty&&e(3,o=w||c),178&r.$$.dirty&&(e(1,n=W(l,d,i)),e(2,a=n.toString()))},[c,n,a,o,l,d,w,i,()=>e(0,c=!0),()=>e(0,c=!1)]}class n1 extends m{constructor(t){super(),C(this,t,N,K,g,{status:4,workspaceStatus:5,isExpanded:6,hasUpdates:7})}}export{s1 as S,o1 as T,n1 as a,l1 as b,a1 as c,t1 as d,e1 as v};

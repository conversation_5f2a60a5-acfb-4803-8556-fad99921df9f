var ai=Object.defineProperty;var ci=(t,e,n)=>e in t?ai(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var Y=(t,e,n)=>ci(t,typeof e!="symbol"?e+"":e,n);import{S as it,i as at,s as ct,b as Xe,d as F,f as Le,h as Ze,n as se,k as ce,a as _e,D as _t,E as ui,a9 as di,F as li,j as ar,af as pi,w as Ws,u as Gs,G as fi,a1 as wr,aj as hi,l as yt,W as de,J as mi,K as gi,L as _i,M as yi,V as Js,a0 as te,Q as _n,T as vi,Y as Er,Z as xr,C as bi,ao as yn,H as Ys,y as Vs,z as Ks,A as Xs,e as Xt,B as Zs}from"./SpinnerAugment-uKUHz-bK.js";import{B as Si,l as wi}from"./partner-mcp-utils-Di7HqDS-.js";import{C as Ei}from"./IconButtonAugment-CQzh_Hae.js";import{r as vn}from"./index-DP6mqmYw.js";const $=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Ne="9.20.0",N=globalThis;function Ge(){return cn(N),N}function cn(t){const e=t.__SENTRY__=t.__SENTRY__||{};return e.version=e.version||Ne,e[Ne]=e[Ne]||{}}function Zt(t,e,n=N){const r=n.__SENTRY__=n.__SENTRY__||{},s=r[Ne]=r[Ne]||{};return s[t]||(s[t]=e())}const Qs=Object.prototype.toString;function cr(t){switch(Qs.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return ye(t,Error)}}function ut(t,e){return Qs.call(t)===`[object ${e}]`}function eo(t){return ut(t,"ErrorEvent")}function Tr(t){return ut(t,"DOMError")}function ge(t){return ut(t,"String")}function ur(t){return typeof t=="object"&&t!==null&&"__sentry_template_string__"in t&&"__sentry_template_values__"in t}function vt(t){return t===null||ur(t)||typeof t!="object"&&typeof t!="function"}function bt(t){return ut(t,"Object")}function un(t){return typeof Event<"u"&&ye(t,Event)}function dn(t){return!!(t!=null&&t.then&&typeof t.then=="function")}function ye(t,e){try{return t instanceof e}catch{return!1}}function to(t){return!(typeof t!="object"||t===null||!t.__isVue&&!t._isVue)}function no(t){return typeof Request<"u"&&ye(t,Request)}const dr=N,xi=80;function Be(t,e={}){if(!t)return"<unknown>";try{let n=t;const r=5,s=[];let o=0,i=0;const a=" > ",c=a.length;let u;const d=Array.isArray(e)?e:e.keyAttrs,h=!Array.isArray(e)&&e.maxStringLength||xi;for(;n&&o++<r&&(u=Ti(n,d),!(u==="html"||o>1&&i+s.length*c+u.length>=h));)s.push(u),i+=u.length,n=n.parentNode;return s.reverse().join(a)}catch{return"<unknown>"}}function Ti(t,e){const n=t,r=[];if(!(n!=null&&n.tagName))return"";if(dr.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}r.push(n.tagName.toLowerCase());const s=e!=null&&e.length?e.filter(i=>n.getAttribute(i)).map(i=>[i,n.getAttribute(i)]):null;if(s!=null&&s.length)s.forEach(i=>{r.push(`[${i[0]}="${i[1]}"]`)});else{n.id&&r.push(`#${n.id}`);const i=n.className;if(i&&ge(i)){const a=i.split(/\s+/);for(const c of a)r.push(`.${c}`)}}const o=["aria-label","type","name","title","alt"];for(const i of o){const a=n.getAttribute(i);a&&r.push(`[${i}="${a}"]`)}return r.join("")}function Ct(){try{return dr.document.location.href}catch{return""}}function ro(t){if(!dr.HTMLElement)return null;let e=t;for(let n=0;n<5;n++){if(!e)return null;if(e instanceof HTMLElement){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}e=e.parentNode}return null}const qn=["debug","info","warn","error","log","assert","trace"],Qt={};function Je(t){if(!("console"in N))return t();const e=N.console,n={},r=Object.keys(Qt);r.forEach(s=>{const o=Qt[s];n[s]=e[s],e[s]=o});try{return t()}finally{r.forEach(s=>{e[s]=n[s]})}}const S=Zt("logger",function(){let t=!1;const e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return $?qn.forEach(n=>{e[n]=(...r)=>{t&&Je(()=>{N.console[n](`Sentry Logger [${n}]:`,...r)})}}):qn.forEach(n=>{e[n]=()=>{}}),e});function en(t,e=0){return typeof t!="string"||e===0||t.length<=e?t:`${t.slice(0,e)}...`}function kr(t,e){if(!Array.isArray(t))return"";const n=[];for(let r=0;r<t.length;r++){const s=t[r];try{to(s)?n.push("[VueViewModel]"):n.push(String(s))}catch{n.push("[value cannot be serialized]")}}return n.join(e)}function ki(t,e,n=!1){return!!ge(t)&&(ut(e,"RegExp")?e.test(t):!!ge(e)&&(n?t===e:t.includes(e)))}function De(t,e=[],n=!1){return e.some(r=>ki(t,r,n))}function Q(t,e,n){if(!(e in t))return;const r=t[e];if(typeof r!="function")return;const s=n(r);typeof s=="function"&&so(s,r);try{t[e]=s}catch{$&&S.log(`Failed to replace method "${e}" in object`,t)}}function ee(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch{$&&S.log(`Failed to add non-enumerable property "${e}" to object`,t)}}function so(t,e){try{const n=e.prototype||{};t.prototype=e.prototype=n,ee(t,"__sentry_original__",e)}catch{}}function lr(t){return t.__sentry_original__}function oo(t){if(cr(t))return{message:t.message,name:t.name,stack:t.stack,...Cr(t)};if(un(t)){const e={type:t.type,target:$r(t.target),currentTarget:$r(t.currentTarget),...Cr(t)};return typeof CustomEvent<"u"&&ye(t,CustomEvent)&&(e.detail=t.detail),e}return t}function $r(t){try{return e=t,typeof Element<"u"&&ye(e,Element)?Be(t):Object.prototype.toString.call(t)}catch{return"<unknown>"}var e}function Cr(t){if(typeof t=="object"&&t!==null){const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}return{}}function oe(t=function(){const e=N;return e.crypto||e.msCrypto}()){let e=()=>16*Math.random();try{if(t!=null&&t.randomUUID)return t.randomUUID().replace(/-/g,"");t!=null&&t.getRandomValues&&(e=()=>{const n=new Uint8Array(1);return t.getRandomValues(n),n[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,n=>(n^(15&e())>>n/4).toString(16))}function io(t){var e,n;return(n=(e=t.exception)==null?void 0:e.values)==null?void 0:n[0]}function Oe(t){const{message:e,event_id:n}=t;if(e)return e;const r=io(t);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function Un(t,e,n){const r=t.exception=t.exception||{},s=r.values=r.values||[],o=s[0]=s[0]||{};o.value||(o.value=e||""),o.type||(o.type="Error")}function Qe(t,e){const n=io(t);if(!n)return;const r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...e},e&&"data"in e){const s={...r==null?void 0:r.data,...e.data};n.mechanism.data=s}}function Ir(t){if(function(e){try{return e.__sentry_captured__}catch{}}(t))return!0;try{ee(t,"__sentry_captured__",!0)}catch{}return!1}const ao=1e3;function It(){return Date.now()/ao}const K=function(){const{performance:t}=N;if(!(t!=null&&t.now))return It;const e=Date.now()-t.now(),n=t.timeOrigin==null?e:t.timeOrigin;return()=>(n+t.now())/ao}();let bn;function re(){return bn||(bn=function(){var c;const{performance:t}=N;if(!(t!=null&&t.now))return[void 0,"none"];const e=36e5,n=t.now(),r=Date.now(),s=t.timeOrigin?Math.abs(t.timeOrigin+n-r):e,o=s<e,i=(c=t.timing)==null?void 0:c.navigationStart,a=typeof i=="number"?Math.abs(i+n-r):e;return o||a<e?s<=a?[t.timeOrigin,"timeOrigin"]:[i,"navigationStart"]:[r,"dateNow"]}()),bn[0]}function $i(t){const e=K(),n={sid:oe(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(r){return{sid:`${r.sid}`,init:r.init,started:new Date(1e3*r.started).toISOString(),timestamp:new Date(1e3*r.timestamp).toISOString(),status:r.status,errors:r.errors,did:typeof r.did=="number"||typeof r.did=="string"?`${r.did}`:void 0,duration:r.duration,abnormal_mechanism:r.abnormal_mechanism,attrs:{release:r.release,environment:r.environment,ip_address:r.ipAddress,user_agent:r.userAgent}}}(n)};return t&&et(n,t),n}function et(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),t.did||e.did||(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||K(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=e.sid.length===32?e.sid:oe()),e.init!==void 0&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),typeof e.started=="number"&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if(typeof e.duration=="number")t.duration=e.duration;else{const n=t.timestamp-t.started;t.duration=n>=0?n:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),typeof e.errors=="number"&&(t.errors=e.errors),e.status&&(t.status=e.status)}function Pt(t,e,n=2){if(!e||typeof e!="object"||n<=0)return e;if(t&&Object.keys(e).length===0)return t;const r={...t};for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&(r[s]=Pt(r[s],e[s],n-1));return r}const Bn="_sentrySpan";function St(t,e){e?ee(t,Bn,e):delete t[Bn]}function tn(t){return t[Bn]}function Ee(){return oe()}function At(){return oe().substring(16)}class ve{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:Ee(),sampleRand:Math.random()}}clone(){const e=new ve;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,St(e,tn(this)),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&et(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,n){return this._tags={...this._tags,[e]:n},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,n){return this._extra={...this._extra,[e]:n},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,n){return n===null?delete this._contexts[e]:this._contexts[e]=n,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;const n=typeof e=="function"?e(this):e,r=n instanceof ve?n.getScopeData():bt(n)?e:void 0,{tags:s,extra:o,user:i,contexts:a,level:c,fingerprint:u=[],propagationContext:d}=r||{};return this._tags={...this._tags,...s},this._extra={...this._extra,...o},this._contexts={...this._contexts,...a},i&&Object.keys(i).length&&(this._user=i),c&&(this._level=c),u.length&&(this._fingerprint=u),d&&(this._propagationContext=d),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,St(this,void 0),this._attachments=[],this.setPropagationContext({traceId:Ee(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(e,n){var o;const r=typeof n=="number"?n:100;if(r<=0)return this;const s={timestamp:It(),...e,message:e.message?en(e.message,2048):e.message};return this._breadcrumbs.push(s),this._breadcrumbs.length>r&&(this._breadcrumbs=this._breadcrumbs.slice(-r),(o=this._client)==null||o.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:tn(this)}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=Pt(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,n){const r=(n==null?void 0:n.event_id)||oe();if(!this._client)return S.warn("No client configured on scope - will not capture exception!"),r;const s=new Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:s,...n,event_id:r},this),r}captureMessage(e,n,r){const s=(r==null?void 0:r.event_id)||oe();if(!this._client)return S.warn("No client configured on scope - will not capture message!"),s;const o=new Error(e);return this._client.captureMessage(e,n,{originalException:e,syntheticException:o,...r,event_id:s},this),s}captureEvent(e,n){const r=(n==null?void 0:n.event_id)||oe();return this._client?(this._client.captureEvent(e,{...n,event_id:r},this),r):(S.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}class Ci{constructor(e,n){let r,s;r=e||new ve,s=n||new ve,this._stack=[{scope:r}],this._isolationScope=s}withScope(e){const n=this._pushScope();let r;try{r=e(n)}catch(s){throw this._popScope(),s}return dn(r)?r.then(s=>(this._popScope(),s),s=>{throw this._popScope(),s}):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function tt(){const t=cn(Ge());return t.stack=t.stack||new Ci(Zt("defaultCurrentScope",()=>new ve),Zt("defaultIsolationScope",()=>new ve))}function Ii(t){return tt().withScope(t)}function Pi(t,e){const n=tt();return n.withScope(()=>(n.getStackTop().scope=t,e(t)))}function Pr(t){return tt().withScope(()=>t(tt().getIsolationScope()))}function dt(t){const e=cn(t);return e.acs?e.acs:{withIsolationScope:Pr,withScope:Ii,withSetScope:Pi,withSetIsolationScope:(n,r)=>Pr(r),getCurrentScope:()=>tt().getScope(),getIsolationScope:()=>tt().getIsolationScope()}}function U(){return dt(Ge()).getCurrentScope()}function Ie(){return dt(Ge()).getIsolationScope()}function wt(...t){const e=dt(Ge());if(t.length===2){const[n,r]=t;return n?e.withSetScope(n,r):e.withScope(r)}return e.withScope(t[0])}function j(){return U().getClient()}function Ai(t){const e=t.getPropagationContext(),{traceId:n,parentSpanId:r,propagationSpanId:s}=e,o={trace_id:n,span_id:s||At()};return r&&(o.parent_span_id=r),o}const he="sentry.source",pr="sentry.sample_rate",co="sentry.previous_trace_sample_rate",ze="sentry.op",H="sentry.origin",nn="sentry.idle_span_finish_reason",ln="sentry.measurement_unit",pn="sentry.measurement_value",Ar="sentry.custom_span_name",zn="sentry.profile_id",Et="sentry.exclusive_time",Oi="sentry.link.type",Ri=0,uo=1,z=2;function lo(t,e){t.setAttribute("http.response.status_code",e);const n=function(r){if(r<400&&r>=100)return{code:uo};if(r>=400&&r<500)switch(r){case 401:return{code:z,message:"unauthenticated"};case 403:return{code:z,message:"permission_denied"};case 404:return{code:z,message:"not_found"};case 409:return{code:z,message:"already_exists"};case 413:return{code:z,message:"failed_precondition"};case 429:return{code:z,message:"resource_exhausted"};case 499:return{code:z,message:"cancelled"};default:return{code:z,message:"invalid_argument"}}if(r>=500&&r<600)switch(r){case 501:return{code:z,message:"unimplemented"};case 503:return{code:z,message:"unavailable"};case 504:return{code:z,message:"deadline_exceeded"};default:return{code:z,message:"internal_error"}}return{code:z,message:"unknown_error"}}(e);n.message!=="unknown_error"&&t.setStatus(n)}const po="_sentryScope",fo="_sentryIsolationScope";function rn(t){return{scope:t[po],isolationScope:t[fo]}}function xt(t){if(typeof t=="boolean")return Number(t);const e=typeof t=="string"?parseFloat(t):t;return typeof e!="number"||isNaN(e)||e<0||e>1?void 0:e}const fr="sentry-",Di=/^sentry-/,Li=8192;function ho(t){const e=function(r){if(!(!r||!ge(r)&&!Array.isArray(r)))return Array.isArray(r)?r.reduce((s,o)=>{const i=Or(o);return Object.entries(i).forEach(([a,c])=>{s[a]=c}),s},{}):Or(r)}(t);if(!e)return;const n=Object.entries(e).reduce((r,[s,o])=>(s.match(Di)&&(r[s.slice(fr.length)]=o),r),{});return Object.keys(n).length>0?n:void 0}function Ni(t){if(t)return function(e){if(Object.keys(e).length!==0)return Object.entries(e).reduce((n,[r,s],o)=>{const i=`${encodeURIComponent(r)}=${encodeURIComponent(s)}`,a=o===0?i:`${n},${i}`;return a.length>Li?($&&S.warn(`Not adding key: ${r} with val: ${s} to baggage header due to exceeding baggage size limits.`),n):a},"")}(Object.entries(t).reduce((e,[n,r])=>(r&&(e[`${fr}${n}`]=r),e),{}))}function Or(t){return t.split(",").map(e=>e.split("=").map(n=>{try{return decodeURIComponent(n.trim())}catch{return}})).reduce((e,[n,r])=>(n&&r&&(e[n]=r),e),{})}const mo=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function Mi(t,e){const n=function(c){if(!c)return;const u=c.match(mo);if(!u)return;let d;return u[3]==="1"?d=!0:u[3]==="0"&&(d=!1),{traceId:u[1],parentSampled:d,parentSpanId:u[2]}}(t),r=ho(e);if(!(n!=null&&n.traceId))return{traceId:Ee(),sampleRand:Math.random()};const s=function(c,u){const d=xt(u==null?void 0:u.sample_rand);if(d!==void 0)return d;const h=xt(u==null?void 0:u.sample_rate);return h&&(c==null?void 0:c.parentSampled)!==void 0?c.parentSampled?Math.random()*h:h+Math.random()*(1-h):Math.random()}(n,r);r&&(r.sample_rand=s.toString());const{traceId:o,parentSpanId:i,parentSampled:a}=n;return{traceId:o,parentSpanId:i,sampled:a,dsc:r||{},sampleRand:s}}function Rr(t=Ee(),e=At(),n){let r="";return n!==void 0&&(r=n?"-1":"-0"),`${t}-${e}${r}`}const hr=1;let Dr=!1;function ji(t){const{spanId:e,traceId:n}=t.spanContext(),{data:r,op:s,parent_span_id:o,status:i,origin:a,links:c}=q(t);return{parent_span_id:o,span_id:e,trace_id:n,data:r,op:s,status:i,origin:a,links:c}}function Fi(t){const{spanId:e,traceId:n,isRemote:r}=t.spanContext(),s=r?e:q(t).parent_span_id,o=rn(t).scope;return{parent_span_id:s,span_id:r?(o==null?void 0:o.getPropagationContext().propagationSpanId)||At():e,trace_id:n}}function go(t){return t&&t.length>0?t.map(({context:{spanId:e,traceId:n,traceFlags:r,...s},attributes:o})=>({span_id:e,trace_id:n,sampled:r===hr,attributes:o,...s})):void 0}function Me(t){return typeof t=="number"?Lr(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?Lr(t.getTime()):K()}function Lr(t){return t>9999999999?t/1e3:t}function q(t){var r;if(function(s){return typeof s.getSpanJSON=="function"}(t))return t.getSpanJSON();const{spanId:e,traceId:n}=t.spanContext();if(function(s){const o=s;return!!(o.attributes&&o.startTime&&o.name&&o.endTime&&o.status)}(t)){const{attributes:s,startTime:o,name:i,endTime:a,status:c,links:u}=t;return{span_id:e,trace_id:n,data:s,description:i,parent_span_id:"parentSpanId"in t?t.parentSpanId:"parentSpanContext"in t?(r=t.parentSpanContext)==null?void 0:r.spanId:void 0,start_timestamp:Me(o),timestamp:Me(a)||void 0,status:_o(c),op:s[ze],origin:s[H],links:go(u)}}return{span_id:e,trace_id:n,start_timestamp:0,data:{}}}function je(t){const{traceFlags:e}=t.spanContext();return e===hr}function _o(t){if(t&&t.code!==Ri)return t.code===uo?"ok":t.message||"unknown_error"}const Fe="_sentryChildSpans",Hn="_sentryRootSpan";function Nr(t,e){const n=t[Hn]||t;ee(e,Hn,n),t[Fe]?t[Fe].add(e):ee(t,Fe,new Set([e]))}function Wt(t){const e=new Set;return function n(r){if(!e.has(r)&&je(r)){e.add(r);const s=r[Fe]?Array.from(r[Fe]):[];for(const o of s)n(o)}}(t),Array.from(e)}function V(t){return t[Hn]||t}function Z(){const t=dt(Ge());return t.getActiveSpan?t.getActiveSpan():tn(U())}function Wn(){Dr||(Je(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),Dr=!0)}const Mr=50,qe="?",jr=/\(error: (.*)\)/,Fr=/captureMessage|captureException/;function yo(...t){const e=t.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0,s=0)=>{const o=[],i=n.split(`
`);for(let a=r;a<i.length;a++){const c=i[a];if(c.length>1024)continue;const u=jr.test(c)?c.replace(jr,"$1"):c;if(!u.match(/\S*Error: /)){for(const d of e){const h=d(u);if(h){o.push(h);break}}if(o.length>=Mr+s)break}}return function(a){if(!a.length)return[];const c=Array.from(a);return/sentryWrapped/.test(Lt(c).function||"")&&c.pop(),c.reverse(),Fr.test(Lt(c).function||"")&&(c.pop(),Fr.test(Lt(c).function||"")&&c.pop()),c.slice(0,Mr).map(u=>({...u,filename:u.filename||Lt(c).filename,function:u.function||qe}))}(o.slice(s))}}function Lt(t){return t[t.length-1]||{}}const qr="<anonymous>";function be(t){try{return t&&typeof t=="function"&&t.name||qr}catch{return qr}}function Ur(t){const e=t.exception;if(e){const n=[];try{return e.values.forEach(r=>{r.stacktrace.frames&&n.push(...r.stacktrace.frames)}),n}catch{return}}}const Gt={},Br={};function xe(t,e){Gt[t]=Gt[t]||[],Gt[t].push(e)}function Te(t,e){if(!Br[t]){Br[t]=!0;try{e()}catch(n){$&&S.error(`Error while instrumenting ${t}`,n)}}}function ie(t,e){const n=t&&Gt[t];if(n)for(const r of n)try{r(e)}catch(s){$&&S.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${be(r)}
Error:`,s)}}let Sn=null;function vo(t){const e="error";xe(e,t),Te(e,qi)}function qi(){Sn=N.onerror,N.onerror=function(t,e,n,r,s){return ie("error",{column:r,error:s,line:n,msg:t,url:e}),!!Sn&&Sn.apply(this,arguments)},N.onerror.__SENTRY_INSTRUMENTED__=!0}let wn=null;function bo(t){const e="unhandledrejection";xe(e,t),Te(e,Ui)}function Ui(){wn=N.onunhandledrejection,N.onunhandledrejection=function(t){return ie("unhandledrejection",t),!wn||wn.apply(this,arguments)},N.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}let zr=!1;function Gn(){const t=Z(),e=t&&V(t);if(e){const n="internal_error";$&&S.log(`[Tracing] Root span: ${n} -> Global error occurred`),e.setStatus({code:z,message:n})}}function ke(t){var n;if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const e=t||((n=j())==null?void 0:n.getOptions());return!(!e||e.tracesSampleRate==null&&!e.tracesSampler)}Gn.tag="sentry_tracingErrorCallback";const mr="production",Bi=/^o(\d+)\./,zi=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function Tt(t,e=!1){const{host:n,path:r,pass:s,port:o,projectId:i,protocol:a,publicKey:c}=t;return`${a}://${c}${e&&s?`:${s}`:""}@${n}${o?`:${o}`:""}/${r&&`${r}/`}${i}`}function Hr(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function Hi(t){const e=typeof t=="string"?function(n){const r=zi.exec(n);if(!r)return void Je(()=>{console.error(`Invalid Sentry Dsn: ${n}`)});const[s,o,i="",a="",c="",u=""]=r.slice(1);let d="",h=u;const f=h.split("/");if(f.length>1&&(d=f.slice(0,-1).join("/"),h=f.pop()),h){const p=h.match(/^\d+/);p&&(h=p[0])}return Hr({host:a,pass:i,path:d,projectId:h,port:c,protocol:s,publicKey:o})}(t):Hr(t);if(e&&function(n){if(!$)return!0;const{port:r,projectId:s,protocol:o}=n;return!(["protocol","publicKey","host","projectId"].find(i=>!n[i]&&(S.error(`Invalid Sentry Dsn: ${i} missing`),!0))||(s.match(/^\d+$/)?function(i){return i==="http"||i==="https"}(o)?r&&isNaN(parseInt(r,10))&&(S.error(`Invalid Sentry Dsn: Invalid port ${r}`),1):(S.error(`Invalid Sentry Dsn: Invalid protocol ${o}`),1):(S.error(`Invalid Sentry Dsn: Invalid projectId ${s}`),1)))}(e))return e}const So="_frozenDsc";function Jt(t,e){ee(t,So,e)}function wo(t,e){const n=e.getOptions(),{publicKey:r,host:s}=e.getDsn()||{};let o;n.orgId?o=String(n.orgId):s&&(o=function(a){const c=a.match(Bi);return c==null?void 0:c[1]}(s));const i={environment:n.environment||mr,release:n.release,public_key:r,trace_id:t,org_id:o};return e.emit("createDsc",i),i}function Eo(t,e){const n=e.getPropagationContext();return n.dsc||wo(n.traceId,t)}function $e(t){var l;const e=j();if(!e)return{};const n=V(t),r=q(n),s=r.data,o=n.spanContext().traceState,i=(o==null?void 0:o.get("sentry.sample_rate"))??s[pr]??s[co];function a(m){return typeof i!="number"&&typeof i!="string"||(m.sample_rate=`${i}`),m}const c=n[So];if(c)return a(c);const u=o==null?void 0:o.get("sentry.dsc"),d=u&&ho(u);if(d)return a(d);const h=wo(t.spanContext().traceId,e),f=s[he],p=r.description;return f!=="url"&&p&&(h.transaction=p),ke()&&(h.sampled=String(je(n)),h.sample_rand=(o==null?void 0:o.get("sentry.sample_rand"))??((l=rn(n).scope)==null?void 0:l.getPropagationContext().sampleRand.toString())),a(h),e.emit("createDsc",h,n),h}class Ue{constructor(e={}){this._traceId=e.traceId||Ee(),this._spanId=e.spanId||At()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:0}}end(e){}setAttribute(e,n){return this}setAttributes(e){return this}setStatus(e){return this}updateName(e){return this}isRecording(){return!1}addEvent(e,n,r){return this}addLink(e){return this}addLinks(e){return this}recordException(e,n){}}function le(t,e=100,n=1/0){try{return Jn("",t,e,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function xo(t,e=3,n=102400){const r=le(t,e);return s=r,function(o){return~-encodeURI(o).split(/%..|./).length}(JSON.stringify(s))>n?xo(t,e-1,n):r;var s}function Jn(t,e,n=1/0,r=1/0,s=function(){const o=new WeakSet;function i(c){return!!o.has(c)||(o.add(c),!1)}function a(c){o.delete(c)}return[i,a]}()){const[o,i]=s;if(e==null||["boolean","string"].includes(typeof e)||typeof e=="number"&&Number.isFinite(e))return e;const a=function(p,l){try{if(p==="domain"&&l&&typeof l=="object"&&l._events)return"[Domain]";if(p==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&l===global)return"[Global]";if(typeof window<"u"&&l===window)return"[Window]";if(typeof document<"u"&&l===document)return"[Document]";if(to(l))return"[VueViewModel]";if(bt(m=l)&&"nativeEvent"in m&&"preventDefault"in m&&"stopPropagation"in m)return"[SyntheticEvent]";if(typeof l=="number"&&!Number.isFinite(l))return`[${l}]`;if(typeof l=="function")return`[Function: ${be(l)}]`;if(typeof l=="symbol")return`[${String(l)}]`;if(typeof l=="bigint")return`[BigInt: ${String(l)}]`;const _=function(w){const y=Object.getPrototypeOf(w);return y!=null&&y.constructor?y.constructor.name:"null prototype"}(l);return/^HTML(\w*)Element$/.test(_)?`[HTMLElement: ${_}]`:`[object ${_}]`}catch(_){return`**non-serializable** (${_})`}var m}(t,e);if(!a.startsWith("[object "))return a;if(e.__sentry_skip_normalization__)return e;const c=typeof e.__sentry_override_normalization_depth__=="number"?e.__sentry_override_normalization_depth__:n;if(c===0)return a.replace("object ","");if(o(e))return"[Circular ~]";const u=e;if(u&&typeof u.toJSON=="function")try{return Jn("",u.toJSON(),c-1,r,s)}catch{}const d=Array.isArray(e)?[]:{};let h=0;const f=oo(e);for(const p in f){if(!Object.prototype.hasOwnProperty.call(f,p))continue;if(h>=r){d[p]="[MaxProperties ~]";break}const l=f[p];d[p]=Jn(p,l,c-1,r,s),h++}return i(e),d}function nt(t,e=[]){return[t,e]}function Wi(t,e){const[n,r]=t;return[n,[...r,e]]}function Wr(t,e){const n=t[1];for(const r of n)if(e(r,r[0].type))return!0;return!1}function Yn(t){const e=cn(N);return e.encodePolyfill?e.encodePolyfill(t):new TextEncoder().encode(t)}function Gi(t){const[e,n]=t;let r=JSON.stringify(e);function s(o){typeof r=="string"?r=typeof o=="string"?r+o:[Yn(r),o]:r.push(typeof o=="string"?Yn(o):o)}for(const o of n){const[i,a]=o;if(s(`
${JSON.stringify(i)}
`),typeof a=="string"||a instanceof Uint8Array)s(a);else{let c;try{c=JSON.stringify(a)}catch{c=JSON.stringify(le(a))}s(c)}}return typeof r=="string"?r:function(o){const i=o.reduce((u,d)=>u+d.length,0),a=new Uint8Array(i);let c=0;for(const u of o)a.set(u,c),c+=u.length;return a}(r)}function Ji(t){return[{type:"span"},t]}function Yi(t){const e=typeof t.data=="string"?Yn(t.data):t.data;return[{type:"attachment",length:e.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType},e]}const Vi={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function Gr(t){return Vi[t]}function To(t){if(!(t!=null&&t.sdk))return;const{name:e,version:n}=t.sdk;return{name:e,version:n}}function Ki(t,e,n,r){const s=To(n),o=t.type&&t.type!=="replay_event"?t.type:"event";(function(a,c){c&&(a.sdk=a.sdk||{},a.sdk.name=a.sdk.name||c.name,a.sdk.version=a.sdk.version||c.version,a.sdk.integrations=[...a.sdk.integrations||[],...c.integrations||[]],a.sdk.packages=[...a.sdk.packages||[],...c.packages||[]])})(t,n==null?void 0:n.sdk);const i=function(a,c,u,d){var f;const h=(f=a.sdkProcessingMetadata)==null?void 0:f.dynamicSamplingContext;return{event_id:a.event_id,sent_at:new Date().toISOString(),...c&&{sdk:c},...!!u&&d&&{dsn:Tt(d)},...h&&{trace:h}}}(t,s,r,e);return delete t.sdkProcessingMetadata,nt(i,[[{type:o},t]])}function Jr(t){if(!t||t.length===0)return;const e={};return t.forEach(n=>{const r=n.attributes||{},s=r[ln],o=r[pn];typeof s=="string"&&typeof o=="number"&&(e[n.name]={value:o,unit:s})}),e}class fn{constructor(e={}){this._traceId=e.traceId||Ee(),this._spanId=e.spanId||At(),this._startTime=e.startTimestamp||K(),this._links=e.links,this._attributes={},this.setAttributes({[H]:"manual",[ze]:e.op,...e.attributes}),this._name=e.name,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.endTimestamp&&(this._endTime=e.endTimestamp),this._events=[],this._isStandaloneSpan=e.isStandalone,this._endTime&&this._onSpanEnded()}addLink(e){return this._links?this._links.push(e):this._links=[e],this}addLinks(e){return this._links?this._links.push(...e):this._links=e,this}recordException(e,n){}spanContext(){const{_spanId:e,_traceId:n,_sampled:r}=this;return{spanId:e,traceId:n,traceFlags:r?hr:0}}setAttribute(e,n){return n===void 0?delete this._attributes[e]:this._attributes[e]=n,this}setAttributes(e){return Object.keys(e).forEach(n=>this.setAttribute(n,e[n])),this}updateStartTime(e){this._startTime=Me(e)}setStatus(e){return this._status=e,this}updateName(e){return this._name=e,this.setAttribute(he,"custom"),this}end(e){this._endTime||(this._endTime=Me(e),function(n){if(!$)return;const{description:r="< unknown name >",op:s="< unknown op >"}=q(n),{spanId:o}=n.spanContext(),i=`[Tracing] Finishing "${s}" ${V(n)===n?"root ":""}span "${r}" with ID ${o}`;S.log(i)}(this),this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[ze],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:_o(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[H],profile_id:this._attributes[zn],exclusive_time:this._attributes[Et],measurements:Jr(this._events),is_segment:this._isStandaloneSpan&&V(this)===this||void 0,segment_id:this._isStandaloneSpan?V(this).spanContext().spanId:void 0,links:go(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(e,n,r){$&&S.log("[Tracing] Adding an event to span:",e);const s=Yr(n)?n:r||K(),o=Yr(n)?{}:n||{},i={name:e,time:Me(s),attributes:o};return this._events.push(i),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){const e=j();if(e&&e.emit("spanEnd",this),!(this._isStandaloneSpan||this===V(this)))return;if(this._isStandaloneSpan)return void(this._sampled?function(r){const s=j();if(!s)return;const o=r[1];if(!o||o.length===0)return void s.recordDroppedEvent("before_send","span");s.sendEnvelope(r)}(function(r,s){const o=$e(r[0]),i=s==null?void 0:s.getDsn(),a=s==null?void 0:s.getOptions().tunnel,c={sent_at:new Date().toISOString(),...function(f){return!!f.trace_id&&!!f.public_key}(o)&&{trace:o},...!!a&&i&&{dsn:Tt(i)}},u=s==null?void 0:s.getOptions().beforeSendSpan,d=u?f=>{const p=q(f);return u(p)||(Wn(),p)}:q,h=[];for(const f of r){const p=d(f);p&&h.push(Ji(p))}return nt(c,h)}([this],e)):($&&S.log("[Tracing] Discarding standalone span because its trace was not chosen to be sampled."),e&&e.recordDroppedEvent("sample_rate","span")));const n=this._convertSpanToTransaction();n&&(rn(this).scope||U()).captureEvent(n)}_convertSpanToTransaction(){var c;if(!Vr(q(this)))return;this._name||($&&S.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>");const{scope:e,isolationScope:n}=rn(this),r=(c=e==null?void 0:e.getScopeData().sdkProcessingMetadata)==null?void 0:c.normalizedRequest;if(this._sampled!==!0)return;const s=Wt(this).filter(u=>u!==this&&!function(d){return d instanceof fn&&d.isStandaloneSpan()}(u)).map(u=>q(u)).filter(Vr),o=this._attributes[he];delete this._attributes[Ar],s.forEach(u=>{delete u.data[Ar]});const i={contexts:{trace:ji(this)},spans:s.length>1e3?s.sort((u,d)=>u.start_timestamp-d.start_timestamp).slice(0,1e3):s,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:e,capturedSpanIsolationScope:n,dynamicSamplingContext:$e(this)},request:r,...o&&{transaction_info:{source:o}}},a=Jr(this._events);return a&&Object.keys(a).length&&($&&S.log("[Measurements] Adding measurements to transaction event",JSON.stringify(a,void 0,2)),i.measurements=a),i}}function Yr(t){return t&&typeof t=="number"||t instanceof Date||Array.isArray(t)}function Vr(t){return!!(t.start_timestamp&&t.timestamp&&t.span_id&&t.trace_id)}const ko="__SENTRY_SUPPRESS_TRACING__";function Ot(t){const e=Co();if(e.startInactiveSpan)return e.startInactiveSpan(t);const n=function(o){const i=o.experimental||{},a={isStandalone:i.standalone,...o};if(o.startTime){const c={...a};return c.startTimestamp=Me(o.startTime),delete c.startTime,c}return a}(t),{forceTransaction:r,parentSpan:s}=t;return(t.scope?o=>wt(t.scope,o):s!==void 0?o=>$o(s,o):o=>o())(()=>{const o=U(),i=function(a){const c=tn(a);if(!c)return;const u=j();return(u?u.getOptions():{}).parentSpanIsAlwaysRootSpan?V(c):c}(o);return t.onlyIfParent&&!i?new Ue:function({parentSpan:a,spanArguments:c,forceTransaction:u,scope:d}){if(!ke()){const p=new Ue;return(u||!a)&&Jt(p,{sampled:"false",sample_rate:"0",transaction:c.name,...$e(p)}),p}const h=Ie();let f;if(a&&!u)f=function(p,l,m){const{spanId:_,traceId:w}=p.spanContext(),y=!l.getScopeData().sdkProcessingMetadata[ko]&&je(p),E=y?new fn({...m,parentSpanId:_,traceId:w,sampled:y}):new Ue({traceId:w});Nr(p,E);const g=j();return g&&(g.emit("spanStart",E),m.endTimestamp&&g.emit("spanEnd",E)),E}(a,d,c),Nr(a,f);else if(a){const p=$e(a),{traceId:l,spanId:m}=a.spanContext(),_=je(a);f=Kr({traceId:l,parentSpanId:m,...c},d,_),Jt(f,p)}else{const{traceId:p,dsc:l,parentSpanId:m,sampled:_}={...h.getPropagationContext(),...d.getPropagationContext()};f=Kr({traceId:p,parentSpanId:m,...c},d,_),l&&Jt(f,l)}return function(p){if(!$)return;const{description:l="< unknown name >",op:m="< unknown op >",parent_span_id:_}=q(p),{spanId:w}=p.spanContext(),y=je(p),E=V(p),g=E===p,v=`[Tracing] Starting ${y?"sampled":"unsampled"} ${g?"root ":""}span`,T=[`op: ${m}`,`name: ${l}`,`ID: ${w}`];if(_&&T.push(`parent ID: ${_}`),!g){const{op:x,description:D}=q(E);T.push(`root ID: ${E.spanContext().spanId}`),x&&T.push(`root op: ${x}`),D&&T.push(`root description: ${D}`)}S.log(`${v}
  ${T.join(`
  `)}`)}(f),function(p,l,m){p&&(ee(p,fo,m),ee(p,po,l))}(f,d,h),f}({parentSpan:i,spanArguments:n,forceTransaction:r,scope:o})})}function $o(t,e){const n=Co();return n.withActiveSpan?n.withActiveSpan(t,e):wt(r=>(St(r,t||void 0),e(r)))}function Co(){return dt(Ge())}function Kr(t,e,n){var l;const r=j(),s=(r==null?void 0:r.getOptions())||{},{name:o=""}=t,i={spanAttributes:{...t.attributes},spanName:o,parentSampled:n};r==null||r.emit("beforeSampling",i,{decision:!1});const a=i.parentSampled??n,c=i.spanAttributes,u=e.getPropagationContext(),[d,h,f]=e.getScopeData().sdkProcessingMetadata[ko]?[!1]:function(m,_,w){if(!ke(m))return[!1];let y,E;typeof m.tracesSampler=="function"?(E=m.tracesSampler({..._,inheritOrSampleWith:T=>typeof _.parentSampleRate=="number"?_.parentSampleRate:typeof _.parentSampled=="boolean"?Number(_.parentSampled):T}),y=!0):_.parentSampled!==void 0?E=_.parentSampled:m.tracesSampleRate!==void 0&&(E=m.tracesSampleRate,y=!0);const g=xt(E);if(g===void 0)return $&&S.warn(`[Tracing] Discarding root span because of invalid sample rate. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(E)} of type ${JSON.stringify(typeof E)}.`),[!1];if(!g)return $&&S.log("[Tracing] Discarding transaction because "+(typeof m.tracesSampler=="function"?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0")),[!1,g,y];const v=w<g;return v||$&&S.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(E)})`),[v,g,y]}(s,{name:o,parentSampled:a,attributes:c,parentSampleRate:xt((l=u.dsc)==null?void 0:l.sample_rate)},u.sampleRand),p=new fn({...t,attributes:{[he]:"custom",[pr]:h!==void 0&&f?h:void 0,...c},sampled:d});return!d&&r&&($&&S.log("[Tracing] Discarding root span because its trace was not chosen to be sampled."),r.recordDroppedEvent("sample_rate","transaction")),r&&r.emit("spanStart",p),p}const Yt={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3},Xi="heartbeatFailed",Zi="idleTimeout",Qi="finalTimeout",ea="externalFinish";function Xr(t,e={}){const n=new Map;let r,s=!1,o=ea,i=!e.disableAutoFinish;const a=[],{idleTimeout:c=Yt.idleTimeout,finalTimeout:u=Yt.finalTimeout,childSpanTimeout:d=Yt.childSpanTimeout,beforeSpanEnd:h}=e,f=j();if(!f||!ke()){const g=new Ue;return Jt(g,{sample_rate:"0",sampled:"false",...$e(g)}),g}const p=U(),l=Z(),m=function(g){const v=Ot(g);return St(U(),v),$&&S.log("[Tracing] Started span is an idle span"),v}(t);function _(){r&&(clearTimeout(r),r=void 0)}function w(g){_(),r=setTimeout(()=>{!s&&n.size===0&&i&&(o=Zi,m.end(g))},c)}function y(g){r=setTimeout(()=>{!s&&i&&(o=Xi,m.end(g))},d)}function E(g){s=!0,n.clear(),a.forEach(b=>b()),St(p,l);const v=q(m),{start_timestamp:T}=v;if(!T)return;v.data[nn]||m.setAttribute(nn,o),S.log(`[Tracing] Idle span "${v.op}" finished`);const x=Wt(m).filter(b=>b!==m);let D=0;x.forEach(b=>{b.isRecording()&&(b.setStatus({code:z,message:"cancelled"}),b.end(g),$&&S.log("[Tracing] Cancelling span since span ended early",JSON.stringify(b,void 0,2)));const O=q(b),{timestamp:R=0,start_timestamp:k=0}=O,I=k<=g,P=R-k<=(u+c)/1e3;if($){const C=JSON.stringify(b,void 0,2);I?P||S.log("[Tracing] Discarding span since it finished after idle span final timeout",C):S.log("[Tracing] Discarding span since it happened after idle span was finished",C)}P&&I||(function(C,M){C[Fe]&&C[Fe].delete(M)}(m,b),D++)}),D>0&&m.setAttribute("sentry.idle_span_discarded_spans",D)}return m.end=new Proxy(m.end,{apply(g,v,T){if(h&&h(m),v instanceof Ue)return;const[x,...D]=T,b=Me(x||K()),O=Wt(m).filter(C=>C!==m);if(!O.length)return E(b),Reflect.apply(g,v,[b,...D]);const R=O.map(C=>q(C).timestamp).filter(C=>!!C),k=R.length?Math.max(...R):void 0,I=q(m).start_timestamp,P=Math.min(I?I+u/1e3:1/0,Math.max(I||-1/0,Math.min(b,k||1/0)));return E(P),Reflect.apply(g,v,[P,...D])}}),a.push(f.on("spanStart",g=>{if(!(s||g===m||q(g).timestamp)){var v;Wt(m).includes(g)&&(v=g.spanContext().spanId,_(),n.set(v,!0),y(K()+d/1e3))}})),a.push(f.on("spanEnd",g=>{var v;s||(v=g.spanContext().spanId,n.has(v)&&n.delete(v),n.size===0&&w(K()+c/1e3))})),a.push(f.on("idleSpanEnableAutoFinish",g=>{g===m&&(i=!0,w(),n.size&&y())})),e.disableAutoFinish||w(),setTimeout(()=>{s||(m.setStatus({code:z,message:"deadline_exceeded"}),o=Qi,m.end())},u),m}var pe;function He(t){return new Ce(e=>{e(t)})}function sn(t){return new Ce((e,n)=>{n(t)})}(function(t){t[t.PENDING=0]="PENDING",t[t.RESOLVED=1]="RESOLVED",t[t.REJECTED=2]="REJECTED"})(pe||(pe={}));class Ce{constructor(e){this._state=pe.PENDING,this._handlers=[],this._runExecutor(e)}then(e,n){return new Ce((r,s)=>{this._handlers.push([!1,o=>{if(e)try{r(e(o))}catch(i){s(i)}else r(o)},o=>{if(n)try{r(n(o))}catch(i){s(i)}else s(o)}]),this._executeHandlers()})}catch(e){return this.then(n=>n,e)}finally(e){return new Ce((n,r)=>{let s,o;return this.then(i=>{o=!1,s=i,e&&e()},i=>{o=!0,s=i,e&&e()}).then(()=>{o?r(s):n(s)})})}_executeHandlers(){if(this._state===pe.PENDING)return;const e=this._handlers.slice();this._handlers=[],e.forEach(n=>{n[0]||(this._state===pe.RESOLVED&&n[1](this._value),this._state===pe.REJECTED&&n[2](this._value),n[0]=!0)})}_runExecutor(e){const n=(o,i)=>{this._state===pe.PENDING&&(dn(i)?i.then(r,s):(this._state=o,this._value=i,this._executeHandlers()))},r=o=>{n(pe.RESOLVED,o)},s=o=>{n(pe.REJECTED,o)};try{e(r,s)}catch(o){s(o)}}}function Vn(t,e,n,r=0){return new Ce((s,o)=>{const i=t[r];if(e===null||typeof i!="function")s(e);else{const a=i({...e},n);$&&i.id&&a===null&&S.log(`Event processor "${i.id}" dropped event`),dn(a)?a.then(c=>Vn(t,c,n,r+1).then(s)).then(null,o):Vn(t,a,n,r+1).then(s).then(null,o)}})}let Nt,Zr,En;function ta(t,e){const{fingerprint:n,span:r,breadcrumbs:s,sdkProcessingMetadata:o}=e;(function(i,a){const{extra:c,tags:u,user:d,contexts:h,level:f,transactionName:p}=a;Object.keys(c).length&&(i.extra={...c,...i.extra}),Object.keys(u).length&&(i.tags={...u,...i.tags}),Object.keys(d).length&&(i.user={...d,...i.user}),Object.keys(h).length&&(i.contexts={...h,...i.contexts}),f&&(i.level=f),p&&i.type!=="transaction"&&(i.transaction=p)})(t,e),r&&function(i,a){i.contexts={trace:Fi(a),...i.contexts},i.sdkProcessingMetadata={dynamicSamplingContext:$e(a),...i.sdkProcessingMetadata};const c=V(a),u=q(c).description;u&&!i.transaction&&i.type==="transaction"&&(i.transaction=u)}(t,r),function(i,a){i.fingerprint=i.fingerprint?Array.isArray(i.fingerprint)?i.fingerprint:[i.fingerprint]:[],a&&(i.fingerprint=i.fingerprint.concat(a)),i.fingerprint.length||delete i.fingerprint}(t,n),function(i,a){const c=[...i.breadcrumbs||[],...a];i.breadcrumbs=c.length?c:void 0}(t,s),function(i,a){i.sdkProcessingMetadata={...i.sdkProcessingMetadata,...a}}(t,o)}function Qr(t,e){const{extra:n,tags:r,user:s,contexts:o,level:i,sdkProcessingMetadata:a,breadcrumbs:c,fingerprint:u,eventProcessors:d,attachments:h,propagationContext:f,transactionName:p,span:l}=e;Mt(t,"extra",n),Mt(t,"tags",r),Mt(t,"user",s),Mt(t,"contexts",o),t.sdkProcessingMetadata=Pt(t.sdkProcessingMetadata,a,2),i&&(t.level=i),p&&(t.transactionName=p),l&&(t.span=l),c.length&&(t.breadcrumbs=[...t.breadcrumbs,...c]),u.length&&(t.fingerprint=[...t.fingerprint,...u]),d.length&&(t.eventProcessors=[...t.eventProcessors,...d]),h.length&&(t.attachments=[...t.attachments,...h]),t.propagationContext={...t.propagationContext,...f}}function Mt(t,e,n){t[e]=Pt(t[e],n,1)}function na(t,e,n,r,s,o){const{normalizeDepth:i=3,normalizeMaxBreadth:a=1e3}=t,c={...e,event_id:e.event_id||n.event_id||oe(),timestamp:e.timestamp||It()},u=n.integrations||t.integrations.map(l=>l.name);(function(l,m){const{environment:_,release:w,dist:y,maxValueLength:E=250}=m;l.environment=l.environment||_||mr,!l.release&&w&&(l.release=w),!l.dist&&y&&(l.dist=y);const g=l.request;g!=null&&g.url&&(g.url=en(g.url,E))})(c,t),function(l,m){m.length>0&&(l.sdk=l.sdk||{},l.sdk.integrations=[...l.sdk.integrations||[],...m])}(c,u),s&&s.emit("applyFrameMetadata",e),e.type===void 0&&function(l,m){var w,y;const _=function(E){const g=N._sentryDebugIds;if(!g)return{};const v=Object.keys(g);return En&&v.length===Zr||(Zr=v.length,En=v.reduce((T,x)=>{Nt||(Nt={});const D=Nt[x];if(D)T[D[0]]=D[1];else{const b=E(x);for(let O=b.length-1;O>=0;O--){const R=b[O],k=R==null?void 0:R.filename,I=g[x];if(k&&I){T[k]=I,Nt[x]=[k,I];break}}}return T},{})),En}(m);(y=(w=l.exception)==null?void 0:w.values)==null||y.forEach(E=>{var g,v;(v=(g=E.stacktrace)==null?void 0:g.frames)==null||v.forEach(T=>{T.filename&&(T.debug_id=_[T.filename])})})}(c,t.stackParser);const d=function(l,m){if(!m)return l;const _=l?l.clone():new ve;return _.update(m),_}(r,n.captureContext);n.mechanism&&Qe(c,n.mechanism);const h=s?s.getEventProcessors():[],f=Zt("globalScope",()=>new ve).getScopeData();o&&Qr(f,o.getScopeData()),d&&Qr(f,d.getScopeData());const p=[...n.attachments||[],...f.attachments];return p.length&&(n.attachments=p),ta(c,f),Vn([...h,...f.eventProcessors],c,n).then(l=>(l&&function(m){var y,E;const _={};if((E=(y=m.exception)==null?void 0:y.values)==null||E.forEach(g=>{var v,T;(T=(v=g.stacktrace)==null?void 0:v.frames)==null||T.forEach(x=>{x.debug_id&&(x.abs_path?_[x.abs_path]=x.debug_id:x.filename&&(_[x.filename]=x.debug_id),delete x.debug_id)})}),Object.keys(_).length===0)return;m.debug_meta=m.debug_meta||{},m.debug_meta.images=m.debug_meta.images||[];const w=m.debug_meta.images;Object.entries(_).forEach(([g,v])=>{w.push({type:"sourcemap",code_file:g,debug_id:v})})}(l),typeof i=="number"&&i>0?function(m,_,w){var E,g;if(!m)return null;const y={...m,...m.breadcrumbs&&{breadcrumbs:m.breadcrumbs.map(v=>({...v,...v.data&&{data:le(v.data,_,w)}}))},...m.user&&{user:le(m.user,_,w)},...m.contexts&&{contexts:le(m.contexts,_,w)},...m.extra&&{extra:le(m.extra,_,w)}};return(E=m.contexts)!=null&&E.trace&&y.contexts&&(y.contexts.trace=m.contexts.trace,m.contexts.trace.data&&(y.contexts.trace.data=le(m.contexts.trace.data,_,w))),m.spans&&(y.spans=m.spans.map(v=>({...v,...v.data&&{data:le(v.data,_,w)}}))),(g=m.contexts)!=null&&g.flags&&y.contexts&&(y.contexts.flags=le(m.contexts.flags,3,w)),y}(l,i,a):l))}function Kn(t,e){return U().captureException(t,void 0)}function es(t,e){return U().captureEvent(t,e)}function ts(t){const e=Ie(),n=U(),{userAgent:r}=N.navigator||{},s=$i({user:n.getUser()||e.getUser(),...r&&{userAgent:r},...t}),o=e.getSession();return(o==null?void 0:o.status)==="ok"&&et(o,{status:"exited"}),Io(),e.setSession(s),s}function Io(){const t=Ie(),e=U().getSession()||t.getSession();e&&function(n,r){let s={};n.status==="ok"&&(s={status:"exited"}),et(n,s)}(e),Po(),t.setSession()}function Po(){const t=Ie(),e=j(),n=t.getSession();n&&e&&e.captureSession(n)}function ns(t=!1){t?Io():Po()}const ra="7";function sa(t,e,n){return e||`${function(r){return`${function(s){const o=s.protocol?`${s.protocol}:`:"",i=s.port?`:${s.port}`:"";return`${o}//${s.host}${i}${s.path?`/${s.path}`:""}/api/`}(r)}${r.projectId}/envelope/`}(t)}?${function(r,s){const o={sentry_version:ra};return r.publicKey&&(o.sentry_key=r.publicKey),s&&(o.sentry_client=`${s.name}/${s.version}`),new URLSearchParams(o).toString()}(t,n)}`}const rs=[];function oa(t){const e=t.defaultIntegrations||[],n=t.integrations;let r;if(e.forEach(s=>{s.isDefaultInstance=!0}),Array.isArray(n))r=[...e,...n];else if(typeof n=="function"){const s=n(e);r=Array.isArray(s)?s:[s]}else r=e;return function(s){const o={};return s.forEach(i=>{const{name:a}=i,c=o[a];c&&!c.isDefaultInstance&&i.isDefaultInstance||(o[a]=i)}),Object.values(o)}(r)}function ss(t,e){for(const n of e)n!=null&&n.afterAllSetup&&n.afterAllSetup(t)}function os(t,e,n){if(n[e.name])$&&S.log(`Integration skipped because it was already installed: ${e.name}`);else{if(n[e.name]=e,rs.indexOf(e.name)===-1&&typeof e.setupOnce=="function"&&(e.setupOnce(),rs.push(e.name)),e.setup&&typeof e.setup=="function"&&e.setup(t),typeof e.preprocessEvent=="function"){const r=e.preprocessEvent.bind(e);t.on("preprocessEvent",(s,o)=>r(s,o,t))}if(typeof e.processEvent=="function"){const r=e.processEvent.bind(e),s=Object.assign((o,i)=>r(o,i,t),{id:e.name});t.addEventProcessor(s)}$&&S.log(`Integration installed: ${e.name}`)}}function Ao(t){const e=[];t.message&&e.push(t.message);try{const n=t.exception.values[t.exception.values.length-1];n!=null&&n.value&&(e.push(n.value),n.type&&e.push(`${n.type}: ${n.value}`))}catch{}return e}const is="Not capturing exception because it's already been captured.",as="Discarded session because of missing or non-string release",Oo=Symbol.for("SentryInternalError"),Ro=Symbol.for("SentryDoNotSendEventError");function jt(t){return{message:t,[Oo]:!0}}function xn(t){return{message:t,[Ro]:!0}}function cs(t){return!!t&&typeof t=="object"&&Oo in t}function us(t){return!!t&&typeof t=="object"&&Ro in t}class ia{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=Hi(e.dsn):$&&S.warn("No DSN provided, client will not send events."),this._dsn){const n=sa(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:n})}}captureException(e,n,r){const s=oe();if(Ir(e))return $&&S.log(is),s;const o={event_id:s,...n};return this._process(this.eventFromException(e,o).then(i=>this._captureEvent(i,o,r))),o.event_id}captureMessage(e,n,r,s){const o={event_id:oe(),...r},i=ur(e)?e:String(e),a=vt(e)?this.eventFromMessage(i,n,o):this.eventFromException(e,o);return this._process(a.then(c=>this._captureEvent(c,o,s))),o.event_id}captureEvent(e,n,r){const s=oe();if(n!=null&&n.originalException&&Ir(n.originalException))return $&&S.log(is),s;const o={event_id:s,...n},i=e.sdkProcessingMetadata||{},a=i.capturedSpanScope,c=i.capturedSpanIsolationScope;return this._process(this._captureEvent(e,o,a||r,c)),o.event_id}captureSession(e){this.sendSession(e),et(e,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(e).then(r=>n.flush(e).then(s=>r&&s))):He(!0)}close(e){return this.flush(e).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){const n=this._integrations[e.name];os(this,e,this._integrations),n||ss(this,[e])}sendEvent(e,n={}){this.emit("beforeSendEvent",e,n);let r=Ki(e,this._dsn,this._options._metadata,this._options.tunnel);for(const o of n.attachments||[])r=Wi(r,Yi(o));const s=this.sendEnvelope(r);s&&s.then(o=>this.emit("afterSendEvent",e,o),null)}sendSession(e){const{release:n,environment:r=mr}=this._options;if("aggregates"in e){const o=e.attrs||{};if(!o.release&&!n)return void($&&S.warn(as));o.release=o.release||n,o.environment=o.environment||r,e.attrs=o}else{if(!e.release&&!n)return void($&&S.warn(as));e.release=e.release||n,e.environment=e.environment||r}this.emit("beforeSendSession",e);const s=function(o,i,a,c){const u=To(a);return nt({sent_at:new Date().toISOString(),...u&&{sdk:u},...!!c&&i&&{dsn:Tt(i)}},["aggregates"in o?[{type:"sessions"},o]:[{type:"session"},o.toJSON()]])}(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(s)}recordDroppedEvent(e,n,r=1){if(this._options.sendClientReports){const s=`${e}:${n}`;$&&S.log(`Recording outcome: "${s}"${r>1?` (${r} times)`:""}`),this._outcomes[s]=(this._outcomes[s]||0)+r}}on(e,n){const r=this._hooks[e]=this._hooks[e]||[];return r.push(n),()=>{const s=r.indexOf(n);s>-1&&r.splice(s,1)}}emit(e,...n){const r=this._hooks[e];r&&r.forEach(s=>s(...n))}sendEnvelope(e){return this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport?this._transport.send(e).then(null,n=>($&&S.error("Error while sending envelope:",n),n)):($&&S.error("Transport disabled"),He({}))}_setupIntegrations(){const{integrations:e}=this._options;this._integrations=function(n,r){const s={};return r.forEach(o=>{o&&os(n,o,s)}),s}(this,e),ss(this,e)}_updateSessionFromEvent(e,n){var a;let r=n.level==="fatal",s=!1;const o=(a=n.exception)==null?void 0:a.values;if(o){s=!0;for(const c of o){const u=c.mechanism;if((u==null?void 0:u.handled)===!1){r=!0;break}}}const i=e.status==="ok";(i&&e.errors===0||i&&r)&&(et(e,{...r&&{status:"crashed"},errors:e.errors||Number(s||r)}),this.captureSession(e))}_isClientDoneProcessing(e){return new Ce(n=>{let r=0;const s=setInterval(()=>{this._numProcessing==0?(clearInterval(s),n(!0)):(r+=1,e&&r>=e&&(clearInterval(s),n(!1)))},1)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(e,n,r,s){const o=this.getOptions(),i=Object.keys(this._integrations);return!n.integrations&&(i!=null&&i.length)&&(n.integrations=i),this.emit("preprocessEvent",e,n),e.type||s.setLastEventId(e.event_id||n.event_id),na(o,e,n,r,this,s).then(a=>{if(a===null)return a;this.emit("postprocessEvent",a,n),a.contexts={trace:Ai(r),...a.contexts};const c=Eo(this,r);return a.sdkProcessingMetadata={dynamicSamplingContext:c,...a.sdkProcessingMetadata},a})}_captureEvent(e,n={},r=U(),s=Ie()){return $&&Tn(e)&&S.log(`Captured error event \`${Ao(e)[0]||"<unknown>"}\``),this._processEvent(e,n,r,s).then(o=>o.event_id,o=>{$&&(us(o)?S.log(o.message):cs(o)?S.warn(o.message):S.warn(o))})}_processEvent(e,n,r,s){const o=this.getOptions(),{sampleRate:i}=o,a=ds(e),c=Tn(e),u=e.type||"error",d=`before send for type \`${u}\``,h=i===void 0?void 0:xt(i);if(c&&typeof h=="number"&&Math.random()>h)return this.recordDroppedEvent("sample_rate","error"),sn(xn(`Discarding event because it's not included in the random sample (sampling rate = ${i})`));const f=u==="replay_event"?"replay":u;return this._prepareEvent(e,n,r,s).then(p=>{if(p===null)throw this.recordDroppedEvent("event_processor",f),xn("An event processor returned `null`, will not send event.");if(n.data&&n.data.__sentry__===!0)return p;const l=function(m,_,w,y){const{beforeSend:E,beforeSendTransaction:g,beforeSendSpan:v}=_;let T=w;if(Tn(T)&&E)return E(T,y);if(ds(T)){if(v){const D=v(function(b){var W;const{trace_id:O,parent_span_id:R,span_id:k,status:I,origin:P,data:C,op:M}=((W=b.contexts)==null?void 0:W.trace)??{};return{data:C??{},description:b.transaction,op:M,parent_span_id:R,span_id:k??"",start_timestamp:b.start_timestamp??0,status:I,timestamp:b.timestamp,trace_id:O??"",origin:P,profile_id:C==null?void 0:C[zn],exclusive_time:C==null?void 0:C[Et],measurements:b.measurements,is_segment:!0}}(T));if(D?T=Pt(w,{type:"transaction",timestamp:(x=D).timestamp,start_timestamp:x.start_timestamp,transaction:x.description,contexts:{trace:{trace_id:x.trace_id,span_id:x.span_id,parent_span_id:x.parent_span_id,op:x.op,status:x.status,origin:x.origin,data:{...x.data,...x.profile_id&&{[zn]:x.profile_id},...x.exclusive_time&&{[Et]:x.exclusive_time}}}},measurements:x.measurements}):Wn(),T.spans){const b=[];for(const O of T.spans){const R=v(O);R?b.push(R):(Wn(),b.push(O))}T.spans=b}}if(g){if(T.spans){const D=T.spans.length;T.sdkProcessingMetadata={...w.sdkProcessingMetadata,spanCountBeforeProcessing:D}}return g(T,y)}}var x;return T}(0,o,p,n);return function(m,_){const w=`${_} must return \`null\` or a valid event.`;if(dn(m))return m.then(y=>{if(!bt(y)&&y!==null)throw jt(w);return y},y=>{throw jt(`${_} rejected with ${y}`)});if(!bt(m)&&m!==null)throw jt(w);return m}(l,d)}).then(p=>{var _;if(p===null){if(this.recordDroppedEvent("before_send",f),a){const w=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",w)}throw xn(`${d} returned \`null\`, will not send event.`)}const l=r.getSession()||s.getSession();if(c&&l&&this._updateSessionFromEvent(l,p),a){const w=(((_=p.sdkProcessingMetadata)==null?void 0:_.spanCountBeforeProcessing)||0)-(p.spans?p.spans.length:0);w>0&&this.recordDroppedEvent("before_send","span",w)}const m=p.transaction_info;if(a&&m&&p.transaction!==e.transaction){const w="custom";p.transaction_info={...m,source:w}}return this.sendEvent(p,n),p}).then(null,p=>{throw us(p)||cs(p)?p:(this.captureException(p,{data:{__sentry__:!0},originalException:p}),jt(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${p}`))})}_process(e){this._numProcessing++,e.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const e=this._outcomes;return this._outcomes={},Object.entries(e).map(([n,r])=>{const[s,o]=n.split(":");return{reason:s,category:o,quantity:r}})}_flushOutcomes(){$&&S.log("Flushing outcomes...");const e=this._clearOutcomes();if(e.length===0)return void($&&S.log("No outcomes to send"));if(!this._dsn)return void($&&S.log("No dsn provided, will not send outcomes"));$&&S.log("Sending outcomes:",e);const n=(r=e,nt((s=this._options.tunnel&&Tt(this._dsn))?{dsn:s}:{},[[{type:"client_report"},{timestamp:It(),discarded_events:r}]]));var r,s;this.sendEnvelope(n)}}function Tn(t){return t.type===void 0}function ds(t){return t.type==="transaction"}function kn(t,e){var o;const n=function(i){var a;return(a=N._sentryClientToLogBufferMap)==null?void 0:a.get(i)}(t)??[];if(n.length===0)return;const r=t.getOptions(),s=function(i,a,c,u){const d={};return a!=null&&a.sdk&&(d.sdk={name:a.sdk.name,version:a.sdk.version}),c&&u&&(d.dsn=Tt(u)),nt(d,[(h=i,[{type:"log",item_count:h.length,content_type:"application/vnd.sentry.items.log+json"},{items:h}])]);var h}(n,r._metadata,r.tunnel,t.getDsn());(o=N._sentryClientToLogBufferMap)==null||o.set(t,[]),t.emit("flushLogs"),t.sendEnvelope(s)}function aa(t,e){e.debug===!0&&($?S.enable():Je(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),U().update(e.initialScope);const n=new t(e);return function(r){U().setClient(r)}(n),n.init(),n}N._sentryClientToLogBufferMap=new WeakMap;const Do=Symbol.for("SentryBufferFullError");function ca(t){const e=[];function n(r){return e.splice(e.indexOf(r),1)[0]||Promise.resolve(void 0)}return{$:e,add:function(r){if(!(t===void 0||e.length<t))return sn(Do);const s=r();return e.indexOf(s)===-1&&e.push(s),s.then(()=>n(s)).then(null,()=>n(s).then(null,()=>{})),s},drain:function(r){return new Ce((s,o)=>{let i=e.length;if(!i)return s(!0);const a=setTimeout(()=>{r&&r>0&&s(!1)},r);e.forEach(c=>{He(c).then(()=>{--i||(clearTimeout(a),s(!0))},o)})})}}}const ua=6e4;function da(t,{statusCode:e,headers:n},r=Date.now()){const s={...t},o=n==null?void 0:n["x-sentry-rate-limits"],i=n==null?void 0:n["retry-after"];if(o)for(const a of o.trim().split(",")){const[c,u,,,d]=a.split(":",5),h=parseInt(c,10),f=1e3*(isNaN(h)?60:h);if(u)for(const p of u.split(";"))p==="metric_bucket"&&d&&!d.split(";").includes("custom")||(s[p]=r+f);else s.all=r+f}else i?s.all=r+function(a,c=Date.now()){const u=parseInt(`${a}`,10);if(!isNaN(u))return 1e3*u;const d=Date.parse(`${a}`);return isNaN(d)?ua:d-c}(i,r):e===429&&(s.all=r+6e4);return s}const la=64;function pa(t,e,n=ca(t.bufferSize||la)){let r={};return{send:function(s){const o=[];if(Wr(s,(c,u)=>{const d=Gr(u);(function(h,f,p=Date.now()){return function(l,m){return l[m]||l.all||0}(h,f)>p})(r,d)?t.recordDroppedEvent("ratelimit_backoff",d):o.push(c)}),o.length===0)return He({});const i=nt(s[0],o),a=c=>{Wr(i,(u,d)=>{t.recordDroppedEvent(c,Gr(d))})};return n.add(()=>e({body:Gi(i)}).then(c=>(c.statusCode!==void 0&&(c.statusCode<200||c.statusCode>=300)&&$&&S.warn(`Sentry responded with status code ${c.statusCode} to sent event.`),r=da(r,c),c),c=>{throw a("network_error"),$&&S.error("Encountered error running transport request:",c),c})).then(c=>c,c=>{if(c===Do)return $&&S.error("Skipped sending event because buffer is full."),a("queue_overflow"),He({});throw c})},flush:s=>n.drain(s)}}function fa(t){var e;((e=t.user)==null?void 0:e.ip_address)===void 0&&(t.user={...t.user,ip_address:"{{auto}}"})}function ha(t){var e;"aggregates"in t?((e=t.attrs)==null?void 0:e.ip_address)===void 0&&(t.attrs={...t.attrs,ip_address:"{{auto}}"}):t.ipAddress===void 0&&(t.ipAddress="{{auto}}")}function Lo(t,e,n=[e],r="npm"){const s=t._metadata||{};s.sdk||(s.sdk={name:`sentry.javascript.${e}`,packages:n.map(o=>({name:`${r}:@sentry/${o}`,version:Ne})),version:Ne}),t._metadata=s}function No(t={}){const e=j();if(!function(){const a=j();return(a==null?void 0:a.getOptions().enabled)!==!1&&!!(a!=null&&a.getTransport())}()||!e)return{};const n=dt(Ge());if(n.getTraceData)return n.getTraceData(t);const r=U(),s=t.span||Z(),o=s?function(a){const{traceId:c,spanId:u}=a.spanContext();return Rr(c,u,je(a))}(s):function(a){const{traceId:c,sampled:u,propagationSpanId:d}=a.getPropagationContext();return Rr(c,d,u)}(r),i=Ni(s?$e(s):Eo(e,r));return mo.test(o)?{"sentry-trace":o,baggage:i}:(S.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}const ma=100;function Ae(t,e){const n=j(),r=Ie();if(!n)return;const{beforeBreadcrumb:s=null,maxBreadcrumbs:o=ma}=n.getOptions();if(o<=0)return;const i={timestamp:It(),...t},a=s?Je(()=>s(i,e)):i;a!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",a,e),r.addBreadcrumb(a,o))}let ls;const ps=new WeakMap,ga=()=>({name:"FunctionToString",setupOnce(){ls=Function.prototype.toString;try{Function.prototype.toString=function(...t){const e=lr(this),n=ps.has(j())&&e!==void 0?e:this;return ls.apply(n,t)}}catch{}},setup(t){ps.set(t,!0)}}),_a=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],ya=(t={})=>{let e;return{name:"EventFilters",setup(n){const r=n.getOptions();e=fs(t,r)},processEvent(n,r,s){if(!e){const o=s.getOptions();e=fs(t,o)}return function(o,i){if(o.type){if(o.type==="transaction"&&function(a,c){if(!(c!=null&&c.length))return!1;const u=a.transaction;return!!u&&De(u,c)}(o,i.ignoreTransactions))return $&&S.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${Oe(o)}`),!0}else{if(function(a,c){return c!=null&&c.length?Ao(a).some(u=>De(u,c)):!1}(o,i.ignoreErrors))return $&&S.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${Oe(o)}`),!0;if(function(a){var c,u;return(u=(c=a.exception)==null?void 0:c.values)!=null&&u.length?!a.message&&!a.exception.values.some(d=>d.stacktrace||d.type&&d.type!=="Error"||d.value):!1}(o))return $&&S.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${Oe(o)}`),!0;if(function(a,c){if(!(c!=null&&c.length))return!1;const u=Ft(a);return!!u&&De(u,c)}(o,i.denyUrls))return $&&S.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${Oe(o)}.
Url: ${Ft(o)}`),!0;if(!function(a,c){if(!(c!=null&&c.length))return!0;const u=Ft(a);return!u||De(u,c)}(o,i.allowUrls))return $&&S.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${Oe(o)}.
Url: ${Ft(o)}`),!0}return!1}(n,e)?null:n}}},va=(t={})=>({...ya(t),name:"InboundFilters"});function fs(t={},e={}){return{allowUrls:[...t.allowUrls||[],...e.allowUrls||[]],denyUrls:[...t.denyUrls||[],...e.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...e.ignoreErrors||[],...t.disableErrorDefaults?[]:_a],ignoreTransactions:[...t.ignoreTransactions||[],...e.ignoreTransactions||[]]}}function Ft(t){var e,n;try{const r=[...((e=t.exception)==null?void 0:e.values)??[]].reverse().find(o=>{var i,a,c;return((i=o.mechanism)==null?void 0:i.parent_id)===void 0&&((c=(a=o.stacktrace)==null?void 0:a.frames)==null?void 0:c.length)}),s=(n=r==null?void 0:r.stacktrace)==null?void 0:n.frames;return s?function(o=[]){for(let i=o.length-1;i>=0;i--){const a=o[i];if(a&&a.filename!=="<anonymous>"&&a.filename!=="[native code]")return a.filename||null}return null}(s):null}catch{return $&&S.error(`Cannot extract url for event ${Oe(t)}`),null}}function ba(t,e,n,r,s,o){var a;if(!((a=s.exception)!=null&&a.values)||!o||!ye(o.originalException,Error))return;const i=s.exception.values.length>0?s.exception.values[s.exception.values.length-1]:void 0;i&&(s.exception.values=Xn(t,e,r,o.originalException,n,s.exception.values,i,0))}function Xn(t,e,n,r,s,o,i,a){if(o.length>=n+1)return o;let c=[...o];if(ye(r[s],Error)){hs(i,a);const u=t(e,r[s]),d=c.length;ms(u,s,d,a),c=Xn(t,e,n,r[s],s,[u,...c],u,d)}return Array.isArray(r.errors)&&r.errors.forEach((u,d)=>{if(ye(u,Error)){hs(i,a);const h=t(e,u),f=c.length;ms(h,`errors[${d}]`,f,a),c=Xn(t,e,n,u,s,[h,...c],h,f)}}),c}function hs(t,e){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,...t.type==="AggregateError"&&{is_exception_group:!0},exception_id:e}}function ms(t,e,n,r){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,type:"chained",source:e,exception_id:n,parent_id:r}}function Sa(){"console"in N&&qn.forEach(function(t){t in N.console&&Q(N.console,t,function(e){return Qt[t]=e,function(...n){ie("console",{args:n,level:t});const r=Qt[t];r==null||r.apply(N.console,n)}})})}function wa(t){return t==="warn"?"warning":["fatal","error","warning","log","info","debug"].includes(t)?t:"log"}const Ea=()=>{let t;return{name:"Dedupe",processEvent(e){if(e.type)return e;try{if(function(n,r){return r?!!(function(s,o){const i=s.message,a=o.message;return!(!i&&!a||i&&!a||!i&&a||i!==a||!_s(s,o)||!gs(s,o))}(n,r)||function(s,o){const i=ys(o),a=ys(s);return!(!i||!a||i.type!==a.type||i.value!==a.value||!_s(s,o)||!gs(s,o))}(n,r)):!1}(e,t))return $&&S.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return t=e}}};function gs(t,e){let n=Ur(t),r=Ur(e);if(!n&&!r)return!0;if(n&&!r||!n&&r||r.length!==n.length)return!1;for(let s=0;s<r.length;s++){const o=r[s],i=n[s];if(o.filename!==i.filename||o.lineno!==i.lineno||o.colno!==i.colno||o.function!==i.function)return!1}return!0}function _s(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return n.join("")===r.join("")}catch{return!1}}function ys(t){var e;return((e=t.exception)==null?void 0:e.values)&&t.exception.values[0]}const xa="thismessage:/";function Mo(t){return"isRelative"in t}function jo(t,e){const n=t.indexOf("://")<=0&&t.indexOf("//")!==0,r=n?xa:void 0;try{if("canParse"in URL&&!URL.canParse(t,r))return;const s=new URL(t,r);return n?{isRelative:n,pathname:s.pathname,search:s.search,hash:s.hash}:s}catch{}}function Ta(t){if(Mo(t))return t.pathname;const e=new URL(t);return e.search="",e.hash="",["80","443"].includes(e.port)&&(e.port=""),e.password&&(e.password="%filtered%"),e.username&&(e.username="%filtered%"),e.toString()}function Ke(t){if(!t)return{};const e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};const n=e[6]||"",r=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],search:n,hash:r,relative:e[5]+n+r}}function ka(t,e,n,r,s="auto.http.browser"){if(!t.fetchData)return;const{method:o,url:i}=t.fetchData,a=ke()&&e(i);if(t.endTimestamp&&a){const h=t.fetchData.__span;if(!h)return;const f=r[h];return void(f&&(function(p,l){var m;if(l.response){lo(p,l.response.status);const _=((m=l.response)==null?void 0:m.headers)&&l.response.headers.get("content-length");if(_){const w=parseInt(_);w>0&&p.setAttribute("http.response_content_length",w)}}else l.error&&p.setStatus({code:z,message:"internal_error"});p.end()}(f,t),delete r[h]))}const c=!!Z(),u=a&&c?Ot(function(h,f,p){const l=jo(h);return{name:l?`${f} ${Ta(l)}`:f,attributes:$a(h,l,f,p)}}(i,o,s)):new Ue;if(t.fetchData.__span=u.spanContext().spanId,r[u.spanContext().spanId]=u,n(t.fetchData.url)){const h=t.args[0],f=t.args[1]||{},p=function(l,m,_){const w=No({span:_}),y=w["sentry-trace"],E=w.baggage;if(!y)return;const g=m.headers||(no(l)?l.headers:void 0);if(g){if(function(v){return typeof Headers<"u"&&ye(v,Headers)}(g)){const v=new Headers(g);if(v.get("sentry-trace")||v.set("sentry-trace",y),E){const T=v.get("baggage");T?qt(T)||v.set("baggage",`${T},${E}`):v.set("baggage",E)}return v}if(Array.isArray(g)){const v=[...g];g.find(x=>x[0]==="sentry-trace")||v.push(["sentry-trace",y]);const T=g.find(x=>x[0]==="baggage"&&qt(x[1]));return E&&!T&&v.push(["baggage",E]),v}{const v="sentry-trace"in g?g["sentry-trace"]:void 0,T="baggage"in g?g.baggage:void 0,x=T?Array.isArray(T)?[...T]:[T]:[],D=T&&(Array.isArray(T)?T.find(b=>qt(b)):qt(T));return E&&!D&&x.push(E),{...g,"sentry-trace":v??y,baggage:x.length>0?x.join(","):void 0}}}return{...w}}(h,f,ke()&&c?u:void 0);p&&(t.args[1]=f,f.headers=p)}const d=j();if(d){const h={input:t.args,response:t.response,startTimestamp:t.startTimestamp,endTimestamp:t.endTimestamp};d.emit("beforeOutgoingRequestSpan",u,h)}return u}function qt(t){return t.split(",").some(e=>e.trim().startsWith(fr))}function $a(t,e,n,r){const s={url:t,type:"fetch","http.method":n,[H]:r,[ze]:"http.client"};return e&&(Mo(e)||(s["http.url"]=e.href,s["server.address"]=e.host),e.search&&(s["http.query"]=e.search),e.hash&&(s["http.fragment"]=e.hash)),s}function vs(t){return t===void 0?void 0:t>=400&&t<500?"warning":t>=500?"error":void 0}const kt=N;function Fo(){if(!("fetch"in kt))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function Zn(t){return t&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function qo(t,e){const n="fetch";xe(n,t),Te(n,()=>Uo(void 0,e))}function Uo(t,e=!1){e&&!function(){var s;if(typeof EdgeRuntime=="string")return!0;if(!Fo())return!1;if(Zn(kt.fetch))return!0;let n=!1;const r=kt.document;if(r&&typeof r.createElement=="function")try{const o=r.createElement("iframe");o.hidden=!0,r.head.appendChild(o),(s=o.contentWindow)!=null&&s.fetch&&(n=Zn(o.contentWindow.fetch)),r.head.removeChild(o)}catch(o){$&&S.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",o)}return n}()||Q(N,"fetch",function(n){return function(...r){const s=new Error,{method:o,url:i}=function(c){if(c.length===0)return{method:"GET",url:""};if(c.length===2){const[d,h]=c;return{url:bs(d),method:Qn(h,"method")?String(h.method).toUpperCase():"GET"}}const u=c[0];return{url:bs(u),method:Qn(u,"method")?String(u.method).toUpperCase():"GET"}}(r),a={args:r,fetchData:{method:o,url:i},startTimestamp:1e3*K(),virtualError:s,headers:Ia(r)};return t||ie("fetch",{...a}),n.apply(N,r).then(async c=>(t?t(c):ie("fetch",{...a,endTimestamp:1e3*K(),response:c}),c),c=>{if(ie("fetch",{...a,endTimestamp:1e3*K(),error:c}),cr(c)&&c.stack===void 0&&(c.stack=s.stack,ee(c,"framesToPop",1)),c instanceof TypeError&&(c.message==="Failed to fetch"||c.message==="Load failed"||c.message==="NetworkError when attempting to fetch resource."))try{const u=new URL(a.fetchData.url);c.message=`${c.message} (${u.host})`}catch{}throw c})}})}function Ca(t){let e;try{e=t.clone()}catch{return}(async function(n,r){if(n!=null&&n.body){const s=n.body,o=s.getReader(),i=setTimeout(()=>{s.cancel().then(null,()=>{})},9e4);let a=!0;for(;a;){let c;try{c=setTimeout(()=>{s.cancel().then(null,()=>{})},5e3);const{done:u}=await o.read();clearTimeout(c),u&&(r(),a=!1)}catch{a=!1}finally{clearTimeout(c)}}clearTimeout(i),o.releaseLock(),s.cancel().then(null,()=>{})}})(e,()=>{ie("fetch-body-resolved",{endTimestamp:1e3*K(),response:t})})}function Qn(t,e){return!!t&&typeof t=="object"&&!!t[e]}function bs(t){return typeof t=="string"?t:t?Qn(t,"url")?t.url:t.toString?t.toString():"":""}function Ia(t){const[e,n]=t;try{if(typeof n=="object"&&n!==null&&"headers"in n&&n.headers)return new Headers(n.headers);if(no(e))return new Headers(e.headers)}catch{}}const L=N;let er=0;function Ss(){return er>0}function rt(t,e={}){if(!function(r){return typeof r=="function"}(t))return t;try{const r=t.__sentry_wrapped__;if(r)return typeof r=="function"?r:t;if(lr(t))return t}catch{return t}const n=function(...r){try{const s=r.map(o=>rt(o,e));return t.apply(this,s)}catch(s){throw er++,setTimeout(()=>{er--}),wt(o=>{o.addEventProcessor(i=>(e.mechanism&&(Un(i,void 0),Qe(i,e.mechanism)),i.extra={...i.extra,arguments:r},i)),Kn(s)}),s}};try{for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}catch{}so(n,t),ee(t,"__sentry_wrapped__",n);try{Object.getOwnPropertyDescriptor(n,"name").configurable&&Object.defineProperty(n,"name",{get:()=>t.name})}catch{}return n}function tr(){const t=Ct(),{referrer:e}=L.document||{},{userAgent:n}=L.navigator||{};return{url:t,headers:{...e&&{Referer:e},...n&&{"User-Agent":n}}}}function gr(t,e){const n=_r(t,e),r={type:Oa(e),value:Ra(e)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function Pa(t,e,n,r){const s=j(),o=s==null?void 0:s.getOptions().normalizeDepth,i=function(u){for(const d in u)if(Object.prototype.hasOwnProperty.call(u,d)){const h=u[d];if(h instanceof Error)return h}}(e),a={__serialized__:xo(e,o)};if(i)return{exception:{values:[gr(t,i)]},extra:a};const c={exception:{values:[{type:un(e)?e.constructor.name:r?"UnhandledRejection":"Error",value:Da(e,{isUnhandledRejection:r})}]},extra:a};if(n){const u=_r(t,n);u.length&&(c.exception.values[0].stacktrace={frames:u})}return c}function $n(t,e){return{exception:{values:[gr(t,e)]}}}function _r(t,e){const n=e.stacktrace||e.stack||"",r=function(o){return o&&Aa.test(o.message)?1:0}(e),s=function(o){return typeof o.framesToPop=="number"?o.framesToPop:0}(e);try{return t(n,r,s)}catch{}return[]}const Aa=/Minified React error #\d+;/i;function Bo(t){return typeof WebAssembly<"u"&&WebAssembly.Exception!==void 0&&t instanceof WebAssembly.Exception}function Oa(t){const e=t==null?void 0:t.name;return!e&&Bo(t)?t.message&&Array.isArray(t.message)&&t.message.length==2?t.message[0]:"WebAssembly.Exception":e}function Ra(t){const e=t==null?void 0:t.message;return Bo(t)?Array.isArray(t.message)&&t.message.length==2?t.message[1]:"wasm exception":e?e.error&&typeof e.error.message=="string"?e.error.message:e:"No error message"}function nr(t,e,n,r,s){let o;if(eo(e)&&e.error)return $n(t,e.error);if(Tr(e)||ut(e,"DOMException")){const i=e;if("stack"in e)o=$n(t,e);else{const a=i.name||(Tr(i)?"DOMError":"DOMException"),c=i.message?`${a}: ${i.message}`:a;o=rr(t,c,n,r),Un(o,c)}return"code"in i&&(o.tags={...o.tags,"DOMException.code":`${i.code}`}),o}return cr(e)?$n(t,e):bt(e)||un(e)?(o=Pa(t,e,n,s),Qe(o,{synthetic:!0}),o):(o=rr(t,e,n,r),Un(o,`${e}`),Qe(o,{synthetic:!0}),o)}function rr(t,e,n,r){const s={};if(r&&n){const o=_r(t,n);o.length&&(s.exception={values:[{value:e,stacktrace:{frames:o}}]}),Qe(s,{synthetic:!0})}if(ur(e)){const{__sentry_template_string__:o,__sentry_template_values__:i}=e;return s.logentry={message:o,params:i},s}return s.message=e,s}function Da(t,{isUnhandledRejection:e}){const n=function(s,o=40){const i=Object.keys(oo(s));i.sort();const a=i[0];if(!a)return"[object has no keys]";if(a.length>=o)return en(a,o);for(let c=i.length;c>0;c--){const u=i.slice(0,c).join(", ");if(!(u.length>o))return c===i.length?u:en(u,o)}return""}(t),r=e?"promise rejection":"exception";return eo(t)?`Event \`ErrorEvent\` captured as ${r} with message \`${t.message}\``:un(t)?`Event \`${function(s){try{const o=Object.getPrototypeOf(s);return o?o.constructor.name:void 0}catch{}}(t)}\` (type=${t.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}class La extends ia{constructor(e){const n={parentSpanIsAlwaysRootSpan:!0,...e};Lo(n,"browser",["browser"],L.SENTRY_SDK_SOURCE||"npm"),super(n);const r=this,{sendDefaultPii:s,_experiments:o}=r._options,i=o==null?void 0:o.enableLogs;n.sendClientReports&&L.document&&L.document.addEventListener("visibilitychange",()=>{L.document.visibilityState==="hidden"&&(this._flushOutcomes(),i&&kn(r))}),i&&(r.on("flush",()=>{kn(r)}),r.on("afterCaptureLog",()=>{r._logFlushIdleTimeout&&clearTimeout(r._logFlushIdleTimeout),r._logFlushIdleTimeout=setTimeout(()=>{kn(r)},5e3)})),s&&(r.on("postprocessEvent",fa),r.on("beforeSendSession",ha))}eventFromException(e,n){return function(r,s,o,i){const a=nr(r,s,(o==null?void 0:o.syntheticException)||void 0,i);return Qe(a),a.level="error",o!=null&&o.event_id&&(a.event_id=o.event_id),He(a)}(this._options.stackParser,e,n,this._options.attachStacktrace)}eventFromMessage(e,n="info",r){return function(s,o,i="info",a,c){const u=rr(s,o,(a==null?void 0:a.syntheticException)||void 0,c);return u.level=i,a!=null&&a.event_id&&(u.event_id=a.event_id),He(u)}(this._options.stackParser,e,n,r,this._options.attachStacktrace)}_prepareEvent(e,n,r,s){return e.platform=e.platform||"javascript",super._prepareEvent(e,n,r,s)}}const yr=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,st=(t,e,n,r)=>{let s,o;return i=>{e.value>=0&&(i||r)&&(o=e.value-(s||0),(o||s===void 0)&&(s=e.value,e.delta=o,e.rating=((a,c)=>a>c[1]?"poor":a>c[0]?"needs-improvement":"good")(e.value,n),t(e)))}},A=N,$t=(t=!0)=>{var n,r;const e=(r=(n=A.performance)==null?void 0:n.getEntriesByType)==null?void 0:r.call(n,"navigation")[0];if(!t||e&&e.responseStart>0&&e.responseStart<performance.now())return e},Rt=()=>{const t=$t();return(t==null?void 0:t.activationStart)||0},ot=(t,e)=>{var s,o;const n=$t();let r="navigate";return n&&((s=A.document)!=null&&s.prerendering||Rt()>0?r="prerender":(o=A.document)!=null&&o.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:t,value:e===void 0?-1:e,rating:"good",delta:0,entries:[],id:`v4-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},We=(t,e,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(t)){const r=new PerformanceObserver(s=>{Promise.resolve().then(()=>{e(s.getEntries())})});return r.observe(Object.assign({type:t,buffered:!0},n||{})),r}}catch{}},lt=t=>{const e=n=>{var r;n.type!=="pagehide"&&((r=A.document)==null?void 0:r.visibilityState)!=="hidden"||t(n)};A.document&&(addEventListener("visibilitychange",e,!0),addEventListener("pagehide",e,!0))},hn=t=>{let e=!1;return()=>{e||(t(),e=!0)}};let ht=-1;const on=t=>{A.document.visibilityState==="hidden"&&ht>-1&&(ht=t.type==="visibilitychange"?t.timeStamp:0,Na())},Na=()=>{removeEventListener("visibilitychange",on,!0),removeEventListener("prerenderingchange",on,!0)},mn=()=>(A.document&&ht<0&&(ht=A.document.visibilityState!=="hidden"||A.document.prerendering?1/0:0,addEventListener("visibilitychange",on,!0),addEventListener("prerenderingchange",on,!0)),{get firstHiddenTime(){return ht}}),Dt=t=>{var e;(e=A.document)!=null&&e.prerendering?addEventListener("prerenderingchange",()=>t(),!0):t()},Ma=[1800,3e3],ja=[.1,.25],Fa=(t,e={})=>{((n,r={})=>{Dt(()=>{const s=mn(),o=ot("FCP");let i;const a=We("paint",c=>{c.forEach(u=>{u.name==="first-contentful-paint"&&(a.disconnect(),u.startTime<s.firstHiddenTime&&(o.value=Math.max(u.startTime-Rt(),0),o.entries.push(u),i(!0)))})});a&&(i=st(n,o,Ma,r.reportAllChanges))})})(hn(()=>{const n=ot("CLS",0);let r,s=0,o=[];const i=c=>{c.forEach(u=>{if(!u.hadRecentInput){const d=o[0],h=o[o.length-1];s&&d&&h&&u.startTime-h.startTime<1e3&&u.startTime-d.startTime<5e3?(s+=u.value,o.push(u)):(s=u.value,o=[u])}}),s>n.value&&(n.value=s,n.entries=o,r())},a=We("layout-shift",i);a&&(r=st(t,n,ja,e.reportAllChanges),lt(()=>{i(a.takeRecords()),r(!0)}),setTimeout(r,0))}))},qa=[100,300],Ua=(t,e={})=>{Dt(()=>{const n=mn(),r=ot("FID");let s;const o=c=>{c.startTime<n.firstHiddenTime&&(r.value=c.processingStart-c.startTime,r.entries.push(c),s(!0))},i=c=>{c.forEach(o)},a=We("first-input",i);s=st(t,r,qa,e.reportAllChanges),a&&lt(hn(()=>{i(a.takeRecords()),a.disconnect()}))})};let zo=0,Cn=1/0,Ut=0;const Ba=t=>{t.forEach(e=>{e.interactionId&&(Cn=Math.min(Cn,e.interactionId),Ut=Math.max(Ut,e.interactionId),zo=Ut?(Ut-Cn)/7+1:0)})};let sr;const za=()=>{"interactionCount"in performance||sr||(sr=We("event",Ba,{type:"event",buffered:!0,durationThreshold:0}))},fe=[],In=new Map,Ha=()=>(sr?zo:performance.interactionCount||0)-0,Wa=[],Ga=t=>{var r;if(Wa.forEach(s=>s(t)),!t.interactionId&&t.entryType!=="first-input")return;const e=fe[fe.length-1],n=In.get(t.interactionId);if(n||fe.length<10||e&&t.duration>e.latency){if(n)t.duration>n.latency?(n.entries=[t],n.latency=t.duration):t.duration===n.latency&&t.startTime===((r=n.entries[0])==null?void 0:r.startTime)&&n.entries.push(t);else{const s={id:t.interactionId,latency:t.duration,entries:[t]};In.set(s.id,s),fe.push(s)}fe.sort((s,o)=>o.latency-s.latency),fe.length>10&&fe.splice(10).forEach(s=>In.delete(s.id))}},Ho=t=>{var r;const e=A.requestIdleCallback||A.setTimeout;let n=-1;return t=hn(t),((r=A.document)==null?void 0:r.visibilityState)==="hidden"?t():(n=e(t),lt(t)),n},Ja=[200,500],Ya=(t,e={})=>{"PerformanceEventTiming"in A&&"interactionId"in PerformanceEventTiming.prototype&&Dt(()=>{za();const n=ot("INP");let r;const s=i=>{Ho(()=>{i.forEach(Ga);const a=(()=>{const c=Math.min(fe.length-1,Math.floor(Ha()/50));return fe[c]})();a&&a.latency!==n.value&&(n.value=a.latency,n.entries=a.entries,r())})},o=We("event",s,{durationThreshold:e.durationThreshold!=null?e.durationThreshold:40});r=st(t,n,Ja,e.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),lt(()=>{s(o.takeRecords()),r(!0)}))})},Va=[2500,4e3],ws={},Ka=(t,e={})=>{Dt(()=>{const n=mn(),r=ot("LCP");let s;const o=a=>{e.reportAllChanges||(a=a.slice(-1)),a.forEach(c=>{c.startTime<n.firstHiddenTime&&(r.value=Math.max(c.startTime-Rt(),0),r.entries=[c],s())})},i=We("largest-contentful-paint",o);if(i){s=st(t,r,Va,e.reportAllChanges);const a=hn(()=>{ws[r.id]||(o(i.takeRecords()),i.disconnect(),ws[r.id]=!0,s(!0))});["keydown","click"].forEach(c=>{A.document&&addEventListener(c,()=>Ho(a),{once:!0,capture:!0})}),lt(a)}})},Xa=[800,1800],or=t=>{var e,n;(e=A.document)!=null&&e.prerendering?Dt(()=>or(t)):((n=A.document)==null?void 0:n.readyState)!=="complete"?addEventListener("load",()=>or(t),!0):setTimeout(t,0)},Za=(t,e={})=>{const n=ot("TTFB"),r=st(t,n,Xa,e.reportAllChanges);or(()=>{const s=$t();s&&(n.value=Math.max(s.responseStart-Rt(),0),n.entries=[s],r(!0))})},mt={},an={};let Wo,Go,Jo,Yo,Vo;function Ko(t,e=!1){return gt("cls",t,Qa,Wo,e)}function ft(t,e){return Xo(t,e),an[t]||(function(n){const r={};n==="event"&&(r.durationThreshold=0),We(n,s=>{pt(n,{entries:s})},r)}(t),an[t]=!0),Zo(t,e)}function pt(t,e){const n=mt[t];if(n!=null&&n.length)for(const r of n)try{r(e)}catch(s){yr&&S.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${be(r)}
Error:`,s)}}function Qa(){return Fa(t=>{pt("cls",{metric:t}),Wo=t},{reportAllChanges:!0})}function ec(){return Ua(t=>{pt("fid",{metric:t}),Go=t})}function tc(){return Ka(t=>{pt("lcp",{metric:t}),Jo=t},{reportAllChanges:!0})}function nc(){return Za(t=>{pt("ttfb",{metric:t}),Yo=t})}function rc(){return Ya(t=>{pt("inp",{metric:t}),Vo=t})}function gt(t,e,n,r,s=!1){let o;return Xo(t,e),an[t]||(o=n(),an[t]=!0),r&&e({metric:r}),Zo(t,e,s?o:void 0)}function Xo(t,e){mt[t]=mt[t]||[],mt[t].push(e)}function Zo(t,e,n){return()=>{n&&n();const r=mt[t];if(!r)return;const s=r.indexOf(e);s!==-1&&r.splice(s,1)}}function Pn(t){return typeof t=="number"&&isFinite(t)}function me(t,e,n,{...r}){const s=q(t).start_timestamp;return s&&s>e&&typeof t.updateStartTime=="function"&&t.updateStartTime(e),$o(t,()=>{const o=Ot({startTime:e,...r});return o&&o.end(n),o})}function Qo(t){var m;const e=j();if(!e)return;const{name:n,transaction:r,attributes:s,startTime:o}=t,{release:i,environment:a,sendDefaultPii:c}=e.getOptions(),u=e.getIntegrationByName("Replay"),d=u==null?void 0:u.getReplayId(),h=U(),f=h.getUser(),p=f!==void 0?f.email||f.id||f.ip_address:void 0;let l;try{l=h.getScopeData().contexts.profile.profile_id}catch{}return Ot({name:n,attributes:{release:i,environment:a,user:p||void 0,profile_id:l||void 0,replay_id:d||void 0,transaction:r,"user_agent.original":(m=A.navigator)==null?void 0:m.userAgent,"client.address":c?"{{auto}}":void 0,...s},startTime:o,experimental:{standalone:!0}})}function vr(){return A.addEventListener&&A.performance}function B(t){return t/1e3}function ei(t){let e="unknown",n="unknown",r="";for(const s of t){if(s==="/"){[e,n]=t.split("/");break}if(!isNaN(Number(s))){e=r==="h"?"http":r,n=t.split(r)[1];break}r+=s}return r===t&&(e=r),{name:e,version:n}}function sc(){let t,e,n=0;if(!function(){try{return PerformanceObserver.supportedEntryTypes.includes("layout-shift")}catch{return!1}}())return;let r=!1;function s(){r||(r=!0,e&&function(i,a,c){var l;yr&&S.log(`Sending CLS span (${i})`);const u=B((re()||0)+((a==null?void 0:a.startTime)||0)),d=U().getScopeData().transactionName,h=a?Be((l=a.sources[0])==null?void 0:l.node):"Layout shift",f={[H]:"auto.http.browser.cls",[ze]:"ui.webvital.cls",[Et]:(a==null?void 0:a.duration)||0,"sentry.pageload.span_id":c},p=Qo({name:h,transaction:d,attributes:f,startTime:u});p&&(p.addEvent("cls",{[ln]:"",[pn]:i}),p.end(u))}(n,t,e),o())}const o=Ko(({metric:i})=>{const a=i.entries[i.entries.length-1];a&&(n=i.value,t=a)},!0);lt(()=>{s()}),setTimeout(()=>{const i=j();if(!i)return;const a=i.on("startNavigationSpan",()=>{s(),a==null||a()}),c=Z();if(c){const u=V(c);q(u).op==="pageload"&&(e=u.spanContext().spanId)}},0)}const oc=2147483647;let X,Ye,Es=0,G={};function ic({recordClsStandaloneSpans:t}){const e=vr();if(e&&re()){e.mark&&A.performance.mark("sentry-tracing-init");const n=gt("fid",({metric:i})=>{const a=i.entries[i.entries.length-1];if(!a)return;const c=B(re()),u=B(a.startTime);G.fid={value:i.value,unit:"millisecond"},G["mark.fid"]={value:c+u,unit:"second"}},ec,Go),r=function(i,a=!1){return gt("lcp",i,tc,Jo,a)}(({metric:i})=>{const a=i.entries[i.entries.length-1];a&&(G.lcp={value:i.value,unit:"millisecond"},X=a)},!0),s=function(i){return gt("ttfb",i,nc,Yo)}(({metric:i})=>{i.entries[i.entries.length-1]&&(G.ttfb={value:i.value,unit:"millisecond"})}),o=t?sc():Ko(({metric:i})=>{const a=i.entries[i.entries.length-1];a&&(G.cls={value:i.value,unit:""},Ye=a)},!0);return()=>{n(),r(),s(),o==null||o()}}return()=>{}}function ac(t,e){const n=vr(),r=re();if(!(n!=null&&n.getEntries)||!r)return;const s=B(r),o=n.getEntries(),{op:i,start_timestamp:a}=q(t);if(o.slice(Es).forEach(c=>{const u=B(c.startTime),d=B(Math.max(0,c.duration));if(!(i==="navigation"&&a&&s+u<a))switch(c.entryType){case"navigation":(function(h,f,p){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach(l=>{Bt(h,f,l,p)}),Bt(h,f,"secureConnection",p,"TLS/SSL"),Bt(h,f,"fetch",p,"cache"),Bt(h,f,"domainLookup",p,"DNS"),function(l,m,_){const w=_+B(m.requestStart),y=_+B(m.responseEnd),E=_+B(m.responseStart);m.responseEnd&&(me(l,w,y,{op:"browser.request",name:m.name,attributes:{[H]:"auto.ui.browser.metrics"}}),me(l,E,y,{op:"browser.response",name:m.name,attributes:{[H]:"auto.ui.browser.metrics"}}))}(h,f,p)})(t,c,s);break;case"mark":case"paint":case"measure":{(function(p,l,m,_,w){const y=$t(!1),E=B(y?y.requestStart:0),g=w+Math.max(m,E),v=w+m,T=v+_,x={[H]:"auto.resource.browser.metrics"};if(g!==v&&(x["sentry.browser.measure_happened_before_request"]=!0,x["sentry.browser.measure_start_time"]=g),l.detail)if(typeof l.detail=="object")for(const[D,b]of Object.entries(l.detail))if(b&&vt(b))x[`sentry.browser.measure.detail.${D}`]=b;else try{x[`sentry.browser.measure.detail.${D}`]=JSON.stringify(b)}catch{}else if(vt(l.detail))x["sentry.browser.measure.detail"]=l.detail;else try{x["sentry.browser.measure.detail"]=JSON.stringify(l.detail)}catch{}g<=T&&me(p,g,T,{name:l.name,op:l.entryType,attributes:x})})(t,c,u,d,s);const h=mn(),f=c.startTime<h.firstHiddenTime;c.name==="first-paint"&&f&&(G.fp={value:c.startTime,unit:"millisecond"}),c.name==="first-contentful-paint"&&f&&(G.fcp={value:c.startTime,unit:"millisecond"});break}case"resource":(function(h,f,p,l,m,_){if(f.initiatorType==="xmlhttprequest"||f.initiatorType==="fetch")return;const w=Ke(p),y={[H]:"auto.resource.browser.metrics"};An(y,f,"transferSize","http.response_transfer_size"),An(y,f,"encodedBodySize","http.response_content_length"),An(y,f,"decodedBodySize","http.decoded_response_content_length");const E=f.deliveryType;E!=null&&(y["http.response_delivery_type"]=E);const g=f.renderBlockingStatus;g&&(y["resource.render_blocking_status"]=g),w.protocol&&(y["url.scheme"]=w.protocol.split(":").pop()),w.host&&(y["server.address"]=w.host),y["url.same_origin"]=p.includes(A.location.origin);const{name:v,version:T}=ei(f.nextHopProtocol);y["network.protocol.name"]=v,y["network.protocol.version"]=T;const x=_+l,D=x+m;me(h,x,D,{name:p.replace(A.location.origin,""),op:f.initiatorType?`resource.${f.initiatorType}`:"resource.other",attributes:y})})(t,c,c.name,u,d,s)}}),Es=Math.max(o.length-1,0),function(c){const u=A.navigator;if(!u)return;const d=u.connection;d&&(d.effectiveType&&c.setAttribute("effectiveConnectionType",d.effectiveType),d.type&&c.setAttribute("connectionType",d.type),Pn(d.rtt)&&(G["connection.rtt"]={value:d.rtt,unit:"millisecond"})),Pn(u.deviceMemory)&&c.setAttribute("deviceMemory",`${u.deviceMemory} GB`),Pn(u.hardwareConcurrency)&&c.setAttribute("hardwareConcurrency",String(u.hardwareConcurrency))}(t),i==="pageload"){(function(u){const d=$t(!1);if(!d)return;const{responseStart:h,requestStart:f}=d;f<=h&&(u["ttfb.requestTime"]={value:h-f,unit:"millisecond"})})(G);const c=G["mark.fid"];c&&G.fid&&(me(t,c.value,c.value+B(G.fid.value),{name:"first input delay",op:"ui.action",attributes:{[H]:"auto.ui.browser.metrics"}}),delete G["mark.fid"]),"fcp"in G&&e.recordClsOnPageloadSpan||delete G.cls,Object.entries(G).forEach(([u,d])=>{(function(h,f,p,l=Z()){const m=l&&V(l);m&&($&&S.log(`[Measurement] Setting measurement on root span: ${h} = ${f} ${p}`),m.addEvent(h,{[pn]:f,[ln]:p}))})(u,d.value,d.unit)}),t.setAttribute("performance.timeOrigin",s),t.setAttribute("performance.activationStart",Rt()),function(u){X&&(X.element&&u.setAttribute("lcp.element",Be(X.element)),X.id&&u.setAttribute("lcp.id",X.id),X.url&&u.setAttribute("lcp.url",X.url.trim().slice(0,200)),X.loadTime!=null&&u.setAttribute("lcp.loadTime",X.loadTime),X.renderTime!=null&&u.setAttribute("lcp.renderTime",X.renderTime),u.setAttribute("lcp.size",X.size)),Ye!=null&&Ye.sources&&Ye.sources.forEach((d,h)=>u.setAttribute(`cls.source.${h+1}`,Be(d.node)))}(t)}X=void 0,Ye=void 0,G={}}function Bt(t,e,n,r,s=n){const o=function(c){return c==="secureConnection"?"connectEnd":c==="fetch"?"domainLookupStart":`${c}End`}(n),i=e[o],a=e[`${n}Start`];a&&i&&me(t,r+B(a),r+B(i),{op:`browser.${s}`,name:e.name,attributes:{[H]:"auto.ui.browser.metrics",...n==="redirect"&&e.redirectCount!=null?{"http.redirect_count":e.redirectCount}:{}}})}function An(t,e,n,r){const s=e[n];s!=null&&s<oc&&(t[r]=s)}const cc=1e3;let xs,On,Rn,zt;function uc(){if(!A.document)return;const t=ie.bind(null,"dom"),e=Ts(t,!0);A.document.addEventListener("click",e,!1),A.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach(n=>{var o,i;const r=A,s=(o=r[n])==null?void 0:o.prototype;(i=s==null?void 0:s.hasOwnProperty)!=null&&i.call(s,"addEventListener")&&(Q(s,"addEventListener",function(a){return function(c,u,d){if(c==="click"||c=="keypress")try{const h=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},f=h[c]=h[c]||{refCount:0};if(!f.handler){const p=Ts(t);f.handler=p,a.call(this,c,p,d)}f.refCount++}catch{}return a.call(this,c,u,d)}}),Q(s,"removeEventListener",function(a){return function(c,u,d){if(c==="click"||c=="keypress")try{const h=this.__sentry_instrumentation_handlers__||{},f=h[c];f&&(f.refCount--,f.refCount<=0&&(a.call(this,c,f.handler,d),f.handler=void 0,delete h[c]),Object.keys(h).length===0&&delete this.__sentry_instrumentation_handlers__)}catch{}return a.call(this,c,u,d)}}))})}function Ts(t,e=!1){return n=>{if(!n||n._sentryCaptured)return;const r=function(o){try{return o.target}catch{return null}}(n);if(function(o,i){return o==="keypress"&&(!(i!=null&&i.tagName)||i.tagName!=="INPUT"&&i.tagName!=="TEXTAREA"&&!i.isContentEditable)}(n.type,r))return;ee(n,"_sentryCaptured",!0),r&&!r._sentryId&&ee(r,"_sentryId",oe());const s=n.type==="keypress"?"input":n.type;(function(o){if(o.type!==On)return!1;try{if(!o.target||o.target._sentryId!==Rn)return!1}catch{}return!0})(n)||(t({event:n,name:s,global:e}),On=n.type,Rn=r?r._sentryId:void 0),clearTimeout(xs),xs=A.setTimeout(()=>{Rn=void 0,On=void 0},cc)}}function br(t){const e="history";xe(e,t),Te(e,dc)}function dc(){function t(e){return function(...n){const r=n.length>2?n[2]:void 0;if(r){const s=zt,o=function(i){try{return new URL(i,A.location.origin).toString()}catch{return i}}(String(r));if(zt=o,s===o)return e.apply(this,n);ie("history",{from:s,to:o})}return e.apply(this,n)}}A.addEventListener("popstate",()=>{const e=A.location.href,n=zt;zt=e,n!==e&&ie("history",{from:n,to:e})}),"history"in kt&&kt.history&&(Q(A.history,"pushState",t),Q(A.history,"replaceState",t))}const Vt={};function ks(t){Vt[t]=void 0}const Ve="__sentry_xhr_v3__";function ti(t){xe("xhr",t),Te("xhr",lc)}function lc(){if(!A.XMLHttpRequest)return;const t=XMLHttpRequest.prototype;t.open=new Proxy(t.open,{apply(e,n,r){const s=new Error,o=1e3*K(),i=ge(r[0])?r[0].toUpperCase():void 0,a=function(u){if(ge(u))return u;try{return u.toString()}catch{}}(r[1]);if(!i||!a)return e.apply(n,r);n[Ve]={method:i,url:a,request_headers:{}},i==="POST"&&a.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const c=()=>{const u=n[Ve];if(u&&n.readyState===4){try{u.status_code=n.status}catch{}ie("xhr",{endTimestamp:1e3*K(),startTimestamp:o,xhr:n,virtualError:s})}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply:(u,d,h)=>(c(),u.apply(d,h))}):n.addEventListener("readystatechange",c),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(u,d,h){const[f,p]=h,l=d[Ve];return l&&ge(f)&&ge(p)&&(l.request_headers[f.toLowerCase()]=p),u.apply(d,h)}}),e.apply(n,r)}}),t.send=new Proxy(t.send,{apply(e,n,r){const s=n[Ve];return s?(r[0]!==void 0&&(s.body=r[0]),ie("xhr",{startTimestamp:1e3*K(),xhr:n}),e.apply(n,r)):e.apply(n,r)}})}const Dn=[],Kt=new Map;function pc(){if(vr()&&re()){const t=gt("inp",({metric:e})=>{if(e.value==null)return;const n=e.entries.find(f=>f.duration===e.value&&$s[f.name]);if(!n)return;const{interactionId:r}=n,s=$s[n.name],o=B(re()+n.startTime),i=B(e.value),a=Z(),c=a?V(a):void 0,u=(r!=null?Kt.get(r):void 0)||c,d=u?q(u).description:U().getScopeData().transactionName,h=Qo({name:Be(n.target),transaction:d,attributes:{[H]:"auto.http.browser.inp",[ze]:`ui.interaction.${s}`,[Et]:n.duration},startTime:o});h&&(h.addEvent("inp",{[ln]:"millisecond",[pn]:e.value}),h.end(o+i))},rc,Vo);return()=>{t()}}return()=>{}}const $s={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function fc(t,e=function(n){const r=Vt[n];if(r)return r;let s=A[n];if(Zn(s))return Vt[n]=s.bind(A);const o=A.document;if(o&&typeof o.createElement=="function")try{const i=o.createElement("iframe");i.hidden=!0,o.head.appendChild(i);const a=i.contentWindow;a!=null&&a[n]&&(s=a[n]),o.head.removeChild(i)}catch(i){yr&&S.warn(`Could not create sandbox iframe for ${n} check, bailing to window.${n}: `,i)}return s&&(Vt[n]=s.bind(A))}("fetch")){let n=0,r=0;return pa(t,function(s){const o=s.body.length;n+=o,r++;const i={body:s.body,method:"POST",referrerPolicy:"strict-origin",headers:t.headers,keepalive:n<=6e4&&r<15,...t.fetchOptions};if(!e)return ks("fetch"),sn("No fetch implementation available");try{return e(t.url,i).then(a=>(n-=o,r--,{statusCode:a.status,headers:{"x-sentry-rate-limits":a.headers.get("X-Sentry-Rate-Limits"),"retry-after":a.headers.get("Retry-After")}}))}catch(a){return ks("fetch"),n-=o,r--,sn(a)}})}function Ln(t,e,n,r){const s={filename:t,function:e==="<anonymous>"?qe:e,in_app:!0};return n!==void 0&&(s.lineno=n),r!==void 0&&(s.colno=r),s}const hc=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,mc=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,gc=/\((\S*)(?::(\d+))(?::(\d+))\)/,_c=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,yc=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,vc=yo([30,t=>{const e=hc.exec(t);if(e){const[,r,s,o]=e;return Ln(r,qe,+s,+o)}const n=mc.exec(t);if(n){if(n[2]&&n[2].indexOf("eval")===0){const o=gc.exec(n[2]);o&&(n[2]=o[1],n[3]=o[2],n[4]=o[3])}const[r,s]=Cs(n[1]||qe,n[2]);return Ln(s,r,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,t=>{const e=_c.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){const s=yc.exec(e[3]);s&&(e[1]=e[1]||"eval",e[3]=s[1],e[4]=s[2],e[5]="")}let n=e[3],r=e[1]||qe;return[r,n]=Cs(r,n),Ln(n,r,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}}]),Cs=(t,e)=>{const n=t.indexOf("safari-extension")!==-1,r=t.indexOf("safari-web-extension")!==-1;return n||r?[t.indexOf("@")!==-1?t.split("@")[0]:qe,n?`safari-extension:${e}`:`safari-web-extension:${e}`]:[t,e]},ne=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Is=1024,bc=(t={})=>{const e={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t};return{name:"Breadcrumbs",setup(n){var r;e.console&&function(s){const o="console";xe(o,s),Te(o,Sa)}(function(s){return function(o){if(j()!==s)return;const i={category:"console",data:{arguments:o.args,logger:"console"},level:wa(o.level),message:kr(o.args," ")};if(o.level==="assert"){if(o.args[0]!==!1)return;i.message=`Assertion failed: ${kr(o.args.slice(1)," ")||"console.assert"}`,i.data.arguments=o.args.slice(1)}Ae(i,{input:o.args,level:o.level})}}(n)),e.dom&&(r=function(s,o){return function(i){if(j()!==s)return;let a,c,u=typeof o=="object"?o.serializeAttribute:void 0,d=typeof o=="object"&&typeof o.maxStringLength=="number"?o.maxStringLength:void 0;d&&d>Is&&(ne&&S.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${d} was configured. Sentry will use 1024 instead.`),d=Is),typeof u=="string"&&(u=[u]);try{const f=i.event,p=function(l){return!!l&&!!l.target}(f)?f.target:f;a=Be(p,{keyAttrs:u,maxStringLength:d}),c=ro(p)}catch{a="<unknown>"}if(a.length===0)return;const h={category:`ui.${i.name}`,message:a};c&&(h.data={"ui.component_name":c}),Ae(h,{event:i.event,name:i.name,global:i.global})}}(n,e.dom),xe("dom",r),Te("dom",uc)),e.xhr&&ti(function(s){return function(o){if(j()!==s)return;const{startTimestamp:i,endTimestamp:a}=o,c=o.xhr[Ve];if(!i||!a||!c)return;const{method:u,url:d,status_code:h,body:f}=c,p={method:u,url:d,status_code:h},l={xhr:o.xhr,input:f,startTimestamp:i,endTimestamp:a},m={category:"xhr",data:p,type:"http",level:vs(h)};s.emit("beforeOutgoingRequestBreadcrumb",m,l),Ae(m,l)}}(n)),e.fetch&&qo(function(s){return function(o){if(j()!==s)return;const{startTimestamp:i,endTimestamp:a}=o;if(a&&(!o.fetchData.url.match(/sentry_key/)||o.fetchData.method!=="POST"))if(o.fetchData.method,o.fetchData.url,o.error){const c=o.fetchData,u={data:o.error,input:o.args,startTimestamp:i,endTimestamp:a},d={category:"fetch",data:c,level:"error",type:"http"};s.emit("beforeOutgoingRequestBreadcrumb",d,u),Ae(d,u)}else{const c=o.response,u={...o.fetchData,status_code:c==null?void 0:c.status};o.fetchData.request_body_size,o.fetchData.response_body_size;const d={input:o.args,response:c,startTimestamp:i,endTimestamp:a},h={category:"fetch",data:u,type:"http",level:vs(u.status_code)};s.emit("beforeOutgoingRequestBreadcrumb",h,d),Ae(h,d)}}}(n)),e.history&&br(function(s){return function(o){if(j()!==s)return;let i=o.from,a=o.to;const c=Ke(L.location.href);let u=i?Ke(i):void 0;const d=Ke(a);u!=null&&u.path||(u=c),c.protocol===d.protocol&&c.host===d.host&&(a=d.relative),c.protocol===u.protocol&&c.host===u.host&&(i=u.relative),Ae({category:"navigation",data:{from:i,to:a}})}}(n)),e.sentry&&n.on("beforeSendEvent",function(s){return function(o){j()===s&&Ae({category:"sentry."+(o.type==="transaction"?"transaction":"event"),event_id:o.event_id,level:o.level,message:Oe(o)},{event:o})}}(n))}}},Sc=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],wc=(t={})=>{const e={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t};return{name:"BrowserApiErrors",setupOnce(){e.setTimeout&&Q(L,"setTimeout",Ps),e.setInterval&&Q(L,"setInterval",Ps),e.requestAnimationFrame&&Q(L,"requestAnimationFrame",Ec),e.XMLHttpRequest&&"XMLHttpRequest"in L&&Q(XMLHttpRequest.prototype,"send",xc);const n=e.eventTarget;n&&(Array.isArray(n)?n:Sc).forEach(Tc)}}};function Ps(t){return function(...e){const n=e[0];return e[0]=rt(n,{mechanism:{data:{function:be(t)},handled:!1,type:"instrument"}}),t.apply(this,e)}}function Ec(t){return function(e){return t.apply(this,[rt(e,{mechanism:{data:{function:"requestAnimationFrame",handler:be(t)},handled:!1,type:"instrument"}})])}}function xc(t){return function(...e){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(r=>{r in n&&typeof n[r]=="function"&&Q(n,r,function(s){const o={mechanism:{data:{function:r,handler:be(s)},handled:!1,type:"instrument"}},i=lr(s);return i&&(o.mechanism.data.handler=be(i)),rt(s,o)})}),t.apply(this,e)}}function Tc(t){var r,s;const e=L,n=(r=e[t])==null?void 0:r.prototype;(s=n==null?void 0:n.hasOwnProperty)!=null&&s.call(n,"addEventListener")&&(Q(n,"addEventListener",function(o){return function(i,a,c){try{typeof a.handleEvent=="function"&&(a.handleEvent=rt(a.handleEvent,{mechanism:{data:{function:"handleEvent",handler:be(a),target:t},handled:!1,type:"instrument"}}))}catch{}return o.apply(this,[i,rt(a,{mechanism:{data:{function:"addEventListener",handler:be(a),target:t},handled:!1,type:"instrument"}}),c])}}),Q(n,"removeEventListener",function(o){return function(i,a,c){try{const u=a.__sentry_wrapped__;u&&o.call(this,i,u,c)}catch{}return o.call(this,i,a,c)}}))}const kc=()=>({name:"BrowserSession",setupOnce(){L.document!==void 0?(ts({ignoreDuration:!0}),ns(),br(({from:t,to:e})=>{t!==void 0&&t!==e&&(ts({ignoreDuration:!0}),ns())})):ne&&S.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.")}}),$c=(t={})=>{const e={onerror:!0,onunhandledrejection:!0,...t};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(n){e.onerror&&(function(r){vo(s=>{const{stackParser:o,attachStacktrace:i}=Os();if(j()!==r||Ss())return;const{msg:a,url:c,line:u,column:d,error:h}=s,f=function(p,l,m,_){const w=p.exception=p.exception||{},y=w.values=w.values||[],E=y[0]=y[0]||{},g=E.stacktrace=E.stacktrace||{},v=g.frames=g.frames||[],T=_,x=m,D=ge(l)&&l.length>0?l:Ct();return v.length===0&&v.push({colno:T,filename:D,function:qe,in_app:!0,lineno:x}),p}(nr(o,h||a,void 0,i,!1),c,u,d);f.level="error",es(f,{originalException:h,mechanism:{handled:!1,type:"onerror"}})})}(n),As("onerror")),e.onunhandledrejection&&(function(r){bo(s=>{const{stackParser:o,attachStacktrace:i}=Os();if(j()!==r||Ss())return;const a=function(u){if(vt(u))return u;try{if("reason"in u)return u.reason;if("detail"in u&&"reason"in u.detail)return u.detail.reason}catch{}return u}(s),c=vt(a)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(a)}`}]}}:nr(o,a,void 0,i,!0);c.level="error",es(c,{originalException:a,mechanism:{handled:!1,type:"onunhandledrejection"}})})}(n),As("onunhandledrejection"))}}};function As(t){ne&&S.log(`Global Handler attached: ${t}`)}function Os(){const t=j();return(t==null?void 0:t.getOptions())||{stackParser:()=>[],attachStacktrace:!1}}const Cc=()=>({name:"HttpContext",preprocessEvent(t){var r;if(!L.navigator&&!L.location&&!L.document)return;const e=tr(),n={...e.headers,...(r=t.request)==null?void 0:r.headers};t.request={...e,...t.request,headers:n}}}),Ic=(t={})=>{const e=t.limit||5,n=t.key||"cause";return{name:"LinkedErrors",preprocessEvent(r,s,o){ba(gr,o.getOptions().stackParser,n,e,r,s)}}};function Pc(t){const e={};for(const n of Object.getOwnPropertyNames(t)){const r=n;t[r]!==void 0&&(e[r]=t[r])}return e}function Ac(t={}){const e=function(s={}){var o;return{defaultIntegrations:[va(),ga(),wc(),bc(),$c(),Ic(),Ea(),Cc(),kc()],release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:(o=L.SENTRY_RELEASE)==null?void 0:o.id,sendClientReports:!0,...Pc(s)}}(t);if(!e.skipBrowserExtensionCheck&&function(){var d;const s=L.window!==void 0&&L;if(!s)return!1;const o=s[s.chrome?"chrome":"browser"],i=(d=o==null?void 0:o.runtime)==null?void 0:d.id,a=Ct()||"",c=!!i&&L===L.top&&["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"].some(h=>a.startsWith(`${h}//`)),u=s.nw!==void 0;return!!i&&!c&&!u}())return void(ne&&Je(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")}));ne&&!Fo()&&S.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill.");const n={...e,stackParser:(r=e.stackParser||vc,Array.isArray(r)?yo(...r):r),integrations:oa(e),transport:e.transport||fc};var r;return aa(La,n)}const Rs=new WeakMap,Nn=new Map,ni={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,trackFetchStreamPerformance:!1};function Oc(t,e){const{traceFetch:n,traceXHR:r,trackFetchStreamPerformance:s,shouldCreateSpanForRequest:o,enableHTTPTimings:i,tracePropagationTargets:a,onRequestSpanStart:c}={...ni,...e},u=typeof o=="function"?o:f=>!0,d=f=>function(p,l){const m=Ct();if(m){let _,w;try{_=new URL(p,m),w=new URL(m).origin}catch{return!1}const y=_.origin===w;return l?De(_.toString(),l)||y&&De(_.pathname,l):y}{const _=!!p.match(/^\/(?!\/)/);return l?De(p,l):_}}(f,a),h={};n&&(t.addEventProcessor(f=>(f.type==="transaction"&&f.spans&&f.spans.forEach(p=>{if(p.op==="http.client"){const l=Nn.get(p.span_id);l&&(p.timestamp=l/1e3,Nn.delete(p.span_id))}}),f)),s&&function(f){const p="fetch-body-resolved";xe(p,f),Te(p,()=>Uo(Ca))}(f=>{if(f.response){const p=Rs.get(f.response);p&&f.endTimestamp&&Nn.set(p,f.endTimestamp)}}),qo(f=>{const p=ka(f,u,d,h);if(f.response&&f.fetchData.__span&&Rs.set(f.response,f.fetchData.__span),p){const l=Ls(f.fetchData.url),m=l?Ke(l).host:void 0;p.setAttributes({"http.url":l,"server.address":m}),i&&Ds(p),c==null||c(p,{headers:f.headers})}})),r&&ti(f=>{var l;const p=function(m,_,w,y){const E=m.xhr,g=E==null?void 0:E[Ve];if(!E||E.__sentry_own_request__||!g)return;const{url:v,method:T}=g,x=ke()&&_(v);if(m.endTimestamp&&x){const C=E.__sentry_xhr_span_id__;if(!C)return;const M=y[C];return void(M&&g.status_code!==void 0&&(lo(M,g.status_code),M.end(),delete y[C]))}const D=Ls(v),b=Ke(D||v),O=(I=v,I.split(/[?#]/,1)[0]),R=!!Z(),k=x&&R?Ot({name:`${T} ${O}`,attributes:{url:v,type:"xhr","http.method":T,"http.url":D,"server.address":b==null?void 0:b.host,[H]:"auto.http.browser",[ze]:"http.client",...(b==null?void 0:b.search)&&{"http.query":b==null?void 0:b.search},...(b==null?void 0:b.hash)&&{"http.fragment":b==null?void 0:b.hash}}}):new Ue;var I;E.__sentry_xhr_span_id__=k.spanContext().spanId,y[E.__sentry_xhr_span_id__]=k,w(v)&&function(C,M){const{"sentry-trace":W,baggage:J}=No({span:M});W&&function(Pe,Se,we){var Sr;const ae=(Sr=Pe.__sentry_xhr_v3__)==null?void 0:Sr.request_headers;if(!(ae!=null&&ae["sentry-trace"]))try{if(Pe.setRequestHeader("sentry-trace",Se),we){const gn=ae==null?void 0:ae.baggage;gn&&gn.split(",").some(ii=>ii.trim().startsWith("sentry-"))||Pe.setRequestHeader("baggage",we)}}catch{}}(C,W,J)}(E,ke()&&R?k:void 0);const P=j();return P&&P.emit("beforeOutgoingRequestSpan",k,m),k}(f,u,d,h);if(p){let m;i&&Ds(p);try{m=new Headers((l=f.xhr.__sentry_xhr_v3__)==null?void 0:l.request_headers)}catch{}c==null||c(p,{headers:m})}})}function Ds(t){const{url:e}=q(t).data;if(!e||typeof e!="string")return;const n=ft("resource",({entries:r})=>{r.forEach(s=>{(function(o){return o.entryType==="resource"&&"initiatorType"in o&&typeof o.nextHopProtocol=="string"&&(o.initiatorType==="fetch"||o.initiatorType==="xmlhttprequest")})(s)&&s.name.endsWith(e)&&(function(o){const{name:i,version:a}=ei(o.nextHopProtocol),c=[];return c.push(["network.protocol.version",a],["network.protocol.name",i]),re()?[...c,["http.request.redirect_start",ue(o.redirectStart)],["http.request.fetch_start",ue(o.fetchStart)],["http.request.domain_lookup_start",ue(o.domainLookupStart)],["http.request.domain_lookup_end",ue(o.domainLookupEnd)],["http.request.connect_start",ue(o.connectStart)],["http.request.secure_connection_start",ue(o.secureConnectionStart)],["http.request.connection_end",ue(o.connectEnd)],["http.request.request_start",ue(o.requestStart)],["http.request.response_start",ue(o.responseStart)],["http.request.response_end",ue(o.responseEnd)]]:c}(s).forEach(o=>t.setAttribute(...o)),setTimeout(n))})})}function ue(t=0){return((re()||performance.timeOrigin)+t)/1e3}function Ls(t){try{return new URL(t,L.location.origin).href}catch{return}}const Rc=3600,Ns="sentry_previous_trace",Dc="sentry.previous_trace";function Lc(t,{linkPreviousTrace:e,consistentTraceSampling:n}){const r=e==="session-storage";let s=r?function(){var i;try{const a=(i=L.sessionStorage)==null?void 0:i.getItem(Ns);return JSON.parse(a)}catch{return}}():void 0;t.on("spanStart",i=>{if(V(i)!==i)return;const a=U().getPropagationContext();s=function(c,u,d){const h=q(u);function f(){var m,_;try{return Number((m=d.dsc)==null?void 0:m.sample_rate)??Number((_=h.data)==null?void 0:_[pr])}catch{return 0}}const p={spanContext:u.spanContext(),startTimestamp:h.start_timestamp,sampleRate:f(),sampleRand:d.sampleRand};if(!c)return p;const l=c.spanContext;return l.traceId===h.trace_id?c:(Date.now()/1e3-c.startTimestamp<=Rc&&(ne&&S.info(`Adding previous_trace ${l} link to span ${{op:h.op,...u.spanContext()}}`),u.addLink({context:l,attributes:{[Oi]:"previous_trace"}}),u.setAttribute(Dc,`${l.traceId}-${l.spanId}-${Mn(l)?1:0}`)),p)}(s,i,a),r&&function(c){try{L.sessionStorage.setItem(Ns,JSON.stringify(c))}catch(u){ne&&S.warn("Could not store previous trace in sessionStorage",u)}}(s)});let o=!0;n&&t.on("beforeSampling",i=>{if(!s)return;const a=U(),c=a.getPropagationContext();o&&c.parentSpanId?o=!1:(a.setPropagationContext({...c,dsc:{...c.dsc,sample_rate:String(s.sampleRate),sampled:String(Mn(s.spanContext))},sampleRand:s.sampleRand}),i.parentSampled=Mn(s.spanContext),i.parentSampleRate=s.sampleRate,i.spanAttributes={...i.spanAttributes,[co]:s.sampleRate})})}function Mn(t){return t.traceFlags===1}const Nc={...Yt,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!0,enableInp:!0,linkPreviousTrace:"in-memory",consistentTraceSampling:!1,_experiments:{},...ni};let Ms=!1;const Mc=(t={})=>{Ms&&Je(()=>{console.warn("Multiple browserTracingIntegration instances are not supported.")}),Ms=!0;const e=L.document;zr||(zr=!0,vo(Gn),bo(Gn));const{enableInp:n,enableLongTask:r,enableLongAnimationFrame:s,_experiments:{enableInteractions:o,enableStandaloneClsSpans:i},beforeStartSpan:a,idleTimeout:c,finalTimeout:u,childSpanTimeout:d,markBackgroundSpan:h,traceFetch:f,traceXHR:p,trackFetchStreamPerformance:l,shouldCreateSpanForRequest:m,enableHTTPTimings:_,instrumentPageLoad:w,instrumentNavigation:y,linkPreviousTrace:E,consistentTraceSampling:g,onRequestSpanStart:v}={...Nc,...t},T=ic({recordClsStandaloneSpans:i||!1});n&&pc(),s&&N.PerformanceObserver&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?new PerformanceObserver(b=>{const O=Z();if(O)for(const R of b.getEntries()){if(!R.scripts[0])continue;const k=B(re()+R.startTime),{start_timestamp:I,op:P}=q(O);if(P==="navigation"&&I&&k<I)continue;const C=B(R.duration),M={[H]:"auto.ui.browser.metrics"},W=R.scripts[0],{invoker:J,invokerType:Pe,sourceURL:Se,sourceFunctionName:we,sourceCharPosition:ae}=W;M["browser.script.invoker"]=J,M["browser.script.invoker_type"]=Pe,Se&&(M["code.filepath"]=Se),we&&(M["code.function"]=we),ae!==-1&&(M["browser.script.source_char_position"]=ae),me(O,k,k+C,{name:"Main UI thread blocked",op:"ui.long-animation-frame",attributes:M})}}).observe({type:"long-animation-frame",buffered:!0}):r&&ft("longtask",({entries:b})=>{const O=Z();if(!O)return;const{op:R,start_timestamp:k}=q(O);for(const I of b){const P=B(re()+I.startTime),C=B(I.duration);R==="navigation"&&k&&P<k||me(O,P,P+C,{name:"Main UI thread blocked",op:"ui.long-task",attributes:{[H]:"auto.ui.browser.metrics"}})}}),o&&ft("event",({entries:b})=>{const O=Z();if(O){for(const R of b)if(R.name==="click"){const k=B(re()+R.startTime),I=B(R.duration),P={name:Be(R.target),op:`ui.interaction.${R.name}`,startTime:k,attributes:{[H]:"auto.ui.browser.metrics"}},C=ro(R.target);C&&(P.attributes["ui.component_name"]=C),me(O,k,k+I,P)}}});const x={name:void 0,source:void 0};function D(b,O){const R=O.op==="pageload",k=a?a(O):O,I=k.attributes||{};O.name!==k.name&&(I[he]="custom",k.attributes=I),x.name=k.name,x.source=I[he];const P=Xr(k,{idleTimeout:c,finalTimeout:u,childSpanTimeout:d,disableAutoFinish:R,beforeSpanEnd:M=>{T(),ac(M,{recordClsOnPageloadSpan:!i}),Fs(b,void 0);const W=U(),J=W.getPropagationContext();W.setPropagationContext({...J,traceId:P.spanContext().traceId,sampled:je(P),dsc:$e(M)})}});function C(){e&&["interactive","complete"].includes(e.readyState)&&b.emit("idleSpanEnableAutoFinish",P)}Fs(b,P),R&&e&&(e.addEventListener("readystatechange",()=>{C()}),C())}return{name:"BrowserTracing",afterAllSetup(b){let O=Ct();function R(){const k=Ht(b);k&&!q(k).timestamp&&(ne&&S.log(`[Tracing] Finishing current active span with op: ${q(k).op}`),k.setAttribute(nn,"cancelled"),k.end())}if(b.on("startNavigationSpan",k=>{if(j()!==b)return;R(),Ie().setPropagationContext({traceId:Ee(),sampleRand:Math.random()});const I=U();I.setPropagationContext({traceId:Ee(),sampleRand:Math.random()}),I.setSDKProcessingMetadata({normalizedRequest:void 0}),D(b,{op:"navigation",...k})}),b.on("startPageLoadSpan",(k,I={})=>{if(j()!==b)return;R();const P=Mi(I.sentryTrace||js("sentry-trace"),I.baggage||js("baggage")),C=U();C.setPropagationContext(P),C.setSDKProcessingMetadata({normalizedRequest:tr()}),D(b,{op:"pageload",...k})}),E!=="off"&&Lc(b,{linkPreviousTrace:E,consistentTraceSampling:g}),L.location){if(w){const k=re();(function(I,P,C){I.emit("startPageLoadSpan",P,C),U().setTransactionName(P.name),Ht(I)})(b,{name:L.location.pathname,startTime:k?k/1e3:void 0,attributes:{[he]:"url",[H]:"auto.pageload.browser"}})}y&&br(({to:k,from:I})=>{if(I===void 0&&(O==null?void 0:O.indexOf(k))!==-1)return void(O=void 0);O=void 0;const P=jo(k);(function(C,M){C.emit("startNavigationSpan",M),U().setTransactionName(M.name),Ht(C)})(b,{name:(P==null?void 0:P.pathname)||L.location.pathname,attributes:{[he]:"url",[H]:"auto.navigation.browser"}}),U().setSDKProcessingMetadata({normalizedRequest:{...tr(),url:k}})})}h&&(L.document?L.document.addEventListener("visibilitychange",()=>{const k=Z();if(!k)return;const I=V(k);if(L.document.hidden&&I){const P="cancelled",{op:C,status:M}=q(I);ne&&S.log(`[Tracing] Transaction: ${P} -> since tab moved to the background, op: ${C}`),M||I.setStatus({code:z,message:P}),I.setAttribute("sentry.cancellation_reason","document.hidden"),I.end()}}):ne&&S.warn("[Tracing] Could not set up background tab detection due to lack of global document")),o&&function(k,I,P,C,M){const W=L.document;let J;const Pe=()=>{const Se="ui.action.click",we=Ht(k);if(we){const ae=q(we).op;if(["navigation","pageload"].includes(ae))return void(ne&&S.warn(`[Tracing] Did not create ${Se} span because a pageload or navigation span is in progress.`))}J&&(J.setAttribute(nn,"interactionInterrupted"),J.end(),J=void 0),M.name?J=Xr({name:M.name,op:Se,attributes:{[he]:M.source||"url"}},{idleTimeout:I,finalTimeout:P,childSpanTimeout:C}):ne&&S.warn(`[Tracing] Did not create ${Se} transaction because _latestRouteName is missing.`)};W&&addEventListener("click",Pe,{once:!1,capture:!0})}(b,c,u,d,x),n&&function(){const k=({entries:I})=>{const P=Z(),C=P&&V(P);I.forEach(M=>{if(!function(J){return"duration"in J}(M)||!C)return;const W=M.interactionId;if(W!=null&&!Kt.has(W)){if(Dn.length>10){const J=Dn.shift();Kt.delete(J)}Dn.push(W),Kt.set(W,C)}})};ft("event",k),ft("first-input",k)}(),Oc(b,{traceFetch:f,traceXHR:p,trackFetchStreamPerformance:l,tracePropagationTargets:b.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:m,enableHTTPTimings:_,onRequestSpanStart:v})}}};function js(t){const e=L.document,n=e==null?void 0:e.querySelector(`meta[name=${t}]`);return(n==null?void 0:n.getAttribute("content"))||void 0}const ri="_sentry_idleSpan";function Ht(t){return t[ri]}function Fs(t,e){ee(t,ri,e)}class si extends Error{constructor(e){super(e),this.name="PerformanceException"}}class jc extends si{constructor(n,r,s,o="unknown"){super(`Slow framerate detected: ${n.toFixed(1)} fps`);Y(this,"fps");Y(this,"threshold");Y(this,"avgFramerate");Y(this,"webviewId");this.name="SlowFramerateException",this.fps=n,this.threshold=r,this.avgFramerate=s,this.webviewId=o}}class Fc extends si{constructor(n,r,s=null,o="unknown"){super(`Slow INP detected: ${n.toFixed(1)} ms${s?` on target: ${s}`:""}`);Y(this,"inp");Y(this,"threshold");Y(this,"target");Y(this,"webviewId");this.name="SlowINPException",this.inp=n,this.threshold=r,this.target=s,this.webviewId=o}}function qc(t){if(window.augmentPerformance=window.augmentPerformance||{},window.augmentPerformance.initialized)return;window.augmentPerformance.initialized=!0;let e=0,n=performance.now(),r=60;const s=[];let o=0;const i=t.lowFramerateThreshold,a=t.slowInpThreshold;if(requestAnimationFrame(function c(u){const d=u-n;if(e++,d>1e3){r=1e3*e/d,e=0,n=u,s.push(r),s.length>10&&s.shift();const h=s.reduce((f,p)=>f+p,0)/s.length;if(r<i){console.error(`[Augment Performance] Slow framerate detected: ${r.toFixed(1)} fps`),console.error(`[Augment Performance] Avg framerate detected: ${h.toFixed(1)} fps`);const f=r<15,p=new jc(r,i,h,window.location.href);wt(l=>{l.setTag("performance_issue","slow_framerate"),l.setTag("fps_value",r.toFixed(1)),l.setTag("avg_fps",h.toFixed(1)),l.setTag("webview_url",window.location.href),l.setExtra("performance_data",{fps:r,avgFps:h,threshold:i,isCritical:f,framerateHistory:[...s]}),l.setLevel("warning"),Kn(p)})}}requestAnimationFrame(c)}),PerformanceObserver.supportedEntryTypes.includes("event"))try{new PerformanceObserver(c=>{(u=>{const d=u.getEntries().filter(p=>"interactionId"in p&&"duration"in p&&p.startTime>0&&p.duration<1e6);if(d.length===0)return;d.sort((p,l)=>l.duration-p.duration);const h=Math.floor(.98*d.length),f=d[Math.min(h,d.length-1)].duration;if(f>a){console.error(`[Augment Performance] Slow INP detected: ${f.toFixed(1)} ms`);let p=null;const l=d[0];l&&"target"in l&&(p=l.target,console.error("[Augment Performance] Slow interaction target:",p,l));const m=new Fc(f,a,p?String(p):null,window.location.href);wt(_=>{_.setTag("performance_issue","slow_inp"),_.setTag("inp_value",f.toFixed(1)),_.setTag("webview_url",window.location.href),p&&_.setTag("interaction_target",String(p)),_.setExtra("performance_data",{inp:f,threshold:a,target:p}),_.setLevel("warning"),Kn(m)}),f>o&&(o=f)}})(c)}).observe({entryTypes:["event","first-input"],buffered:!0})}catch(c){console.error("[Augment Performance] Error setting up INP monitoring:",c)}else console.warn("[Augment Performance] PerformanceObserver not supported for INP monitoring");window.augmentPerformance.getFramerate=()=>r,window.augmentPerformance.getWorstINP=()=>o}const qs=16,Us=200;function Bs(){var t;return((t=window.augmentFlags)==null?void 0:t.enablePerformanceMonitoring)??!1}let zs=!1;function Uc(t){let e,n;return{c(){e=Xe("svg"),n=Xe("path"),F(n,"fill-rule","evenodd"),F(n,"clip-rule","evenodd"),F(n,"d","M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z"),F(n,"fill","currentColor"),F(e,"width","15"),F(e,"height","15"),F(e,"viewBox","0 0 15 15"),F(e,"fill","none"),F(e,"xmlns","http://www.w3.org/2000/svg")},m(r,s){Le(r,e,s),Ze(e,n)},p:se,i:se,o:se,d(r){r&&ce(e)}}}(function(){var n,r;const t=!!((r=(n=window.augmentFlags)==null?void 0:n.sentry)!=null&&r.enabled);var e;(e={enabled:Bs(),lowFramerateThreshold:qs,slowInpThreshold:Us}).enabled&&qc({lowFramerateThreshold:e.lowFramerateThreshold||qs,slowInpThreshold:e.slowInpThreshold||Us}),Bs()&&!t&&console.warn("[Augment Performance] Performance monitoring enabled but Sentry is not initialized. Performance issues will not be reported to Sentry.")})(),function(){var e,n;if(!((n=(e=window.augmentFlags)==null?void 0:e.sentry)!=null&&n.enabled))return;const t=window.augmentFlags.sentry;if(t)if(zs)console.warn("Sentry is already initialized, duplicate initialization attempt");else try{(function(r){const s={...r};Lo(s,"svelte"),Ac(s)})({dsn:t.dsn,release:t.release,environment:t.environment,tracesSampleRate:t.tracesSampleRate||0,replaysSessionSampleRate:t.replaysSessionSampleRate||0,replaysOnErrorSampleRate:t.replaysOnErrorSampleRate||0,sampleRate:t.errorSampleRate||0,sendDefaultPii:t.sendDefaultPii!==void 0&&t.sendDefaultPii,integrations:(()=>{const r=[];return t.tracesSampleRate&&t.tracesSampleRate>0&&r.push(Mc()),r})(),beforeSend:r=>r}),t.tags&&Object.entries(t.tags).forEach(([r,s])=>{(function(o,i){Ie().setTag(o,i)})(r,String(s))}),zs=!0}catch(r){console.error("Failed to initialize Sentry:",r)}else console.warn("Sentry configuration not found in window.augmentDeps")}();class cu extends it{constructor(e){super(),at(this,e,null,Uc,ct,{})}}function Bc(t){let e;const n=t[7].default,r=mi(n,t,t[17],null);return{c(){r&&r.c()},m(s,o){r&&r.m(s,o),e=!0},p(s,o){r&&r.p&&(!e||131072&o)&&gi(r,n,s,s[17],e?yi(n,s[17],o,null):_i(s[17]),null)},i(s){e||(Ws(r,s),e=!0)},o(s){Gs(r,s),e=!1},d(s){r&&r.d(s)}}}function zc(t){let e,n,r;const s=[{size:t[6]},{variant:Hc},{color:t[0]},{highContrast:t[1]},{disabled:t[2]},{class:`c-badge-icon-btn__base-btn ${t[4]}`},t[3]];let o={$$slots:{default:[Bc]},$$scope:{ctx:t}};for(let i=0;i<s.length;i+=1)o=_e(o,s[i]);return n=new Ei({props:o}),n.$on("click",t[8]),n.$on("keyup",t[9]),n.$on("keydown",t[10]),n.$on("mousedown",t[11]),n.$on("mouseover",t[12]),n.$on("focus",t[13]),n.$on("mouseleave",t[14]),n.$on("blur",t[15]),n.$on("contextmenu",t[16]),{c(){e=_t("div"),ui(n.$$.fragment),F(e,"class",di(`c-badge-icon-btn c-badge-icon-btn--${t[5].variant} c-badge-icon-btn--size-${t[6]}`)+" svelte-1im94um")},m(i,a){Le(i,e,a),li(n,e,null),r=!0},p(i,[a]){const c=95&a?ar(s,[64&a&&{size:i[6]},0,1&a&&{color:i[0]},2&a&&{highContrast:i[1]},4&a&&{disabled:i[2]},16&a&&{class:`c-badge-icon-btn__base-btn ${i[4]}`},8&a&&pi(i[3])]):{};131072&a&&(c.$$scope={dirty:a,ctx:i}),n.$set(c)},i(i){r||(Ws(n.$$.fragment,i),r=!0)},o(i){Gs(n.$$.fragment,i),r=!1},d(i){i&&ce(e),fi(n)}}}let Hc="ghost";function Wc(t,e){return typeof t=="string"&&["accent","neutral","error","success","warning","info"].includes(t)?t:e}function Gc(t,e,n){let r,s;const o=["color","highContrast","disabled"];let i=wr(e,o),{$$slots:a={},$$scope:c}=e;const u=hi(Si.CONTEXT_KEY);let{color:d=Wc(u.color,"neutral")}=e,{highContrast:h=!1}=e,{disabled:f=!1}=e,p=u.size===0?.5:u.size;return t.$$set=l=>{e=_e(_e({},e),yt(l)),n(18,i=wr(e,o)),"color"in l&&n(0,d=l.color),"highContrast"in l&&n(1,h=l.highContrast),"disabled"in l&&n(2,f=l.disabled),"$$scope"in l&&n(17,c=l.$$scope)},t.$$.update=()=>{n(4,{class:r,...s}=i,r,(n(3,s),n(18,i)))},[d,h,f,s,r,u,p,a,function(l){de.call(this,t,l)},function(l){de.call(this,t,l)},function(l){de.call(this,t,l)},function(l){de.call(this,t,l)},function(l){de.call(this,t,l)},function(l){de.call(this,t,l)},function(l){de.call(this,t,l)},function(l){de.call(this,t,l)},function(l){de.call(this,t,l)},c]}const uu={Root:wi,IconButton:class extends it{constructor(t){super(),at(this,t,Gc,zc,ct,{color:0,highContrast:1,disabled:2})}}};function Hs(t){let e,n,r,s,o,i=(t[5]||"")+"",a=(t[4]||"")+"";return{c(){e=_t("span"),n=Er(i),r=Js(),s=_t("span"),o=Er(a),F(e,"class","c-toggle-text c-toggle-text--off svelte-xr5g0k"),te(e,"visible",!t[0]&&t[5]),F(s,"class","c-toggle-text c-toggle-text--on svelte-xr5g0k"),te(s,"visible",t[0]&&t[4])},m(c,u){Le(c,e,u),Ze(e,n),Le(c,r,u),Le(c,s,u),Ze(s,o)},p(c,u){32&u&&i!==(i=(c[5]||"")+"")&&xr(n,i),33&u&&te(e,"visible",!c[0]&&c[5]),16&u&&a!==(a=(c[4]||"")+"")&&xr(o,a),17&u&&te(s,"visible",c[0]&&c[4])},d(c){c&&(ce(e),ce(r),ce(s))}}}function Jc(t){let e,n,r,s,o,i,a=t[6]&&Hs(t);return{c(){e=_t("label"),a&&a.c(),n=Js(),r=_t("input"),F(r,"type","checkbox"),F(r,"class","c-toggle-input svelte-xr5g0k"),r.disabled=t[1],F(r,"aria-label",t[3]),F(r,"role","switch"),te(r,"disabled",t[1]),F(e,"class",s="c-toggle-track c-toggle-track-size--"+t[2]+" svelte-xr5g0k"),te(e,"checked",t[0]),te(e,"disabled",t[1]),te(e,"has-text",t[6])},m(c,u){Le(c,e,u),a&&a.m(e,null),Ze(e,n),Ze(e,r),r.checked=t[0],o||(i=[_n(r,"change",t[9]),_n(r,"keydown",t[7]),_n(e,"change",t[8])],o=!0)},p(c,[u]){c[6]?a?a.p(c,u):(a=Hs(c),a.c(),a.m(e,n)):a&&(a.d(1),a=null),2&u&&(r.disabled=c[1]),8&u&&F(r,"aria-label",c[3]),1&u&&(r.checked=c[0]),2&u&&te(r,"disabled",c[1]),4&u&&s!==(s="c-toggle-track c-toggle-track-size--"+c[2]+" svelte-xr5g0k")&&F(e,"class",s),5&u&&te(e,"checked",c[0]),6&u&&te(e,"disabled",c[1]),68&u&&te(e,"has-text",c[6])},i:se,o:se,d(c){c&&ce(e),a&&a.d(),o=!1,vi(i)}}}function Yc(t,e,n){let r,{checked:s=!1}=e,{disabled:o=!1}=e,{size:i=2}=e,{ariaLabel:a}=e,{onText:c}=e,{offText:u}=e;return t.$$set=d=>{"checked"in d&&n(0,s=d.checked),"disabled"in d&&n(1,o=d.disabled),"size"in d&&n(2,i=d.size),"ariaLabel"in d&&n(3,a=d.ariaLabel),"onText"in d&&n(4,c=d.onText),"offText"in d&&n(5,u=d.offText)},t.$$.update=()=>{48&t.$$.dirty&&n(6,r=c||u)},[s,o,i,a,c,u,r,function(d){o||d.key!=="Enter"&&d.key!==" "||(d.preventDefault(),n(0,s=!s))},function(d){de.call(this,t,d)},function(){s=this.checked,n(0,s)}]}class du extends it{constructor(e){super(),at(this,e,Yc,Jc,ct,{checked:0,disabled:1,size:2,ariaLabel:3,onText:4,offText:5})}}const jn={enabled:!1,volume:.5},lu={enabled:!0},Vc=""+new URL("agent-complete-DO0gyADk.mp3",import.meta.url).href;var oi=(t=>(t.AGENT_COMPLETE="agent-complete",t))(oi||{});const Kc={"agent-complete":Vc},Re=class Re{constructor(){Y(this,"audioCache",new Map)}static getInstance(){return Re._instance||(Re._instance=new Re),Re._instance}retrieveAudioElement(e,n){let r=this.audioCache.get(e);return r?r.volume=n.volume:(r=new Audio,r.src=Kc[e],r.volume=n.volume,r.preload="auto",r._isUnlocked=!1,this.audioCache.set(e,r)),r}async playSound(e,n){if(n.enabled)try{const r=this.retrieveAudioElement(e,n);r.currentTime=0,await r.play()}catch(r){if(r instanceof DOMException&&r.name==="NotAllowedError")return void console.error("Audio blocked by browser policy. Sound will work after user interaction.");console.error("Failed to play sound:",r)}}async unlockSoundForConfig(e){if(!e.enabled)return;const n=this.retrieveAudioElement("agent-complete",e);if(!n._isUnlocked)try{await this.playSound("agent-complete",{enabled:!0,volume:0}),n._isUnlocked=!0}catch(r){console.warn("Failed to unlock sound:",r)}}disposeSounds(){this.audioCache.forEach(e=>{e.pause(),e.src="",e._isUnlocked=!1}),this.audioCache.clear()}};Y(Re,"_instance");let ir=Re;const Fn=ir.getInstance();class Xc{constructor(e){Y(this,"_soundSettings",bi(jn));Y(this,"_isLoaded",!1);Y(this,"dispose",()=>{Fn.disposeSounds()});this._msgBroker=e,this.initialize()}async refreshSettings(){try{const e=await this._msgBroker.sendToSidecar({type:vn.getSoundSettings});e.data&&this._soundSettings.set(e.data)}catch(e){console.warn("Failed to refresh sound settings:",e)}}async unlockSound(){yn(this._soundSettings).enabled&&Fn.unlockSoundForConfig(yn(this._soundSettings))}async playAgentComplete(){const e=yn(this._soundSettings);await Fn.playSound(oi.AGENT_COMPLETE,e)}get getCurrentSettings(){return this._soundSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:vn.getSoundSettings});e.data&&this._soundSettings.set(e.data),this._isLoaded=!0}catch(e){console.warn("Failed to load sound settings, using defaults:",e),this._soundSettings.set(jn),this._isLoaded=!0}}async updateSettings(e){try{await this._msgBroker.sendToSidecar({type:vn.updateSoundSettings,data:e}),this._soundSettings.update(n=>({...n,...e}))}catch(n){throw console.error("Failed to update sound settings:",n),n}}async setEnabled(e){await this.updateSettings({enabled:e})}async setVolume(e){const n=Math.max(0,Math.min(1,e));await this.updateSettings({volume:n})}async resetToDefaults(){await this.updateSettings(jn)}updateEnabled(e){this.setEnabled(e).catch(n=>{console.error("Failed to update enabled setting:",n)})}updateVolume(e){this.setVolume(e).catch(n=>{console.error("Failed to update volume setting:",n)})}}Y(Xc,"key","soundModel");function Zc(t){let e,n,r=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},t[0]],s={};for(let o=0;o<r.length;o+=1)s=_e(s,r[o]);return{c(){e=Xe("svg"),n=new Ys(!0),this.h()},l(o){e=Vs(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Ks(e);n=Xs(i,!0),i.forEach(ce),this.h()},h(){n.a=null,Xt(e,s)},m(o,i){Zs(o,e,i),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248 72c0-13.3-10.7-24-24-24s-24 10.7-24 24v160H40c-13.3 0-24 10.7-24 24s10.7 24 24 24h160v160c0 13.3 10.7 24 24 24s24-10.7 24-24V280h160c13.3 0 24-10.7 24-24s-10.7-24-24-24H248z"/>',e)},p(o,[i]){Xt(e,s=ar(r,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&i&&o[0]]))},i:se,o:se,d(o){o&&ce(e)}}}function Qc(t,e,n){return t.$$set=r=>{n(0,e=_e(_e({},e),yt(r)))},[e=yt(e)]}class pu extends it{constructor(e){super(),at(this,e,Qc,Zc,ct,{})}}function eu(t){let e,n,r=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},t[0]],s={};for(let o=0;o<r.length;o+=1)s=_e(s,r[o]);return{c(){e=Xe("svg"),n=new Ys(!0),this.h()},l(o){e=Vs(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Ks(e);n=Xs(i,!0),i.forEach(ce),this.h()},h(){n.a=null,Xt(e,s)},m(o,i){Zs(o,e,i),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M280 24c0-13.3-10.7-24-24-24s-24 10.7-24 24v270.1l-95-95c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 369c9.4 9.4 24.6 9.4 33.9 0L409 233c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-95 95zM128.8 304H64c-35.3 0-64 28.7-64 64v80c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-80c0-35.3-28.7-64-64-64h-64.8l-48 48H448c8.8 0 16 7.2 16 16v80c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-80c0-8.8 7.2-16 16-16h112.8zM432 408a24 24 0 1 0-48 0 24 24 0 1 0 48 0"/>',e)},p(o,[i]){Xt(e,s=ar(r,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&o[0]]))},i:se,o:se,d(o){o&&ce(e)}}}function tu(t,e,n){return t.$$set=r=>{n(0,e=_e(_e({},e),yt(r)))},[e=yt(e)]}class fu extends it{constructor(e){super(),at(this,e,tu,eu,ct,{})}}function nu(t){let e,n;return{c(){e=Xe("svg"),n=Xe("path"),F(n,"fill-rule","evenodd"),F(n,"clip-rule","evenodd"),F(n,"d","M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z"),F(n,"fill","currentColor"),F(e,"width","15"),F(e,"height","15"),F(e,"viewBox","0 0 15 15"),F(e,"fill","none"),F(e,"xmlns","http://www.w3.org/2000/svg")},m(r,s){Le(r,e,s),Ze(e,n)},p:se,i:se,o:se,d(r){r&&ce(e)}}}class hu extends it{constructor(e){super(),at(this,e,null,nu,ct,{})}}export{uu as B,cu as C,jn as D,hu as G,pu as P,oi as S,du as T,lu as a,fu as b,Xc as c,Fn as s};

import{S as q,i as B,s as L,X as H,E as O,F as P,w as d,u as m,G as R,I as T,N as U,f as y,k as z,a9 as N,a as W,D as b,V as _,Y as M,d as k,aa as F,h as w,Q as nn,ab as an,t as j,v as D,Z as Q,j as en,J as V,K as X,L as Y,M as Z,a0 as E}from"./SpinnerAugment-uKUHz-bK.js";import{n as tn,g as cn,a as ln}from"./file-paths-CAgP5Fvb.js";const on=e=>({}),S=e=>({}),pn=e=>({}),A=e=>({});function G(e){let n;const l=e[11].leftIcon,c=V(l,e,e[12],A);return{c(){c&&c.c()},m(a,t){c&&c.m(a,t),n=!0},p(a,t){c&&c.p&&(!n||4096&t)&&X(c,l,a,a[12],n?Z(l,a[12],t,pn):Y(a[12]),A)},i(a){n||(d(c,a),n=!0)},o(a){m(c,a),n=!1},d(a){c&&c.d(a)}}}function J(e){let n,l,c;return{c(){n=b("div"),l=b("div"),c=M(e[6]),k(l,"class","c-filespan__dir-text svelte-9pfhnp"),k(n,"class","c-filespan__dir svelte-9pfhnp"),E(n,"growname",e[3])},m(a,t){y(a,n,t),w(n,l),w(l,c)},p(a,t){64&t&&Q(c,a[6]),8&t&&E(n,"growname",a[3])},d(a){a&&z(n)}}}function K(e){let n,l;const c=e[11].rightIcon,a=V(c,e,e[12],S);return{c(){n=b("span"),a&&a.c(),k(n,"class","right-icons svelte-9pfhnp")},m(t,s){y(t,n,s),a&&a.m(n,null),l=!0},p(t,s){a&&a.p&&(!l||4096&s)&&X(a,c,t,t[12],l?Z(c,t[12],s,on):Y(t[12]),S)},i(t){l||(d(a,t),l=!0)},o(t){m(a,t),l=!1},d(t){t&&z(n),a&&a.d(t)}}}function C(e){let n,l,c,a,t,s,h,g,u,x,I,i=e[8].leftIcon&&G(e),r=!e[2]&&J(e),o=e[8].rightIcon&&K(e),v=[{class:h=N(`c-filespan ${e[0]}`)+" svelte-9pfhnp"},{role:g=e[4]?"button":""},{tabindex:"0"}],p={};for(let $=0;$<v.length;$+=1)p=W(p,v[$]);return{c(){n=b(e[5]),i&&i.c(),l=_(),c=b("span"),a=M(e[7]),t=_(),r&&r.c(),s=_(),o&&o.c(),k(c,"class","c-filespan__filename svelte-9pfhnp"),F(e[5])(n,p)},m($,f){y($,n,f),i&&i.m(n,null),w(n,l),w(n,c),w(c,a),w(n,t),r&&r.m(n,null),w(n,s),o&&o.m(n,null),u=!0,x||(I=nn(n,"click",function(){an(e[4])&&e[4].apply(this,arguments)}),x=!0)},p($,f){(e=$)[8].leftIcon?i?(i.p(e,f),256&f&&d(i,1)):(i=G(e),i.c(),d(i,1),i.m(n,l)):i&&(j(),m(i,1,1,()=>{i=null}),D()),(!u||128&f)&&Q(a,e[7]),e[2]?r&&(r.d(1),r=null):r?r.p(e,f):(r=J(e),r.c(),r.m(n,s)),e[8].rightIcon?o?(o.p(e,f),256&f&&d(o,1)):(o=K(e),o.c(),d(o,1),o.m(n,null)):o&&(j(),m(o,1,1,()=>{o=null}),D()),F(e[5])(n,p=en(v,[(!u||1&f&&h!==(h=N(`c-filespan ${e[0]}`)+" svelte-9pfhnp"))&&{class:h},(!u||16&f&&g!==(g=e[4]?"button":""))&&{role:g},{tabindex:"0"}]))},i($){u||(d(i),d(o),u=!0)},o($){m(i),m(o),u=!1},d($){$&&z(n),i&&i.d(),r&&r.d(),o&&o.d(),x=!1,I()}}}function rn(e){let n,l,c=e[5],a=e[5]&&C(e);return{c(){a&&a.c(),n=U()},m(t,s){a&&a.m(t,s),y(t,n,s),l=!0},p(t,s){t[5]?c?L(c,t[5])?(a.d(1),a=C(t),c=t[5],a.c(),a.m(n.parentNode,n)):a.p(t,s):(a=C(t),c=t[5],a.c(),a.m(n.parentNode,n)):c&&(a.d(1),a=null,c=t[5])},i(t){l||(d(a,t),l=!0)},o(t){m(a,t),l=!1},d(t){t&&z(n),a&&a.d(t)}}}function sn(e){let n,l;return n=new H({props:{size:e[1],$$slots:{default:[rn]},$$scope:{ctx:e}}}),{c(){O(n.$$.fragment)},m(c,a){P(n,c,a),l=!0},p(c,[a]){const t={};2&a&&(t.size=c[1]),4605&a&&(t.$$scope={dirty:a,ctx:c}),n.$set(t)},i(c){l||(d(n.$$.fragment,c),l=!0)},o(c){m(n.$$.fragment,c),l=!1},d(c){R(n,c)}}}function $n(e,n,l){let c,a,t,s,{$$slots:h={},$$scope:g}=n;const u=T(h);let{class:x=""}=n,{filepath:I}=n,{size:i=1}=n,{nopath:r=!1}=n,{growname:o=!0}=n,{onClick:v}=n;return e.$$set=p=>{"class"in p&&l(0,x=p.class),"filepath"in p&&l(9,I=p.filepath),"size"in p&&l(1,i=p.size),"nopath"in p&&l(2,r=p.nopath),"growname"in p&&l(3,o=p.growname),"onClick"in p&&l(4,v=p.onClick),"$$scope"in p&&l(12,g=p.$$scope)},e.$$.update=()=>{512&e.$$.dirty&&l(10,c=tn(I)),1024&e.$$.dirty&&l(7,a=cn(c)),1024&e.$$.dirty&&l(6,t=ln(c)),16&e.$$.dirty&&l(5,s=v?"button":"div")},[x,i,r,o,v,s,t,a,u,I,c,h,g]}class un extends q{constructor(n){super(),B(this,n,$n,sn,L,{class:0,filepath:9,size:1,nopath:2,growname:3,onClick:4})}}export{un as F};

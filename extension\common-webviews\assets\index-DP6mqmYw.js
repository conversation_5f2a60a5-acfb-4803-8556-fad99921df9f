var ro=Object.defineProperty;var oo=(e,t,n)=>t in e?ro(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var T=(e,t,n)=>oo(e,typeof t!="symbol"?t+"":t,n);import{R as se}from"./message-broker-DdVtH9Vr.js";import{g as ao,S as Y,i as G,s as V,b as Mt,d as I,f as k,h as Gs,n as re,k as S,C as Vs,ax as Ks,E as A,F as D,Q as te,w as y,u as v,G as x,ak as oe,am as Bs,aj as B,D as H,a4 as io,W as le,J as j,a9 as ae,ae as js,K as q,L as X,M as W,aB as je,a as _,j as pe,af as me,a1 as K,I as qs,l as ie,X as Xs,V as Ue,N as Ws,t as Le,v as Pe,H as uo,y as lo,z as co,A as mo,e as wn,B as po,a5 as zt,a0 as fe,an as fo,O as go,$ as $n,ad as Sn,T as ho,a6 as bo,a7 as yo}from"./SpinnerAugment-uKUHz-bK.js";import{b as Eo,c as vo,e as Ve,C as No,R as To,a as Yt,f as zs}from"./CardAugment-BqjOeIg4.js";import{B as Io}from"./IconButtonAugment-CQzh_Hae.js";import{B as _o}from"./BaseTextInput-BTYl5feP.js";function Ht(e,t){return!(e===null||typeof e!="object"||!("$typeName"in e)||typeof e.$typeName!="string")&&(t===void 0||t.typeName===e.$typeName)}var d;function wo(){let e=0,t=0;for(let s=0;s<28;s+=7){let o=this.buf[this.pos++];if(e|=(127&o)<<s,!(128&o))return this.assertBounds(),[e,t]}let n=this.buf[this.pos++];if(e|=(15&n)<<28,t=(112&n)>>4,!(128&n))return this.assertBounds(),[e,t];for(let s=3;s<=31;s+=7){let o=this.buf[this.pos++];if(t|=(127&o)<<s,!(128&o))return this.assertBounds(),[e,t]}throw new Error("invalid varint")}function It(e,t,n){for(let r=0;r<28;r+=7){const a=e>>>r,i=!(!(a>>>7)&&t==0),u=255&(i?128|a:a);if(n.push(u),!i)return}const s=e>>>28&15|(7&t)<<4,o=!!(t>>3);if(n.push(255&(o?128|s:s)),o){for(let r=3;r<31;r+=7){const a=t>>>r,i=!!(a>>>7),u=255&(i?128|a:a);if(n.push(u),!i)return}n.push(t>>>31&1)}}(function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"})(d||(d={}));const Qe=4294967296;function On(e){const t=e[0]==="-";t&&(e=e.slice(1));const n=1e6;let s=0,o=0;function r(a,i){const u=Number(e.slice(a,i));o*=n,s=s*n+u,s>=Qe&&(o+=s/Qe|0,s%=Qe)}return r(-24,-18),r(-18,-12),r(-12,-6),r(-6),t?Hs(s,o):Jt(s,o)}function kn(e,t){if({lo:e,hi:t}=function(u,l){return{lo:u>>>0,hi:l>>>0}}(e,t),t<=2097151)return String(Qe*t+e);const n=16777215&(e>>>24|t<<8),s=t>>16&65535;let o=(16777215&e)+6777216*n+6710656*s,r=n+8147497*s,a=2*s;const i=1e7;return o>=i&&(r+=Math.floor(o/i),o%=i),r>=i&&(a+=Math.floor(r/i),r%=i),a.toString()+Rn(r)+Rn(o)}function Jt(e,t){return{lo:0|e,hi:0|t}}function Hs(e,t){return t=~t,e?e=1+~e:t+=1,Jt(e,t)}const Rn=e=>{const t=String(e);return"0000000".slice(t.length)+t};function An(e,t){if(e>=0){for(;e>127;)t.push(127&e|128),e>>>=7;t.push(e)}else{for(let n=0;n<9;n++)t.push(127&e|128),e>>=7;t.push(1)}}function $o(){let e=this.buf[this.pos++],t=127&e;if(!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<7,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<14,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<21,!(128&e))return this.assertBounds(),t;e=this.buf[this.pos++],t|=(15&e)<<28;for(let n=5;128&e&&n<10;n++)e=this.buf[this.pos++];if(128&e)throw new Error("invalid varint");return this.assertBounds(),t>>>0}var Dn={};const $=So();function So(){const e=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof e.getBigInt64=="function"&&typeof e.getBigUint64=="function"&&typeof e.setBigInt64=="function"&&typeof e.setBigUint64=="function"&&(typeof process!="object"||typeof Dn!="object"||Dn.BUF_BIGINT_DISABLE!=="1")){const t=BigInt("-9223372036854775808"),n=BigInt("9223372036854775807"),s=BigInt("0"),o=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(r){const a=typeof r=="bigint"?r:BigInt(r);if(a>n||a<t)throw new Error(`invalid int64: ${r}`);return a},uParse(r){const a=typeof r=="bigint"?r:BigInt(r);if(a>o||a<s)throw new Error(`invalid uint64: ${r}`);return a},enc(r){return e.setBigInt64(0,this.parse(r),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},uEnc(r){return e.setBigInt64(0,this.uParse(r),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},dec:(r,a)=>(e.setInt32(0,r,!0),e.setInt32(4,a,!0),e.getBigInt64(0,!0)),uDec:(r,a)=>(e.setInt32(0,r,!0),e.setInt32(4,a,!0),e.getBigUint64(0,!0))}}return{zero:"0",supported:!1,parse:t=>(typeof t!="string"&&(t=t.toString()),xn(t),t),uParse:t=>(typeof t!="string"&&(t=t.toString()),Fn(t),t),enc:t=>(typeof t!="string"&&(t=t.toString()),xn(t),On(t)),uEnc:t=>(typeof t!="string"&&(t=t.toString()),Fn(t),On(t)),dec:(t,n)=>function(s,o){let r=Jt(s,o);const a=2147483648&r.hi;a&&(r=Hs(r.lo,r.hi));const i=kn(r.lo,r.hi);return a?"-"+i:i}(t,n),uDec:(t,n)=>kn(t,n)}}function xn(e){if(!/^-?[0-9]+$/.test(e))throw new Error("invalid int64: "+e)}function Fn(e){if(!/^[0-9]+$/.test(e))throw new Error("invalid uint64: "+e)}function Ne(e,t){switch(e){case d.STRING:return"";case d.BOOL:return!1;case d.DOUBLE:case d.FLOAT:return 0;case d.INT64:case d.UINT64:case d.SFIXED64:case d.FIXED64:case d.SINT64:return t?"0":$.zero;case d.BYTES:return new Uint8Array(0);default:return 0}}const de=Symbol.for("reflect unsafe local");function Js(e,t){const n=e[t.localName].case;return n===void 0?n:t.fields.find(s=>s.localName===n)}function Oo(e,t){const n=t.localName;if(t.oneof)return e[t.oneof.localName].case===n;if(t.presence!=2)return e[n]!==void 0&&Object.prototype.hasOwnProperty.call(e,n);switch(t.fieldKind){case"list":return e[n].length>0;case"map":return Object.keys(e[n]).length>0;case"scalar":return!function(s,o){switch(s){case d.BOOL:return o===!1;case d.STRING:return o==="";case d.BYTES:return o instanceof Uint8Array&&!o.byteLength;default:return o==0}}(t.scalar,e[n]);case"enum":return e[n]!==t.enum.values[0].number}throw new Error("message field with implicit presence")}function xe(e,t){return Object.prototype.hasOwnProperty.call(e,t)&&e[t]!==void 0}function Zs(e,t){if(t.oneof){const n=e[t.oneof.localName];return n.case===t.localName?n.value:void 0}return e[t.localName]}function Qs(e,t,n){t.oneof?e[t.oneof.localName]={case:t.localName,value:n}:e[t.localName]=n}function ye(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)}function Zt(e,t){var n,s,o,r;if(ye(e)&&de in e&&"add"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const a=t,i=e.field();return a.listKind==i.listKind&&a.scalar===i.scalar&&((n=a.message)===null||n===void 0?void 0:n.typeName)===((s=i.message)===null||s===void 0?void 0:s.typeName)&&((o=a.enum)===null||o===void 0?void 0:o.typeName)===((r=i.enum)===null||r===void 0?void 0:r.typeName)}return!0}return!1}function Qt(e,t){var n,s,o,r;if(ye(e)&&de in e&&"has"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const a=t,i=e.field();return a.mapKey===i.mapKey&&a.mapKind==i.mapKind&&a.scalar===i.scalar&&((n=a.message)===null||n===void 0?void 0:n.typeName)===((s=i.message)===null||s===void 0?void 0:s.typeName)&&((o=a.enum)===null||o===void 0?void 0:o.typeName)===((r=i.enum)===null||r===void 0?void 0:r.typeName)}return!0}return!1}function en(e,t){return ye(e)&&de in e&&"desc"in e&&ye(e.desc)&&e.desc.kind==="message"&&(t===void 0||e.desc.typeName==t.typeName)}function Ke(e){const t=e.fields[0];return er(e.typeName)&&t!==void 0&&t.fieldKind=="scalar"&&t.name=="value"&&t.number==1}function er(e){return e.startsWith("google.protobuf.")&&["DoubleValue","FloatValue","Int64Value","UInt64Value","Int32Value","UInt32Value","BoolValue","StringValue","BytesValue"].includes(e.substring(16))}const ko=999,Ro=998,qe=2;function ue(e,t){if(Ht(t,e))return t;const n=function(s){let o;if(function(r){switch(r.file.edition){case ko:return!1;case Ro:return!0;default:return r.fields.some(a=>a.presence!=qe&&a.fieldKind!="message"&&!a.oneof)}}(s)){const r=Un.get(s);let a,i;if(r)({prototype:a,members:i}=r);else{a={},i=new Set;for(const u of s.members)u.kind!="oneof"&&(u.fieldKind!="scalar"&&u.fieldKind!="enum"||u.presence!=qe&&(i.add(u),a[u.localName]=_t(u)));Un.set(s,{prototype:a,members:i})}o=Object.create(a),o.$typeName=s.typeName;for(const u of s.members)if(!i.has(u)){if(u.kind=="field"&&(u.fieldKind=="message"||(u.fieldKind=="scalar"||u.fieldKind=="enum")&&u.presence!=qe))continue;o[u.localName]=_t(u)}}else{o={$typeName:s.typeName};for(const r of s.members)r.kind!="oneof"&&r.presence!=qe||(o[r.localName]=_t(r))}return o}(e);return t!==void 0&&function(s,o,r){for(const a of s.members){let i,u=r[a.localName];if(u!=null){if(a.kind=="oneof"){const l=Js(r,a);if(!l)continue;i=l,u=Zs(r,l)}else i=a;switch(i.fieldKind){case"message":u=tn(i,u);break;case"scalar":u=tr(i,u);break;case"list":u=Do(i,u);break;case"map":u=Ao(i,u)}Qs(o,i,u)}}}(e,n,t),n}function tr(e,t){return e.scalar==d.BYTES?nn(t):t}function Ao(e,t){if(ye(t)){if(e.scalar==d.BYTES)return Cn(t,nn);if(e.mapKind=="message")return Cn(t,n=>tn(e,n))}return t}function Do(e,t){if(Array.isArray(t)){if(e.scalar==d.BYTES)return t.map(nn);if(e.listKind=="message")return t.map(n=>tn(e,n))}return t}function tn(e,t){if(e.fieldKind=="message"&&!e.oneof&&Ke(e.message))return tr(e.message.fields[0],t);if(ye(t)){if(e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!=="google.protobuf.Value")return t;if(!Ht(t,e.message))return ue(e.message,t)}return t}function nn(e){return Array.isArray(e)?new Uint8Array(e):e}function Cn(e,t){const n={};for(const s of Object.entries(e))n[s[0]]=t(s[1]);return n}const xo=Symbol(),Un=new WeakMap;function _t(e){if(e.kind=="oneof")return{case:void 0};if(e.fieldKind=="list")return[];if(e.fieldKind=="map")return{};if(e.fieldKind=="message")return xo;const t=e.getDefaultValue();return t!==void 0?e.fieldKind=="scalar"&&e.longAsString?t.toString():t:e.fieldKind=="scalar"?Ne(e.scalar,e.longAsString):e.enum.values[0].number}const Fo=["FieldValueInvalidError","FieldListRangeError","ForeignFieldError"];class z extends Error{constructor(t,n,s="FieldValueInvalidError"){super(n),this.name=s,this.field=()=>t}}const wt=Symbol.for("@bufbuild/protobuf/text-encoding");function sn(){if(globalThis[wt]==null){const e=new globalThis.TextEncoder,t=new globalThis.TextDecoder;globalThis[wt]={encodeUtf8:n=>e.encode(n),decodeUtf8:n=>t.decode(n),checkUtf8(n){try{return encodeURIComponent(n),!0}catch{return!1}}}}return globalThis[wt]}var O;(function(e){e[e.Varint=0]="Varint",e[e.Bit64=1]="Bit64",e[e.LengthDelimited=2]="LengthDelimited",e[e.StartGroup=3]="StartGroup",e[e.EndGroup=4]="EndGroup",e[e.Bit32=5]="Bit32"})(O||(O={}));const nr=34028234663852886e22,sr=-34028234663852886e22,rr=4294967295,or=2147483647,ar=-2147483648;class ir{constructor(t=sn().encodeUtf8){this.encodeUtf8=t,this.stack=[],this.chunks=[],this.buf=[]}finish(){this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]);let t=0;for(let o=0;o<this.chunks.length;o++)t+=this.chunks[o].length;let n=new Uint8Array(t),s=0;for(let o=0;o<this.chunks.length;o++)n.set(this.chunks[o],s),s+=this.chunks[o].length;return this.chunks=[],n}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let t=this.finish(),n=this.stack.pop();if(!n)throw new Error("invalid state, fork stack empty");return this.chunks=n.chunks,this.buf=n.buf,this.uint32(t.byteLength),this.raw(t)}tag(t,n){return this.uint32((t<<3|n)>>>0)}raw(t){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(t),this}uint32(t){for(Ln(t);t>127;)this.buf.push(127&t|128),t>>>=7;return this.buf.push(t),this}int32(t){return $t(t),An(t,this.buf),this}bool(t){return this.buf.push(t?1:0),this}bytes(t){return this.uint32(t.byteLength),this.raw(t)}string(t){let n=this.encodeUtf8(t);return this.uint32(n.byteLength),this.raw(n)}float(t){(function(s){if(typeof s=="string"){const o=s;if(s=Number(s),Number.isNaN(s)&&o!=="NaN")throw new Error("invalid float32: "+o)}else if(typeof s!="number")throw new Error("invalid float32: "+typeof s);if(Number.isFinite(s)&&(s>nr||s<sr))throw new Error("invalid float32: "+s)})(t);let n=new Uint8Array(4);return new DataView(n.buffer).setFloat32(0,t,!0),this.raw(n)}double(t){let n=new Uint8Array(8);return new DataView(n.buffer).setFloat64(0,t,!0),this.raw(n)}fixed32(t){Ln(t);let n=new Uint8Array(4);return new DataView(n.buffer).setUint32(0,t,!0),this.raw(n)}sfixed32(t){$t(t);let n=new Uint8Array(4);return new DataView(n.buffer).setInt32(0,t,!0),this.raw(n)}sint32(t){return $t(t),An(t=(t<<1^t>>31)>>>0,this.buf),this}sfixed64(t){let n=new Uint8Array(8),s=new DataView(n.buffer),o=$.enc(t);return s.setInt32(0,o.lo,!0),s.setInt32(4,o.hi,!0),this.raw(n)}fixed64(t){let n=new Uint8Array(8),s=new DataView(n.buffer),o=$.uEnc(t);return s.setInt32(0,o.lo,!0),s.setInt32(4,o.hi,!0),this.raw(n)}int64(t){let n=$.enc(t);return It(n.lo,n.hi,this.buf),this}sint64(t){const n=$.enc(t),s=n.hi>>31;return It(n.lo<<1^s,(n.hi<<1|n.lo>>>31)^s,this.buf),this}uint64(t){const n=$.uEnc(t);return It(n.lo,n.hi,this.buf),this}}class rn{constructor(t,n=sn().decodeUtf8){this.decodeUtf8=n,this.varint64=wo,this.uint32=$o,this.buf=t,this.len=t.length,this.pos=0,this.view=new DataView(t.buffer,t.byteOffset,t.byteLength)}tag(){let t=this.uint32(),n=t>>>3,s=7&t;if(n<=0||s<0||s>5)throw new Error("illegal tag: field no "+n+" wire type "+s);return[n,s]}skip(t,n){let s=this.pos;switch(t){case O.Varint:for(;128&this.buf[this.pos++];);break;case O.Bit64:this.pos+=4;case O.Bit32:this.pos+=4;break;case O.LengthDelimited:let o=this.uint32();this.pos+=o;break;case O.StartGroup:for(;;){const[r,a]=this.tag();if(a===O.EndGroup){if(n!==void 0&&r!==n)throw new Error("invalid end group tag");break}this.skip(a,r)}break;default:throw new Error("cant skip wire type "+t)}return this.assertBounds(),this.buf.subarray(s,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let t=this.uint32();return t>>>1^-(1&t)}int64(){return $.dec(...this.varint64())}uint64(){return $.uDec(...this.varint64())}sint64(){let[t,n]=this.varint64(),s=-(1&t);return t=(t>>>1|(1&n)<<31)^s,n=n>>>1^s,$.dec(t,n)}bool(){let[t,n]=this.varint64();return t!==0||n!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return $.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return $.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let t=this.uint32(),n=this.pos;return this.pos+=t,this.assertBounds(),this.buf.subarray(n,n+t)}string(){return this.decodeUtf8(this.bytes())}}function $t(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid int32: "+typeof e);if(!Number.isInteger(e)||e>or||e<ar)throw new Error("invalid int32: "+e)}function Ln(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid uint32: "+typeof e);if(!Number.isInteger(e)||e>rr||e<0)throw new Error("invalid uint32: "+e)}function Ee(e,t){const n=e.fieldKind=="list"?Zt(t,e):e.fieldKind=="map"?Qt(t,e):on(e,t);if(n===!0)return;let s;switch(e.fieldKind){case"list":s=`expected ${cr(e)}, got ${R(t)}`;break;case"map":s=`expected ${dr(e)}, got ${R(t)}`;break;default:s=rt(e,t,n)}return new z(e,s)}function Pn(e,t,n){const s=on(e,n);if(s!==!0)return new z(e,`list item #${t+1}: ${rt(e,n,s)}`)}function on(e,t){return e.scalar!==void 0?ur(t,e.scalar):e.enum!==void 0?e.enum.open?Number.isInteger(t):e.enum.values.some(n=>n.number===t):en(t,e.message)}function ur(e,t){switch(t){case d.DOUBLE:return typeof e=="number";case d.FLOAT:return typeof e=="number"&&(!(!Number.isNaN(e)&&Number.isFinite(e))||!(e>nr||e<sr)||`${e.toFixed()} out of range`);case d.INT32:case d.SFIXED32:case d.SINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>or||e<ar)||`${e.toFixed()} out of range`);case d.FIXED32:case d.UINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>rr||e<0)||`${e.toFixed()} out of range`);case d.BOOL:return typeof e=="boolean";case d.STRING:return typeof e=="string"&&(sn().checkUtf8(e)||"invalid UTF8");case d.BYTES:return e instanceof Uint8Array;case d.INT64:case d.SFIXED64:case d.SINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return $.parse(e),!0}catch{return`${e} out of range`}return!1;case d.FIXED64:case d.UINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return $.uParse(e),!0}catch{return`${e} out of range`}return!1}}function rt(e,t,n){return n=typeof n=="string"?`: ${n}`:`, got ${R(t)}`,e.scalar!==void 0?`expected ${function(s){switch(s){case d.STRING:return"string";case d.BOOL:return"boolean";case d.INT64:case d.SINT64:case d.SFIXED64:return"bigint (int64)";case d.UINT64:case d.FIXED64:return"bigint (uint64)";case d.BYTES:return"Uint8Array";case d.DOUBLE:return"number (float64)";case d.FLOAT:return"number (float32)";case d.FIXED32:case d.UINT32:return"number (uint32)";case d.INT32:case d.SFIXED32:case d.SINT32:return"number (int32)"}}(e.scalar)}`+n:e.enum!==void 0?`expected ${e.enum.toString()}`+n:`expected ${lr(e.message)}`+n}function R(e){switch(typeof e){case"object":return e===null?"null":e instanceof Uint8Array?`Uint8Array(${e.length})`:Array.isArray(e)?`Array(${e.length})`:Zt(e)?cr(e.field()):Qt(e)?dr(e.field()):en(e)?lr(e.desc):Ht(e)?`message ${e.$typeName}`:"object";case"string":return e.length>30?"string":`"${e.split('"').join('\\"')}"`;case"boolean":case"number":return String(e);case"bigint":return String(e)+"n";default:return typeof e}}function lr(e){return`ReflectMessage (${e.typeName})`}function cr(e){switch(e.listKind){case"message":return`ReflectList (${e.message.toString()})`;case"enum":return`ReflectList (${e.enum.toString()})`;case"scalar":return`ReflectList (${d[e.scalar]})`}}function dr(e){switch(e.mapKind){case"message":return`ReflectMap (${d[e.mapKey]}, ${e.message.toString()})`;case"enum":return`ReflectMap (${d[e.mapKey]}, ${e.enum.toString()})`;case"scalar":return`ReflectMap (${d[e.mapKey]}, ${d[e.scalar]})`}}function Q(e,t,n=!0){return new mr(e,t,n)}class mr{get sortedFields(){var t;return(t=this._sortedFields)!==null&&t!==void 0?t:this._sortedFields=this.desc.fields.concat().sort((n,s)=>n.number-s.number)}constructor(t,n,s=!0){this.lists=new Map,this.maps=new Map,this.check=s,this.desc=t,this.message=this[de]=n??ue(t),this.fields=t.fields,this.oneofs=t.oneofs,this.members=t.members}findNumber(t){return this._fieldsByNumber||(this._fieldsByNumber=new Map(this.desc.fields.map(n=>[n.number,n]))),this._fieldsByNumber.get(t)}oneofCase(t){return Re(this.message,t),Js(this.message,t)}isSet(t){return Re(this.message,t),Oo(this.message,t)}clear(t){Re(this.message,t),function(n,s){const o=s.localName;if(s.oneof){const r=s.oneof.localName;n[r].case===o&&(n[r]={case:void 0})}else if(s.presence!=2)delete n[o];else switch(s.fieldKind){case"map":n[o]={};break;case"list":n[o]=[];break;case"enum":n[o]=s.enum.values[0].number;break;case"scalar":n[o]=Ne(s.scalar,s.longAsString)}}(this.message,t)}get(t){Re(this.message,t);const n=Zs(this.message,t);switch(t.fieldKind){case"list":let s=this.lists.get(t);return s&&s[de]===n||this.lists.set(t,s=new Co(t,n,this.check)),s;case"map":let o=this.maps.get(t);return o&&o[de]===n||this.maps.set(t,o=new Uo(t,n,this.check)),o;case"message":return un(t,n,this.check);case"scalar":return n===void 0?Ne(t.scalar,!1):ln(t,n);case"enum":return n??t.enum.values[0].number}}set(t,n){if(Re(this.message,t),this.check){const o=Ee(t,n);if(o)throw o}let s;s=t.fieldKind=="message"?an(t,n):Qt(n)||Zt(n)?n[de]:cn(t,n),Qs(this.message,t,s)}getUnknown(){return this.message.$unknown}setUnknown(t){this.message.$unknown=t}}function Re(e,t){if(t.parent.typeName!==e.$typeName)throw new z(t,`cannot use ${t.toString()} with message ${e.$typeName}`,"ForeignFieldError")}class Co{field(){return this._field}get size(){return this._arr.length}constructor(t,n,s){this._field=t,this._arr=this[de]=n,this.check=s}get(t){const n=this._arr[t];return n===void 0?void 0:St(this._field,n,this.check)}set(t,n){if(t<0||t>=this._arr.length)throw new z(this._field,`list item #${t+1}: out of range`);if(this.check){const s=Pn(this._field,t,n);if(s)throw s}this._arr[t]=Mn(this._field,n)}add(t){if(this.check){const n=Pn(this._field,this._arr.length,t);if(n)throw n}this._arr.push(Mn(this._field,t))}clear(){this._arr.splice(0,this._arr.length)}[Symbol.iterator](){return this.values()}keys(){return this._arr.keys()}*values(){for(const t of this._arr)yield St(this._field,t,this.check)}*entries(){for(let t=0;t<this._arr.length;t++)yield[t,St(this._field,this._arr[t],this.check)]}}class Uo{constructor(t,n,s=!0){this.obj=this[de]=n??{},this.check=s,this._field=t}field(){return this._field}set(t,n){if(this.check){const s=function(o,r,a){const i=ur(r,o.mapKey);if(i!==!0)return new z(o,`invalid map key: ${rt({scalar:o.mapKey},r,i)}`);const u=on(o,a);return u!==!0?new z(o,`map entry ${R(r)}: ${rt(o,a,u)}`):void 0}(this._field,t,n);if(s)throw s}return this.obj[Xe(t)]=function(s,o){return s.mapKind=="message"?an(s,o):cn(s,o)}(this._field,n),this}delete(t){const n=Xe(t),s=Object.prototype.hasOwnProperty.call(this.obj,n);return s&&delete this.obj[n],s}clear(){for(const t of Object.keys(this.obj))delete this.obj[t]}get(t){let n=this.obj[Xe(t)];return n!==void 0&&(n=Ot(this._field,n,this.check)),n}has(t){return Object.prototype.hasOwnProperty.call(this.obj,Xe(t))}*keys(){for(const t of Object.keys(this.obj))yield Yn(t,this._field.mapKey)}*entries(){for(const t of Object.entries(this.obj))yield[Yn(t[0],this._field.mapKey),Ot(this._field,t[1],this.check)]}[Symbol.iterator](){return this.entries()}get size(){return Object.keys(this.obj).length}*values(){for(const t of Object.values(this.obj))yield Ot(this._field,t,this.check)}forEach(t,n){for(const s of this.entries())t.call(n,s[1],s[0],this)}}function an(e,t){return en(t)?er(t.message.$typeName)&&!e.oneof&&e.fieldKind=="message"?t.message.value:t.desc.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"?fr(t.message):t.message:t}function un(e,t,n){return t!==void 0&&(Ke(e.message)&&!e.oneof&&e.fieldKind=="message"?t={$typeName:e.message.typeName,value:ln(e.message.fields[0],t)}:e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"&&ye(t)&&(t=pr(t))),new mr(e.message,t,n)}function Mn(e,t){return e.listKind=="message"?an(e,t):cn(e,t)}function St(e,t,n){return e.listKind=="message"?un(e,t,n):ln(e,t)}function Ot(e,t,n){return e.mapKind=="message"?un(e,t,n):t}function Xe(e){return typeof e=="string"||typeof e=="number"?e:String(e)}function Yn(e,t){switch(t){case d.STRING:return e;case d.INT32:case d.FIXED32:case d.UINT32:case d.SFIXED32:case d.SINT32:{const n=Number.parseInt(e);if(Number.isFinite(n))return n;break}case d.BOOL:switch(e){case"true":return!0;case"false":return!1}break;case d.UINT64:case d.FIXED64:try{return $.uParse(e)}catch{}break;default:try{return $.parse(e)}catch{}}return e}function ln(e,t){switch(e.scalar){case d.INT64:case d.SFIXED64:case d.SINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=$.parse(t));break;case d.FIXED64:case d.UINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=$.uParse(t))}return t}function cn(e,t){switch(e.scalar){case d.INT64:case d.SFIXED64:case d.SINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=$.parse(t));break;case d.FIXED64:case d.UINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=$.uParse(t))}return t}function pr(e){const t={$typeName:"google.protobuf.Struct",fields:{}};if(ye(e))for(const[n,s]of Object.entries(e))t.fields[n]=hr(s);return t}function fr(e){const t={};for(const[n,s]of Object.entries(e.fields))t[n]=gr(s);return t}function gr(e){switch(e.kind.case){case"structValue":return fr(e.kind.value);case"listValue":return e.kind.value.values.map(gr);case"nullValue":case void 0:return null;default:return e.kind.value}}function hr(e){const t={$typeName:"google.protobuf.Value",kind:{case:void 0}};switch(typeof e){case"number":t.kind={case:"numberValue",value:e};break;case"string":t.kind={case:"stringValue",value:e};break;case"boolean":t.kind={case:"boolValue",value:e};break;case"object":if(e===null)t.kind={case:"nullValue",value:0};else if(Array.isArray(e)){const n={$typeName:"google.protobuf.ListValue",values:[]};if(Array.isArray(e))for(const s of e)n.values.push(hr(s));t.kind={case:"listValue",value:n}}else t.kind={case:"structValue",value:pr(e)}}return t}function br(e){const t=function(){if(!_e){_e=[];const u=yr("std");for(let l=0;l<u.length;l++)_e[u[l].charCodeAt(0)]=l;_e[45]=u.indexOf("+"),_e[95]=u.indexOf("/")}return _e}();let n=3*e.length/4;e[e.length-2]=="="?n-=2:e[e.length-1]=="="&&(n-=1);let s,o=new Uint8Array(n),r=0,a=0,i=0;for(let u=0;u<e.length;u++){if(s=t[e.charCodeAt(u)],s===void 0)switch(e[u]){case"=":a=0;case`
`:case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string")}switch(a){case 0:i=s,a=1;break;case 1:o[r++]=i<<2|(48&s)>>4,i=s,a=2;break;case 2:o[r++]=(15&i)<<4|(60&s)>>2,i=s,a=3;break;case 3:o[r++]=(3&i)<<6|s,a=0}}if(a==1)throw Error("invalid base64 string");return o.subarray(0,r)}let We,Gn,_e;function yr(e){return We||(We="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),Gn=We.slice(0,-2).concat("-","_")),e=="url"?Gn:We}function Me(e){let t=!1;const n=[];for(let s=0;s<e.length;s++){let o=e.charAt(s);switch(o){case"_":t=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n.push(o),t=!1;break;default:t&&(t=!1,o=o.toUpperCase()),n.push(o)}}return n.join("")}const Lo=new Set(["constructor","toString","toJSON","valueOf"]);function Ye(e){return Lo.has(e)?e+"$":e}function dn(e){for(const t of e.field)xe(t,"jsonName")||(t.jsonName=Me(t.name));e.nestedType.forEach(dn)}function Po(e,t){switch(e){case d.STRING:return t;case d.BYTES:{const n=function(s){const o=[],r={tail:s,c:"",next(){return this.tail.length!=0&&(this.c=this.tail[0],this.tail=this.tail.substring(1),!0)},take(a){if(this.tail.length>=a){const i=this.tail.substring(0,a);return this.tail=this.tail.substring(a),i}return!1}};for(;r.next();)if(r.c==="\\"){if(r.next())switch(r.c){case"\\":o.push(r.c.charCodeAt(0));break;case"b":o.push(8);break;case"f":o.push(12);break;case"n":o.push(10);break;case"r":o.push(13);break;case"t":o.push(9);break;case"v":o.push(11);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":{const a=r.c,i=r.take(2);if(i===!1)return!1;const u=parseInt(a+i,8);if(Number.isNaN(u))return!1;o.push(u);break}case"x":{const a=r.c,i=r.take(2);if(i===!1)return!1;const u=parseInt(a+i,16);if(Number.isNaN(u))return!1;o.push(u);break}case"u":{const a=r.c,i=r.take(4);if(i===!1)return!1;const u=parseInt(a+i,16);if(Number.isNaN(u))return!1;const l=new Uint8Array(4);new DataView(l.buffer).setInt32(0,u,!0),o.push(l[0],l[1],l[2],l[3]);break}case"U":{const a=r.c,i=r.take(8);if(i===!1)return!1;const u=$.uEnc(a+i),l=new Uint8Array(8),c=new DataView(l.buffer);c.setInt32(0,u.lo,!0),c.setInt32(4,u.hi,!0),o.push(l[0],l[1],l[2],l[3],l[4],l[5],l[6],l[7]);break}}}else o.push(r.c.charCodeAt(0));return new Uint8Array(o)}(t);if(n===!1)throw new Error(`cannot parse ${d[e]} default value: ${t}`);return n}case d.INT64:case d.SFIXED64:case d.SINT64:return $.parse(t);case d.UINT64:case d.FIXED64:return $.uParse(t);case d.DOUBLE:case d.FLOAT:switch(t){case"inf":return Number.POSITIVE_INFINITY;case"-inf":return Number.NEGATIVE_INFINITY;case"nan":return Number.NaN;default:return parseFloat(t)}case d.BOOL:return t==="true";case d.INT32:case d.UINT32:case d.SINT32:case d.FIXED32:case d.SFIXED32:return parseInt(t,10)}}function*Gt(e){switch(e.kind){case"file":for(const t of e.messages)yield t,yield*Gt(t);yield*e.enums,yield*e.services,yield*e.extensions;break;case"message":for(const t of e.nestedMessages)yield t,yield*Gt(t);yield*e.nestedEnums,yield*e.nestedExtensions}}function Er(...e){const t=function(){const n=new Map,s=new Map,o=new Map;return{kind:"registry",types:n,extendees:s,[Symbol.iterator]:()=>n.values(),get files(){return o.values()},addFile(r,a,i){if(o.set(r.proto.name,r),!a)for(const u of Gt(r))this.add(u);if(i)for(const u of r.dependencies)this.addFile(u,a,i)},add(r){if(r.kind=="extension"){let a=s.get(r.extendee.typeName);a||s.set(r.extendee.typeName,a=new Map),a.set(r.number,r)}n.set(r.typeName,r)},get:r=>n.get(r),getFile:r=>o.get(r),getMessage(r){const a=n.get(r);return(a==null?void 0:a.kind)=="message"?a:void 0},getEnum(r){const a=n.get(r);return(a==null?void 0:a.kind)=="enum"?a:void 0},getExtension(r){const a=n.get(r);return(a==null?void 0:a.kind)=="extension"?a:void 0},getExtensionFor(r,a){var i;return(i=s.get(r.typeName))===null||i===void 0?void 0:i.get(a)},getService(r){const a=n.get(r);return(a==null?void 0:a.kind)=="service"?a:void 0}}}();if(!e.length)return t;if("$typeName"in e[0]&&e[0].$typeName=="google.protobuf.FileDescriptorSet"){for(const n of e[0].file)Bn(n,t);return t}if("$typeName"in e[0]){let r=function(a){const i=[];for(const u of a.dependency){if(t.getFile(u)!=null||o.has(u))continue;const l=s(u);if(!l)throw new Error(`Unable to resolve ${u}, imported by ${a.name}`);"kind"in l?t.addFile(l,!1,!0):(o.add(l.name),i.push(l))}return i.concat(...i.map(r))};const n=e[0],s=e[1],o=new Set;for(const a of[n,...r(n)].reverse())Bn(a,t)}else for(const n of e)for(const s of n.files)t.addFile(s);return t}const Mo=998,Yo=999,Go=9,et=10,Ae=11,Vo=12,Vn=14,Vt=3,Ko=2,Kn=1,Bo=0,jo=1,qo=2,Xo=3,Wo=1,zo=2,Ho=1,vr={998:{fieldPresence:1,enumType:2,repeatedFieldEncoding:2,utf8Validation:3,messageEncoding:1,jsonFormat:2,enforceNamingStyle:2},999:{fieldPresence:2,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2},1e3:{fieldPresence:1,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2}};function Bn(e,t){var n,s;const o={kind:"file",proto:e,deprecated:(s=(n=e.options)===null||n===void 0?void 0:n.deprecated)!==null&&s!==void 0&&s,edition:Qo(e),name:e.name.replace(/\.proto$/,""),dependencies:ea(e,t),enums:[],messages:[],extensions:[],services:[],toString:()=>`file ${e.name}`},r=new Map,a={get:i=>r.get(i),add(i){var u;ne(((u=i.proto.options)===null||u===void 0?void 0:u.mapEntry)===!0),r.set(i.typeName,i)}};for(const i of e.enumType)Nr(i,o,void 0,t);for(const i of e.messageType)Tr(i,o,void 0,t,a);for(const i of e.service)Jo(i,o,t);Kt(o,t);for(const i of r.values())Bt(i,t,a);for(const i of o.messages)Bt(i,t,a),Kt(i,t);t.addFile(o,!0)}function Kt(e,t){switch(e.kind){case"file":for(const n of e.proto.extension){const s=jt(n,e,t);e.extensions.push(s),t.add(s)}break;case"message":for(const n of e.proto.extension){const s=jt(n,e,t);e.nestedExtensions.push(s),t.add(s)}for(const n of e.nestedMessages)Kt(n,t)}}function Bt(e,t,n){const s=e.proto.oneofDecl.map(r=>function(a,i){return{kind:"oneof",proto:a,deprecated:!1,parent:i,fields:[],name:a.name,localName:Ye(Me(a.name)),toString(){return`oneof ${i.typeName}.${this.name}`}}}(r,e)),o=new Set;for(const r of e.proto.field){const a=ta(r,s),i=jt(r,e,t,a,n);e.fields.push(i),e.field[i.localName]=i,a===void 0?e.members.push(i):(a.fields.push(i),o.has(a)||(o.add(a),e.members.push(a)))}for(const r of s.filter(a=>o.has(a)))e.oneofs.push(r);for(const r of e.nestedMessages)Bt(r,t,n)}function Nr(e,t,n,s){var o,r,a,i,u;const l=function(m,f){const p=(h=m,(h.substring(0,1)+h.substring(1).replace(/[A-Z]/g,b=>"_"+b)).toLowerCase()+"_");var h;for(const b of f){if(!b.name.toLowerCase().startsWith(p))return;const E=b.name.substring(p.length);if(E.length==0||/^\d/.test(E))return}return p}(e.name,e.value),c={kind:"enum",proto:e,deprecated:(r=(o=e.options)===null||o===void 0?void 0:o.deprecated)!==null&&r!==void 0&&r,file:t,parent:n,open:!0,name:e.name,typeName:Et(e,n,t),value:{},values:[],sharedPrefix:l,toString(){return`enum ${this.typeName}`}};c.open=function(m){var f;return Ho==Se("enumType",{proto:m.proto,parent:(f=m.parent)!==null&&f!==void 0?f:m.file})}(c),s.add(c);for(const m of e.value){const f=m.name;c.values.push(c.value[m.number]={kind:"enum_value",proto:m,deprecated:(i=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&i!==void 0&&i,parent:c,name:f,localName:Ye(l==null?f:f.substring(l.length)),number:m.number,toString:()=>`enum value ${c.typeName}.${f}`})}((u=n==null?void 0:n.nestedEnums)!==null&&u!==void 0?u:t.enums).push(c)}function Tr(e,t,n,s,o){var r,a,i,u;const l={kind:"message",proto:e,deprecated:(a=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&a!==void 0&&a,file:t,parent:n,name:e.name,typeName:Et(e,n,t),fields:[],field:{},oneofs:[],members:[],nestedEnums:[],nestedMessages:[],nestedExtensions:[],toString(){return`message ${this.typeName}`}};((i=e.options)===null||i===void 0?void 0:i.mapEntry)===!0?o.add(l):(((u=n==null?void 0:n.nestedMessages)!==null&&u!==void 0?u:t.messages).push(l),s.add(l));for(const c of e.enumType)Nr(c,t,l,s);for(const c of e.nestedType)Tr(c,t,l,s,o)}function Jo(e,t,n){var s,o;const r={kind:"service",proto:e,deprecated:(o=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&o!==void 0&&o,file:t,name:e.name,typeName:Et(e,void 0,t),methods:[],method:{},toString(){return`service ${this.typeName}`}};t.services.push(r),n.add(r);for(const a of e.method){const i=Zo(a,r,n);r.methods.push(i),r.method[i.localName]=i}}function Zo(e,t,n){var s,o,r,a;let i;i=e.clientStreaming&&e.serverStreaming?"bidi_streaming":e.clientStreaming?"client_streaming":e.serverStreaming?"server_streaming":"unary";const u=n.getMessage(ce(e.inputType)),l=n.getMessage(ce(e.outputType));ne(u,`invalid MethodDescriptorProto: input_type ${e.inputType} not found`),ne(l,`invalid MethodDescriptorProto: output_type ${e.inputType} not found`);const c=e.name;return{kind:"rpc",proto:e,deprecated:(o=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&o!==void 0&&o,parent:t,name:c,localName:Ye(c.length?Ye(c[0].toLowerCase()+c.substring(1)):c),methodKind:i,input:u,output:l,idempotency:(a=(r=e.options)===null||r===void 0?void 0:r.idempotencyLevel)!==null&&a!==void 0?a:Bo,toString:()=>`rpc ${t.typeName}.${c}`}}function jt(e,t,n,s,o){var r,a,i;const u=o===void 0,l={kind:"field",proto:e,deprecated:(a=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&a!==void 0&&a,name:e.name,number:e.number,scalar:void 0,message:void 0,enum:void 0,presence:na(e,s,u,t),listKind:void 0,mapKind:void 0,mapKey:void 0,delimitedEncoding:void 0,packed:void 0,longAsString:!1,getDefaultValue:void 0};if(u){const p=t.kind=="file"?t:t.file,h=t.kind=="file"?void 0:t,b=Et(e,h,p);l.kind="extension",l.file=p,l.parent=h,l.oneof=void 0,l.typeName=b,l.jsonName=`[${b}]`,l.toString=()=>`extension ${b}`;const E=n.getMessage(ce(e.extendee));ne(E,`invalid FieldDescriptorProto: extendee ${e.extendee} not found`),l.extendee=E}else{const p=t;ne(p.kind=="message"),l.parent=p,l.oneof=s,l.localName=s?Me(e.name):Ye(Me(e.name)),l.jsonName=e.jsonName,l.toString=()=>`field ${p.typeName}.${e.name}`}const c=e.label,m=e.type,f=(i=e.options)===null||i===void 0?void 0:i.jstype;if(c===Vt){const p=m==Ae?o==null?void 0:o.get(ce(e.typeName)):void 0;if(p){l.fieldKind="map";const{key:h,value:b}=function(E){const N=E.fields.find(g=>g.number===1),F=E.fields.find(g=>g.number===2);return ne(N&&N.fieldKind=="scalar"&&N.scalar!=d.BYTES&&N.scalar!=d.FLOAT&&N.scalar!=d.DOUBLE&&F&&F.fieldKind!="list"&&F.fieldKind!="map"),{key:N,value:F}}(p);return l.mapKey=h.scalar,l.mapKind=b.fieldKind,l.message=b.message,l.delimitedEncoding=!1,l.enum=b.enum,l.scalar=b.scalar,l}switch(l.fieldKind="list",m){case Ae:case et:l.listKind="message",l.message=n.getMessage(ce(e.typeName)),ne(l.message),l.delimitedEncoding=jn(e,t);break;case Vn:l.listKind="enum",l.enum=n.getEnum(ce(e.typeName)),ne(l.enum);break;default:l.listKind="scalar",l.scalar=m,l.longAsString=f==Kn}return l.packed=function(h,b){if(h.label!=Vt)return!1;switch(h.type){case Go:case Vo:case et:case Ae:return!1}const E=h.options;return E&&xe(E,"packed")?E.packed:Wo==Se("repeatedFieldEncoding",{proto:h,parent:b})}(e,t),l}switch(m){case Ae:case et:l.fieldKind="message",l.message=n.getMessage(ce(e.typeName)),ne(l.message,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),l.delimitedEncoding=jn(e,t),l.getDefaultValue=()=>{};break;case Vn:{const p=n.getEnum(ce(e.typeName));ne(p!==void 0,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),l.fieldKind="enum",l.enum=n.getEnum(ce(e.typeName)),l.getDefaultValue=()=>xe(e,"defaultValue")?function(h,b){const E=h.values.find(N=>N.name===b);if(!E)throw new Error(`cannot parse ${h} default value: ${b}`);return E.number}(p,e.defaultValue):void 0;break}default:l.fieldKind="scalar",l.scalar=m,l.longAsString=f==Kn,l.getDefaultValue=()=>xe(e,"defaultValue")?Po(m,e.defaultValue):void 0}return l}function Qo(e){switch(e.syntax){case"":case"proto2":return Mo;case"proto3":return Yo;case"editions":if(e.edition in vr)return e.edition;throw new Error(`${e.name}: unsupported edition`);default:throw new Error(`${e.name}: unsupported syntax "${e.syntax}"`)}}function ea(e,t){return e.dependency.map(n=>{const s=t.getFile(n);if(!s)throw new Error(`Cannot find ${n}, imported by ${e.name}`);return s})}function Et(e,t,n){let s;return s=t?`${t.typeName}.${e.name}`:n.proto.package.length>0?`${n.proto.package}.${e.name}`:`${e.name}`,s}function ce(e){return e.startsWith(".")?e.substring(1):e}function ta(e,t){if(!xe(e,"oneofIndex")||e.proto3Optional)return;const n=t[e.oneofIndex];return ne(n,`invalid FieldDescriptorProto: oneof #${e.oneofIndex} for field #${e.number} not found`),n}function na(e,t,n,s){return e.label==Ko?Xo:e.label==Vt?qo:t||e.proto3Optional||e.type==Ae||n?jo:Se("fieldPresence",{proto:e,parent:s})}function jn(e,t){return e.type==et||zo==Se("messageEncoding",{proto:e,parent:t})}function Se(e,t){var n,s;const o=(n=t.proto.options)===null||n===void 0?void 0:n.features;if(o){const r=o[e];if(r!=0)return r}if("kind"in t){if(t.kind=="message")return Se(e,(s=t.parent)!==null&&s!==void 0?s:t.file);const r=vr[t.edition];if(!r)throw new Error(`feature default for edition ${t.edition} not found`);return r[e]}return Se(e,t.parent)}function ne(e,t){if(!e)throw new Error(t)}function sa(e){const t=function(n){return Object.assign(Object.create({syntax:"",edition:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FileDescriptorProto",dependency:[],publicDependency:[],weakDependency:[],service:[],extension:[]},n),{messageType:n.messageType.map(Ir),enumType:n.enumType.map(_r)}))}(e);return t.messageType.forEach(dn),Er(t,()=>{}).getFile(t.name)}function Ir(e){var t,n,s,o,r,a,i,u;return{$typeName:"google.protobuf.DescriptorProto",name:e.name,field:(n=(t=e.field)===null||t===void 0?void 0:t.map(ra))!==null&&n!==void 0?n:[],extension:[],nestedType:(o=(s=e.nestedType)===null||s===void 0?void 0:s.map(Ir))!==null&&o!==void 0?o:[],enumType:(a=(r=e.enumType)===null||r===void 0?void 0:r.map(_r))!==null&&a!==void 0?a:[],extensionRange:(u=(i=e.extensionRange)===null||i===void 0?void 0:i.map(l=>Object.assign({$typeName:"google.protobuf.DescriptorProto.ExtensionRange"},l)))!==null&&u!==void 0?u:[],oneofDecl:[],reservedRange:[],reservedName:[]}}function ra(e){return Object.assign(Object.create({label:1,typeName:"",extendee:"",defaultValue:"",oneofIndex:0,jsonName:"",proto3Optional:!1}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldDescriptorProto"},e),{options:e.options?oa(e.options):void 0}))}function oa(e){var t,n,s;return Object.assign(Object.create({ctype:0,packed:!1,jstype:0,lazy:!1,unverifiedLazy:!1,deprecated:!1,weak:!1,debugRedact:!1,retention:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldOptions"},e),{targets:(t=e.targets)!==null&&t!==void 0?t:[],editionDefaults:(s=(n=e.editionDefaults)===null||n===void 0?void 0:n.map(r=>Object.assign({$typeName:"google.protobuf.FieldOptions.EditionDefault"},r)))!==null&&s!==void 0?s:[],uninterpretedOption:[]}))}function _r(e){return{$typeName:"google.protobuf.EnumDescriptorProto",name:e.name,reservedName:[],reservedRange:[],value:e.value.map(t=>Object.assign({$typeName:"google.protobuf.EnumValueDescriptorProto"},t))}}function Be(e,t,...n){return n.reduce((s,o)=>s.nestedMessages[o],e.messages[t])}const aa=Be(sa({name:"google/protobuf/descriptor.proto",package:"google.protobuf",messageType:[{name:"FileDescriptorSet",field:[{name:"file",number:1,type:11,label:3,typeName:".google.protobuf.FileDescriptorProto"}],extensionRange:[{start:536e6,end:536000001}]},{name:"FileDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"package",number:2,type:9,label:1},{name:"dependency",number:3,type:9,label:3},{name:"public_dependency",number:10,type:5,label:3},{name:"weak_dependency",number:11,type:5,label:3},{name:"message_type",number:4,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:5,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"service",number:6,type:11,label:3,typeName:".google.protobuf.ServiceDescriptorProto"},{name:"extension",number:7,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FileOptions"},{name:"source_code_info",number:9,type:11,label:1,typeName:".google.protobuf.SourceCodeInfo"},{name:"syntax",number:12,type:9,label:1},{name:"edition",number:14,type:14,label:1,typeName:".google.protobuf.Edition"}]},{name:"DescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"field",number:2,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"extension",number:6,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"nested_type",number:3,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"extension_range",number:5,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ExtensionRange"},{name:"oneof_decl",number:8,type:11,label:3,typeName:".google.protobuf.OneofDescriptorProto"},{name:"options",number:7,type:11,label:1,typeName:".google.protobuf.MessageOptions"},{name:"reserved_range",number:9,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ReservedRange"},{name:"reserved_name",number:10,type:9,label:3}],nestedType:[{name:"ExtensionRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ExtensionRangeOptions"}]},{name:"ReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"ExtensionRangeOptions",field:[{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"},{name:"declaration",number:2,type:11,label:3,typeName:".google.protobuf.ExtensionRangeOptions.Declaration",options:{retention:2}},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"verification",number:3,type:14,label:1,typeName:".google.protobuf.ExtensionRangeOptions.VerificationState",defaultValue:"UNVERIFIED",options:{retention:2}}],nestedType:[{name:"Declaration",field:[{name:"number",number:1,type:5,label:1},{name:"full_name",number:2,type:9,label:1},{name:"type",number:3,type:9,label:1},{name:"reserved",number:5,type:8,label:1},{name:"repeated",number:6,type:8,label:1}]}],enumType:[{name:"VerificationState",value:[{name:"DECLARATION",number:0},{name:"UNVERIFIED",number:1}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:3,type:5,label:1},{name:"label",number:4,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Label"},{name:"type",number:5,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Type"},{name:"type_name",number:6,type:9,label:1},{name:"extendee",number:2,type:9,label:1},{name:"default_value",number:7,type:9,label:1},{name:"oneof_index",number:9,type:5,label:1},{name:"json_name",number:10,type:9,label:1},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FieldOptions"},{name:"proto3_optional",number:17,type:8,label:1}],enumType:[{name:"Type",value:[{name:"TYPE_DOUBLE",number:1},{name:"TYPE_FLOAT",number:2},{name:"TYPE_INT64",number:3},{name:"TYPE_UINT64",number:4},{name:"TYPE_INT32",number:5},{name:"TYPE_FIXED64",number:6},{name:"TYPE_FIXED32",number:7},{name:"TYPE_BOOL",number:8},{name:"TYPE_STRING",number:9},{name:"TYPE_GROUP",number:10},{name:"TYPE_MESSAGE",number:11},{name:"TYPE_BYTES",number:12},{name:"TYPE_UINT32",number:13},{name:"TYPE_ENUM",number:14},{name:"TYPE_SFIXED32",number:15},{name:"TYPE_SFIXED64",number:16},{name:"TYPE_SINT32",number:17},{name:"TYPE_SINT64",number:18}]},{name:"Label",value:[{name:"LABEL_OPTIONAL",number:1},{name:"LABEL_REPEATED",number:3},{name:"LABEL_REQUIRED",number:2}]}]},{name:"OneofDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"options",number:2,type:11,label:1,typeName:".google.protobuf.OneofOptions"}]},{name:"EnumDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"value",number:2,type:11,label:3,typeName:".google.protobuf.EnumValueDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumOptions"},{name:"reserved_range",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto.EnumReservedRange"},{name:"reserved_name",number:5,type:9,label:3}],nestedType:[{name:"EnumReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"EnumValueDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumValueOptions"}]},{name:"ServiceDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"method",number:2,type:11,label:3,typeName:".google.protobuf.MethodDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ServiceOptions"}]},{name:"MethodDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"input_type",number:2,type:9,label:1},{name:"output_type",number:3,type:9,label:1},{name:"options",number:4,type:11,label:1,typeName:".google.protobuf.MethodOptions"},{name:"client_streaming",number:5,type:8,label:1,defaultValue:"false"},{name:"server_streaming",number:6,type:8,label:1,defaultValue:"false"}]},{name:"FileOptions",field:[{name:"java_package",number:1,type:9,label:1},{name:"java_outer_classname",number:8,type:9,label:1},{name:"java_multiple_files",number:10,type:8,label:1,defaultValue:"false"},{name:"java_generate_equals_and_hash",number:20,type:8,label:1,options:{deprecated:!0}},{name:"java_string_check_utf8",number:27,type:8,label:1,defaultValue:"false"},{name:"optimize_for",number:9,type:14,label:1,typeName:".google.protobuf.FileOptions.OptimizeMode",defaultValue:"SPEED"},{name:"go_package",number:11,type:9,label:1},{name:"cc_generic_services",number:16,type:8,label:1,defaultValue:"false"},{name:"java_generic_services",number:17,type:8,label:1,defaultValue:"false"},{name:"py_generic_services",number:18,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:23,type:8,label:1,defaultValue:"false"},{name:"cc_enable_arenas",number:31,type:8,label:1,defaultValue:"true"},{name:"objc_class_prefix",number:36,type:9,label:1},{name:"csharp_namespace",number:37,type:9,label:1},{name:"swift_prefix",number:39,type:9,label:1},{name:"php_class_prefix",number:40,type:9,label:1},{name:"php_namespace",number:41,type:9,label:1},{name:"php_metadata_namespace",number:44,type:9,label:1},{name:"ruby_package",number:45,type:9,label:1},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"OptimizeMode",value:[{name:"SPEED",number:1},{name:"CODE_SIZE",number:2},{name:"LITE_RUNTIME",number:3}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"MessageOptions",field:[{name:"message_set_wire_format",number:1,type:8,label:1,defaultValue:"false"},{name:"no_standard_descriptor_accessor",number:2,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"map_entry",number:7,type:8,label:1},{name:"deprecated_legacy_json_field_conflicts",number:11,type:8,label:1,options:{deprecated:!0}},{name:"features",number:12,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldOptions",field:[{name:"ctype",number:1,type:14,label:1,typeName:".google.protobuf.FieldOptions.CType",defaultValue:"STRING"},{name:"packed",number:2,type:8,label:1},{name:"jstype",number:6,type:14,label:1,typeName:".google.protobuf.FieldOptions.JSType",defaultValue:"JS_NORMAL"},{name:"lazy",number:5,type:8,label:1,defaultValue:"false"},{name:"unverified_lazy",number:15,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"weak",number:10,type:8,label:1,defaultValue:"false"},{name:"debug_redact",number:16,type:8,label:1,defaultValue:"false"},{name:"retention",number:17,type:14,label:1,typeName:".google.protobuf.FieldOptions.OptionRetention"},{name:"targets",number:19,type:14,label:3,typeName:".google.protobuf.FieldOptions.OptionTargetType"},{name:"edition_defaults",number:20,type:11,label:3,typeName:".google.protobuf.FieldOptions.EditionDefault"},{name:"features",number:21,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"feature_support",number:22,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],nestedType:[{name:"EditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"value",number:2,type:9,label:1}]},{name:"FeatureSupport",field:[{name:"edition_introduced",number:1,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"edition_deprecated",number:2,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"deprecation_warning",number:3,type:9,label:1},{name:"edition_removed",number:4,type:14,label:1,typeName:".google.protobuf.Edition"}]}],enumType:[{name:"CType",value:[{name:"STRING",number:0},{name:"CORD",number:1},{name:"STRING_PIECE",number:2}]},{name:"JSType",value:[{name:"JS_NORMAL",number:0},{name:"JS_STRING",number:1},{name:"JS_NUMBER",number:2}]},{name:"OptionRetention",value:[{name:"RETENTION_UNKNOWN",number:0},{name:"RETENTION_RUNTIME",number:1},{name:"RETENTION_SOURCE",number:2}]},{name:"OptionTargetType",value:[{name:"TARGET_TYPE_UNKNOWN",number:0},{name:"TARGET_TYPE_FILE",number:1},{name:"TARGET_TYPE_EXTENSION_RANGE",number:2},{name:"TARGET_TYPE_MESSAGE",number:3},{name:"TARGET_TYPE_FIELD",number:4},{name:"TARGET_TYPE_ONEOF",number:5},{name:"TARGET_TYPE_ENUM",number:6},{name:"TARGET_TYPE_ENUM_ENTRY",number:7},{name:"TARGET_TYPE_SERVICE",number:8},{name:"TARGET_TYPE_METHOD",number:9}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"OneofOptions",field:[{name:"features",number:1,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumOptions",field:[{name:"allow_alias",number:2,type:8,label:1},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"deprecated_legacy_json_field_conflicts",number:6,type:8,label:1,options:{deprecated:!0}},{name:"features",number:7,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumValueOptions",field:[{name:"deprecated",number:1,type:8,label:1,defaultValue:"false"},{name:"features",number:2,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"debug_redact",number:3,type:8,label:1,defaultValue:"false"},{name:"feature_support",number:4,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"ServiceOptions",field:[{name:"features",number:34,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"MethodOptions",field:[{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"idempotency_level",number:34,type:14,label:1,typeName:".google.protobuf.MethodOptions.IdempotencyLevel",defaultValue:"IDEMPOTENCY_UNKNOWN"},{name:"features",number:35,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"IdempotencyLevel",value:[{name:"IDEMPOTENCY_UNKNOWN",number:0},{name:"NO_SIDE_EFFECTS",number:1},{name:"IDEMPOTENT",number:2}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"UninterpretedOption",field:[{name:"name",number:2,type:11,label:3,typeName:".google.protobuf.UninterpretedOption.NamePart"},{name:"identifier_value",number:3,type:9,label:1},{name:"positive_int_value",number:4,type:4,label:1},{name:"negative_int_value",number:5,type:3,label:1},{name:"double_value",number:6,type:1,label:1},{name:"string_value",number:7,type:12,label:1},{name:"aggregate_value",number:8,type:9,label:1}],nestedType:[{name:"NamePart",field:[{name:"name_part",number:1,type:9,label:2},{name:"is_extension",number:2,type:8,label:2}]}]},{name:"FeatureSet",field:[{name:"field_presence",number:1,type:14,label:1,typeName:".google.protobuf.FeatureSet.FieldPresence",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPLICIT",edition:900},{value:"IMPLICIT",edition:999},{value:"EXPLICIT",edition:1e3}]}},{name:"enum_type",number:2,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnumType",options:{retention:1,targets:[6,1],editionDefaults:[{value:"CLOSED",edition:900},{value:"OPEN",edition:999}]}},{name:"repeated_field_encoding",number:3,type:14,label:1,typeName:".google.protobuf.FeatureSet.RepeatedFieldEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPANDED",edition:900},{value:"PACKED",edition:999}]}},{name:"utf8_validation",number:4,type:14,label:1,typeName:".google.protobuf.FeatureSet.Utf8Validation",options:{retention:1,targets:[4,1],editionDefaults:[{value:"NONE",edition:900},{value:"VERIFY",edition:999}]}},{name:"message_encoding",number:5,type:14,label:1,typeName:".google.protobuf.FeatureSet.MessageEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"LENGTH_PREFIXED",edition:900}]}},{name:"json_format",number:6,type:14,label:1,typeName:".google.protobuf.FeatureSet.JsonFormat",options:{retention:1,targets:[3,6,1],editionDefaults:[{value:"LEGACY_BEST_EFFORT",edition:900},{value:"ALLOW",edition:999}]}},{name:"enforce_naming_style",number:7,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnforceNamingStyle",options:{retention:2,targets:[1,2,3,4,5,6,7,8,9],editionDefaults:[{value:"STYLE_LEGACY",edition:900},{value:"STYLE2024",edition:1001}]}}],enumType:[{name:"FieldPresence",value:[{name:"FIELD_PRESENCE_UNKNOWN",number:0},{name:"EXPLICIT",number:1},{name:"IMPLICIT",number:2},{name:"LEGACY_REQUIRED",number:3}]},{name:"EnumType",value:[{name:"ENUM_TYPE_UNKNOWN",number:0},{name:"OPEN",number:1},{name:"CLOSED",number:2}]},{name:"RepeatedFieldEncoding",value:[{name:"REPEATED_FIELD_ENCODING_UNKNOWN",number:0},{name:"PACKED",number:1},{name:"EXPANDED",number:2}]},{name:"Utf8Validation",value:[{name:"UTF8_VALIDATION_UNKNOWN",number:0},{name:"VERIFY",number:2},{name:"NONE",number:3}]},{name:"MessageEncoding",value:[{name:"MESSAGE_ENCODING_UNKNOWN",number:0},{name:"LENGTH_PREFIXED",number:1},{name:"DELIMITED",number:2}]},{name:"JsonFormat",value:[{name:"JSON_FORMAT_UNKNOWN",number:0},{name:"ALLOW",number:1},{name:"LEGACY_BEST_EFFORT",number:2}]},{name:"EnforceNamingStyle",value:[{name:"ENFORCE_NAMING_STYLE_UNKNOWN",number:0},{name:"STYLE2024",number:1},{name:"STYLE_LEGACY",number:2}]}],extensionRange:[{start:1e3,end:9995},{start:9995,end:1e4},{start:1e4,end:10001}]},{name:"FeatureSetDefaults",field:[{name:"defaults",number:1,type:11,label:3,typeName:".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"},{name:"minimum_edition",number:4,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"maximum_edition",number:5,type:14,label:1,typeName:".google.protobuf.Edition"}],nestedType:[{name:"FeatureSetEditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"overridable_features",number:4,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"fixed_features",number:5,type:11,label:1,typeName:".google.protobuf.FeatureSet"}]}]},{name:"SourceCodeInfo",field:[{name:"location",number:1,type:11,label:3,typeName:".google.protobuf.SourceCodeInfo.Location"}],nestedType:[{name:"Location",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"span",number:2,type:5,label:3,options:{packed:!0}},{name:"leading_comments",number:3,type:9,label:1},{name:"trailing_comments",number:4,type:9,label:1},{name:"leading_detached_comments",number:6,type:9,label:3}]}],extensionRange:[{start:536e6,end:536000001}]},{name:"GeneratedCodeInfo",field:[{name:"annotation",number:1,type:11,label:3,typeName:".google.protobuf.GeneratedCodeInfo.Annotation"}],nestedType:[{name:"Annotation",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"source_file",number:2,type:9,label:1},{name:"begin",number:3,type:5,label:1},{name:"end",number:4,type:5,label:1},{name:"semantic",number:5,type:14,label:1,typeName:".google.protobuf.GeneratedCodeInfo.Annotation.Semantic"}],enumType:[{name:"Semantic",value:[{name:"NONE",number:0},{name:"SET",number:1},{name:"ALIAS",number:2}]}]}]}],enumType:[{name:"Edition",value:[{name:"EDITION_UNKNOWN",number:0},{name:"EDITION_LEGACY",number:900},{name:"EDITION_PROTO2",number:998},{name:"EDITION_PROTO3",number:999},{name:"EDITION_2023",number:1e3},{name:"EDITION_2024",number:1001},{name:"EDITION_1_TEST_ONLY",number:1},{name:"EDITION_2_TEST_ONLY",number:2},{name:"EDITION_99997_TEST_ONLY",number:99997},{name:"EDITION_99998_TEST_ONLY",number:99998},{name:"EDITION_99999_TEST_ONLY",number:99999},{name:"EDITION_MAX",number:2147483647}]}]}),1);var qn,Xn,Wn,zn,Hn,Jn,Zn,Qn,es,ts,ns,ss,rs,os,as,is,us,ls;(function(e){e[e.DECLARATION=0]="DECLARATION",e[e.UNVERIFIED=1]="UNVERIFIED"})(qn||(qn={})),function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.GROUP=10]="GROUP",e[e.MESSAGE=11]="MESSAGE",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.ENUM=14]="ENUM",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"}(Xn||(Xn={})),function(e){e[e.OPTIONAL=1]="OPTIONAL",e[e.REPEATED=3]="REPEATED",e[e.REQUIRED=2]="REQUIRED"}(Wn||(Wn={})),function(e){e[e.SPEED=1]="SPEED",e[e.CODE_SIZE=2]="CODE_SIZE",e[e.LITE_RUNTIME=3]="LITE_RUNTIME"}(zn||(zn={})),function(e){e[e.STRING=0]="STRING",e[e.CORD=1]="CORD",e[e.STRING_PIECE=2]="STRING_PIECE"}(Hn||(Hn={})),function(e){e[e.JS_NORMAL=0]="JS_NORMAL",e[e.JS_STRING=1]="JS_STRING",e[e.JS_NUMBER=2]="JS_NUMBER"}(Jn||(Jn={})),function(e){e[e.RETENTION_UNKNOWN=0]="RETENTION_UNKNOWN",e[e.RETENTION_RUNTIME=1]="RETENTION_RUNTIME",e[e.RETENTION_SOURCE=2]="RETENTION_SOURCE"}(Zn||(Zn={})),function(e){e[e.TARGET_TYPE_UNKNOWN=0]="TARGET_TYPE_UNKNOWN",e[e.TARGET_TYPE_FILE=1]="TARGET_TYPE_FILE",e[e.TARGET_TYPE_EXTENSION_RANGE=2]="TARGET_TYPE_EXTENSION_RANGE",e[e.TARGET_TYPE_MESSAGE=3]="TARGET_TYPE_MESSAGE",e[e.TARGET_TYPE_FIELD=4]="TARGET_TYPE_FIELD",e[e.TARGET_TYPE_ONEOF=5]="TARGET_TYPE_ONEOF",e[e.TARGET_TYPE_ENUM=6]="TARGET_TYPE_ENUM",e[e.TARGET_TYPE_ENUM_ENTRY=7]="TARGET_TYPE_ENUM_ENTRY",e[e.TARGET_TYPE_SERVICE=8]="TARGET_TYPE_SERVICE",e[e.TARGET_TYPE_METHOD=9]="TARGET_TYPE_METHOD"}(Qn||(Qn={})),function(e){e[e.IDEMPOTENCY_UNKNOWN=0]="IDEMPOTENCY_UNKNOWN",e[e.NO_SIDE_EFFECTS=1]="NO_SIDE_EFFECTS",e[e.IDEMPOTENT=2]="IDEMPOTENT"}(es||(es={})),function(e){e[e.FIELD_PRESENCE_UNKNOWN=0]="FIELD_PRESENCE_UNKNOWN",e[e.EXPLICIT=1]="EXPLICIT",e[e.IMPLICIT=2]="IMPLICIT",e[e.LEGACY_REQUIRED=3]="LEGACY_REQUIRED"}(ts||(ts={})),function(e){e[e.ENUM_TYPE_UNKNOWN=0]="ENUM_TYPE_UNKNOWN",e[e.OPEN=1]="OPEN",e[e.CLOSED=2]="CLOSED"}(ns||(ns={})),function(e){e[e.REPEATED_FIELD_ENCODING_UNKNOWN=0]="REPEATED_FIELD_ENCODING_UNKNOWN",e[e.PACKED=1]="PACKED",e[e.EXPANDED=2]="EXPANDED"}(ss||(ss={})),function(e){e[e.UTF8_VALIDATION_UNKNOWN=0]="UTF8_VALIDATION_UNKNOWN",e[e.VERIFY=2]="VERIFY",e[e.NONE=3]="NONE"}(rs||(rs={})),function(e){e[e.MESSAGE_ENCODING_UNKNOWN=0]="MESSAGE_ENCODING_UNKNOWN",e[e.LENGTH_PREFIXED=1]="LENGTH_PREFIXED",e[e.DELIMITED=2]="DELIMITED"}(os||(os={})),function(e){e[e.JSON_FORMAT_UNKNOWN=0]="JSON_FORMAT_UNKNOWN",e[e.ALLOW=1]="ALLOW",e[e.LEGACY_BEST_EFFORT=2]="LEGACY_BEST_EFFORT"}(as||(as={})),function(e){e[e.ENFORCE_NAMING_STYLE_UNKNOWN=0]="ENFORCE_NAMING_STYLE_UNKNOWN",e[e.STYLE2024=1]="STYLE2024",e[e.STYLE_LEGACY=2]="STYLE_LEGACY"}(is||(is={})),function(e){e[e.NONE=0]="NONE",e[e.SET=1]="SET",e[e.ALIAS=2]="ALIAS"}(us||(us={})),function(e){e[e.EDITION_UNKNOWN=0]="EDITION_UNKNOWN",e[e.EDITION_LEGACY=900]="EDITION_LEGACY",e[e.EDITION_PROTO2=998]="EDITION_PROTO2",e[e.EDITION_PROTO3=999]="EDITION_PROTO3",e[e.EDITION_2023=1e3]="EDITION_2023",e[e.EDITION_2024=1001]="EDITION_2024",e[e.EDITION_1_TEST_ONLY=1]="EDITION_1_TEST_ONLY",e[e.EDITION_2_TEST_ONLY=2]="EDITION_2_TEST_ONLY",e[e.EDITION_99997_TEST_ONLY=99997]="EDITION_99997_TEST_ONLY",e[e.EDITION_99998_TEST_ONLY=99998]="EDITION_99998_TEST_ONLY",e[e.EDITION_99999_TEST_ONLY=99999]="EDITION_99999_TEST_ONLY",e[e.EDITION_MAX=2147483647]="EDITION_MAX"}(ls||(ls={}));const cs={readUnknownFields:!0};function wr(e,t,n){const s=Q(e,void 0,!1);return $r(s,new rn(t),function(o){return o?Object.assign(Object.assign({},cs),o):cs}(n),!1,t.byteLength),s.message}function $r(e,t,n,s,o){var r;const a=s?t.len:t.pos+o;let i,u;const l=(r=e.getUnknown())!==null&&r!==void 0?r:[];for(;t.pos<a&&([i,u]=t.tag(),!s||u!=O.EndGroup);){const c=e.findNumber(i);if(c)Sr(e,t,c,u,n);else{const m=t.skip(u,i);n.readUnknownFields&&l.push({no:i,wireType:u,data:m})}}if(s&&(u!=O.EndGroup||i!==o))throw new Error("invalid end group tag");l.length>0&&e.setUnknown(l)}function Sr(e,t,n,s,o){switch(n.fieldKind){case"scalar":e.set(n,we(t,n.scalar));break;case"enum":e.set(n,we(t,d.INT32));break;case"message":e.set(n,kt(t,o,n,e.get(n)));break;case"list":(function(r,a,i,u){var l;const c=i.field();if(c.listKind==="message")return void i.add(kt(r,u,c));const m=(l=c.scalar)!==null&&l!==void 0?l:d.INT32;if(!(a==O.LengthDelimited&&m!=d.STRING&&m!=d.BYTES))return void i.add(we(r,m));const p=r.uint32()+r.pos;for(;r.pos<p;)i.add(we(r,m))})(t,s,e.get(n),o);break;case"map":(function(r,a,i){const u=a.field();let l,c;const m=r.pos+r.uint32();for(;r.pos<m;){const[f]=r.tag();switch(f){case 1:l=we(r,u.mapKey);break;case 2:switch(u.mapKind){case"scalar":c=we(r,u.scalar);break;case"enum":c=r.int32();break;case"message":c=kt(r,i,u)}}}if(l===void 0&&(l=Ne(u.mapKey,!1)),c===void 0)switch(u.mapKind){case"scalar":c=Ne(u.scalar,!1);break;case"enum":c=u.enum.values[0].number;break;case"message":c=Q(u.message,void 0,!1)}a.set(l,c)})(t,e.get(n),o)}}function kt(e,t,n,s){const o=n.delimitedEncoding,r=s??Q(n.message,void 0,!1);return $r(r,e,t,o,o?n.number:e.uint32()),r}function we(e,t){switch(t){case d.STRING:return e.string();case d.BOOL:return e.bool();case d.DOUBLE:return e.double();case d.FLOAT:return e.float();case d.INT32:return e.int32();case d.INT64:return e.int64();case d.UINT64:return e.uint64();case d.FIXED64:return e.fixed64();case d.BYTES:return e.bytes();case d.FIXED32:return e.fixed32();case d.SFIXED32:return e.sfixed32();case d.SFIXED64:return e.sfixed64();case d.SINT64:return e.sint64();case d.UINT32:return e.uint32();case d.SINT32:return e.sint32()}}function mn(e,t){const n=wr(aa,br(e));return n.messageType.forEach(dn),n.dependency=[],Er(n,s=>{}).getFile(n.name)}const ia=Be(mn("Chlnb29nbGUvcHJvdG9idWYvYW55LnByb3RvEg9nb29nbGUucHJvdG9idWYiJgoDQW55EhAKCHR5cGVfdXJsGAEgASgJEg0KBXZhbHVlGAIgASgMQnYKE2NvbS5nb29nbGUucHJvdG9idWZCCEFueVByb3RvUAFaLGdvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL2FueXBiogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),0),ua=3,ds={writeUnknownFields:!0};function la(e,t,n){return ot(new ir,function(s){return s?Object.assign(Object.assign({},ds),s):ds}(n),Q(e,t)).finish()}function ot(e,t,n){var s;for(const o of n.sortedFields)if(n.isSet(o))Or(e,t,n,o);else if(o.presence==ua)throw new Error(`cannot encode ${o} to binary: required field not set`);if(t.writeUnknownFields)for(const{no:o,wireType:r,data:a}of(s=n.getUnknown())!==null&&s!==void 0?s:[])e.tag(o,r).raw(a);return e}function Or(e,t,n,s){var o;switch(s.fieldKind){case"scalar":case"enum":at(e,n.desc.typeName,s.name,(o=s.scalar)!==null&&o!==void 0?o:d.INT32,s.number,n.get(s));break;case"list":(function(r,a,i,u){var l;if(i.listKind=="message"){for(const m of u)ms(r,a,i,m);return}const c=(l=i.scalar)!==null&&l!==void 0?l:d.INT32;if(i.packed){if(!u.size)return;r.tag(i.number,O.LengthDelimited).fork();for(const m of u)kr(r,i.parent.typeName,i.name,c,m);return void r.join()}for(const m of u)at(r,i.parent.typeName,i.name,c,i.number,m)})(e,t,s,n.get(s));break;case"message":ms(e,t,s,n.get(s));break;case"map":for(const[r,a]of n.get(s))ca(e,t,s,r,a)}}function at(e,t,n,s,o,r){kr(e.tag(o,function(a){switch(a){case d.BYTES:case d.STRING:return O.LengthDelimited;case d.DOUBLE:case d.FIXED64:case d.SFIXED64:return O.Bit64;case d.FIXED32:case d.SFIXED32:case d.FLOAT:return O.Bit32;default:return O.Varint}}(s)),t,n,s,r)}function ms(e,t,n,s){n.delimitedEncoding?ot(e.tag(n.number,O.StartGroup),t,s).tag(n.number,O.EndGroup):ot(e.tag(n.number,O.LengthDelimited).fork(),t,s).join()}function ca(e,t,n,s,o){var r;switch(e.tag(n.number,O.LengthDelimited).fork(),at(e,n.parent.typeName,n.name,n.mapKey,1,s),n.mapKind){case"scalar":case"enum":at(e,n.parent.typeName,n.name,(r=n.scalar)!==null&&r!==void 0?r:d.INT32,2,o);break;case"message":ot(e.tag(2,O.LengthDelimited).fork(),t,o).join()}e.join()}function kr(e,t,n,s,o){try{switch(s){case d.STRING:e.string(o);break;case d.BOOL:e.bool(o);break;case d.DOUBLE:e.double(o);break;case d.FLOAT:e.float(o);break;case d.INT32:e.int32(o);break;case d.INT64:e.int64(o);break;case d.UINT64:e.uint64(o);break;case d.FIXED64:e.fixed64(o);break;case d.BYTES:e.bytes(o);break;case d.FIXED32:e.fixed32(o);break;case d.SFIXED32:e.sfixed32(o);break;case d.SFIXED64:e.sfixed64(o);break;case d.SINT64:e.sint64(o);break;case d.UINT32:e.uint32(o);break;case d.SINT32:e.sint32(o)}}catch(r){throw r instanceof Error?new Error(`cannot encode field ${t}.${n} to binary: ${r.message}`):r}}function da(e,t){if(e.typeUrl==="")return;const n=t.kind=="message"?t:t.getMessage(ps(e.typeUrl));return n&&function(s,o){return s.typeUrl!==""&&(typeof o=="string"?o:o.typeName)===ps(s.typeUrl)}(e,n)?wr(n,e.value):void 0}function ps(e){const t=e.lastIndexOf("/"),n=t>=0?e.substring(t+1):e;if(!n.length)throw new Error(`invalid type url: ${e}`);return n}const pn=mn("Chxnb29nbGUvcHJvdG9idWYvc3RydWN0LnByb3RvEg9nb29nbGUucHJvdG9idWYihAEKBlN0cnVjdBIzCgZmaWVsZHMYASADKAsyIy5nb29nbGUucHJvdG9idWYuU3RydWN0LkZpZWxkc0VudHJ5GkUKC0ZpZWxkc0VudHJ5EgsKA2tleRgBIAEoCRIlCgV2YWx1ZRgCIAEoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZToCOAEi6gEKBVZhbHVlEjAKCm51bGxfdmFsdWUYASABKA4yGi5nb29nbGUucHJvdG9idWYuTnVsbFZhbHVlSAASFgoMbnVtYmVyX3ZhbHVlGAIgASgBSAASFgoMc3RyaW5nX3ZhbHVlGAMgASgJSAASFAoKYm9vbF92YWx1ZRgEIAEoCEgAEi8KDHN0cnVjdF92YWx1ZRgFIAEoCzIXLmdvb2dsZS5wcm90b2J1Zi5TdHJ1Y3RIABIwCgpsaXN0X3ZhbHVlGAYgASgLMhouZ29vZ2xlLnByb3RvYnVmLkxpc3RWYWx1ZUgAQgYKBGtpbmQiMwoJTGlzdFZhbHVlEiYKBnZhbHVlcxgBIAMoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZSobCglOdWxsVmFsdWUSDgoKTlVMTF9WQUxVRRAAQn8KE2NvbS5nb29nbGUucHJvdG9idWZCC1N0cnVjdFByb3RvUAFaL2dvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL3N0cnVjdHBi+AEBogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),ma=Be(pn,0),Rr=Be(pn,1),pa=Be(pn,2);var qt;function fa(e,t){Ar(t,e);const n=function(a,i){if(a===void 0)return[];if(i.fieldKind==="enum"||i.fieldKind==="scalar"){for(let u=a.length-1;u>=0;--u)if(a[u].no==i.number)return[a[u]];return[]}return a.filter(u=>u.no===i.number)}(e.$unknown,t),[s,o,r]=vt(t);for(const a of n)Sr(s,new rn(a.data),o,a.wireType,{readUnknownFields:!0});return r()}function ga(e,t,n){var s;Ar(t,e);const o=((s=e.$unknown)!==null&&s!==void 0?s:[]).filter(l=>l.no!==t.number),[r,a]=vt(t,n),i=new ir;Or(i,{writeUnknownFields:!0},r,a);const u=new rn(i.finish());for(;u.pos<u.len;){const[l,c]=u.tag(),m=u.skip(c,l);o.push({no:l,wireType:c,data:m})}e.$unknown=o}function vt(e,t){const n=e.typeName,s=Object.assign(Object.assign({},e),{kind:"field",parent:e.extendee,localName:n}),o=Object.assign(Object.assign({},e.extendee),{fields:[s],members:[s],oneofs:[]}),r=ue(o,t!==void 0?{[n]:t}:void 0);return[Q(o,r),s,()=>{const a=r[n];if(a===void 0){const i=e.message;return Ke(i)?Ne(i.fields[0].scalar,i.fields[0].longAsString):ue(i)}return a}]}function Ar(e,t){if(e.extendee.typeName!=t.$typeName)throw new Error(`extension ${e.typeName} can only be applied to message ${e.extendee.typeName}`)}(function(e){e[e.NULL_VALUE=0]="NULL_VALUE"})(qt||(qt={}));const ha=3,ba=2,fs={alwaysEmitImplicit:!1,enumAsInteger:!1,useProtoFieldName:!1};function ya(e,t,n){return Fe(Q(e,t),function(s){return s?Object.assign(Object.assign({},fs),s):fs}(n))}function Fe(e,t){var n;const s=function(r,a){if(r.desc.typeName.startsWith("google.protobuf.")){switch(r.desc.typeName){case"google.protobuf.Any":return function(u,l){if(u.typeUrl==="")return{};const{registry:c}=l;let m,f;if(c&&(m=da(u,c),m&&(f=c.getMessage(m.$typeName))),!f||!m)throw new Error(`cannot encode message ${u.$typeName} to JSON: "${u.typeUrl}" is not in the type registry`);let p=Fe(Q(f,m),l);return(f.typeName.startsWith("google.protobuf.")||p===null||Array.isArray(p)||typeof p!="object")&&(p={value:p}),p["@type"]=u.typeUrl,p}(r.message,a);case"google.protobuf.Timestamp":return function(u){const l=1e3*Number(u.seconds);if(l<Date.parse("0001-01-01T00:00:00Z")||l>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot encode message ${u.$typeName} to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);if(u.nanos<0)throw new Error(`cannot encode message ${u.$typeName} to JSON: nanos must not be negative`);let c="Z";if(u.nanos>0){const m=(u.nanos+1e9).toString().substring(1);c=m.substring(3)==="000000"?"."+m.substring(0,3)+"Z":m.substring(6)==="000"?"."+m.substring(0,6)+"Z":"."+m+"Z"}return new Date(l).toISOString().replace(".000Z",c)}(r.message);case"google.protobuf.Duration":return function(u){if(Number(u.seconds)>315576e6||Number(u.seconds)<-315576e6)throw new Error(`cannot encode message ${u.$typeName} to JSON: value out of range`);let l=u.seconds.toString();if(u.nanos!==0){let c=Math.abs(u.nanos).toString();c="0".repeat(9-c.length)+c,c.substring(3)==="000000"?c=c.substring(0,3):c.substring(6)==="000"&&(c=c.substring(0,6)),l+="."+c,u.nanos<0&&Number(u.seconds)==0&&(l="-"+l)}return l+"s"}(r.message);case"google.protobuf.FieldMask":return(i=r.message).paths.map(u=>{if(u.match(/_[0-9]?_/g)||u.match(/[A-Z]/g))throw new Error(`cannot encode message ${i.$typeName} to JSON: lowerCamelCase of path name "`+u+'" is irreversible');return Me(u)}).join(",");case"google.protobuf.Struct":return Dr(r.message);case"google.protobuf.Value":return fn(r.message);case"google.protobuf.ListValue":return xr(r.message);default:if(Ke(r.desc)){const u=r.desc.fields[0];return tt(u,r.get(u))}return}var i}}(e,t);if(s!==void 0)return s;const o={};for(const r of e.sortedFields){if(!e.isSet(r)){if(r.presence==ha)throw new Error(`cannot encode ${r} to JSON: required field not set`);if(!t.alwaysEmitImplicit||r.presence!==ba)continue}const a=gs(r,e.get(r),t);a!==void 0&&(o[Ea(r,t)]=a)}if(t.registry){const r=new Set;for(const{no:a}of(n=e.getUnknown())!==null&&n!==void 0?n:[])if(!r.has(a)){r.add(a);const i=t.registry.getExtensionFor(e.desc,a);if(!i)continue;const u=fa(e.message,i),[l,c]=vt(i,u),m=gs(c,l.get(c),t);m!==void 0&&(o[i.jsonName]=m)}}return o}function gs(e,t,n){switch(e.fieldKind){case"scalar":return tt(e,t);case"message":return Fe(t,n);case"enum":return Rt(e.enum,t,n.enumAsInteger);case"list":return function(s,o){const r=s.field(),a=[];switch(r.listKind){case"scalar":for(const i of s)a.push(tt(r,i));break;case"enum":for(const i of s)a.push(Rt(r.enum,i,o.enumAsInteger));break;case"message":for(const i of s)a.push(Fe(i,o))}return o.alwaysEmitImplicit||a.length>0?a:void 0}(t,n);case"map":return function(s,o){const r=s.field(),a={};switch(r.mapKind){case"scalar":for(const[i,u]of s)a[i]=tt(r,u);break;case"message":for(const[i,u]of s)a[i]=Fe(u,o);break;case"enum":for(const[i,u]of s)a[i]=Rt(r.enum,u,o.enumAsInteger)}return o.alwaysEmitImplicit||s.size>0?a:void 0}(t,n)}}function Rt(e,t,n){var s;if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: expected number, got ${R(t)}`);if(e.typeName=="google.protobuf.NullValue")return null;if(n)return t;const o=e.value[t];return(s=o==null?void 0:o.name)!==null&&s!==void 0?s:t}function tt(e,t){var n,s,o,r,a,i;switch(e.scalar){case d.INT32:case d.SFIXED32:case d.SINT32:case d.FIXED32:case d.UINT32:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(n=Ee(e,t))===null||n===void 0?void 0:n.message}`);return t;case d.FLOAT:case d.DOUBLE:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(s=Ee(e,t))===null||s===void 0?void 0:s.message}`);return Number.isNaN(t)?"NaN":t===Number.POSITIVE_INFINITY?"Infinity":t===Number.NEGATIVE_INFINITY?"-Infinity":t;case d.STRING:if(typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(o=Ee(e,t))===null||o===void 0?void 0:o.message}`);return t;case d.BOOL:if(typeof t!="boolean")throw new Error(`cannot encode ${e} to JSON: ${(r=Ee(e,t))===null||r===void 0?void 0:r.message}`);return t;case d.UINT64:case d.FIXED64:case d.INT64:case d.SFIXED64:case d.SINT64:if(typeof t!="bigint"&&typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(a=Ee(e,t))===null||a===void 0?void 0:a.message}`);return t.toString();case d.BYTES:if(t instanceof Uint8Array)return function(u,l="std"){const c=yr(l),m=l=="std";let f,p="",h=0,b=0;for(let E=0;E<u.length;E++)switch(f=u[E],h){case 0:p+=c[f>>2],b=(3&f)<<4,h=1;break;case 1:p+=c[b|f>>4],b=(15&f)<<2,h=2;break;case 2:p+=c[b|f>>6],p+=c[63&f],h=0}return h&&(p+=c[b],m&&(p+="=",h==1&&(p+="="))),p}(t);throw new Error(`cannot encode ${e} to JSON: ${(i=Ee(e,t))===null||i===void 0?void 0:i.message}`)}}function Ea(e,t){return t.useProtoFieldName?e.name:e.jsonName}function Dr(e){const t={};for(const[n,s]of Object.entries(e.fields))t[n]=fn(s);return t}function fn(e){switch(e.kind.case){case"nullValue":return null;case"numberValue":if(!Number.isFinite(e.kind.value))throw new Error(`${e.$typeName} cannot be NaN or Infinity`);return e.kind.value;case"boolValue":case"stringValue":return e.kind.value;case"structValue":return Dr(e.kind.value);case"listValue":return xr(e.kind.value);default:throw new Error(`${e.$typeName} must have a value`)}}function xr(e){return e.values.map(fn)}const hs={ignoreUnknownFields:!1};function va(e,t,n){const s=Q(e);try{$e(s,t,function(r){return r?Object.assign(Object.assign({},hs),r):hs}(n))}catch(r){throw(o=r)instanceof Error&&Fo.includes(o.name)&&"field"in o&&typeof o.field=="function"?new Error(`cannot decode ${r.field()} from JSON: ${r.message}`,{cause:r}):r}var o;return s.message}function $e(e,t,n){var s;if(function(a,i,u){if(!a.desc.typeName.startsWith("google.protobuf."))return!1;switch(a.desc.typeName){case"google.protobuf.Any":return function(l,c,m){var f;if(c===null||Array.isArray(c)||typeof c!="object")throw new Error(`cannot decode message ${l.$typeName} from JSON: expected object but got ${R(c)}`);if(Object.keys(c).length==0)return;const p=c["@type"];if(typeof p!="string"||p=="")throw new Error(`cannot decode message ${l.$typeName} from JSON: "@type" is empty`);const h=p.includes("/")?p.substring(p.lastIndexOf("/")+1):p;if(!h.length)throw new Error(`cannot decode message ${l.$typeName} from JSON: "@type" is invalid`);const b=(f=m.registry)===null||f===void 0?void 0:f.getMessage(h);if(!b)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${p} is not in the type registry`);const E=Q(b);if(h.startsWith("google.protobuf.")&&Object.prototype.hasOwnProperty.call(c,"value"))$e(E,c.value,m);else{const N=Object.assign({},c);delete N["@type"],$e(E,N,m)}(function(N,F,g){let Ie=!1;g||(g=ue(ia),Ie=!0),g.value=la(N,F),g.typeUrl=`type.googleapis.com/${F.$typeName}`})(E.desc,E.message,l)}(a.message,i,u),!0;case"google.protobuf.Timestamp":return function(l,c){if(typeof c!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${R(c)}`);const m=c.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:\.([0-9]{1,9}))?(?:Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!m)throw new Error(`cannot decode message ${l.$typeName} from JSON: invalid RFC 3339 string`);const f=Date.parse(m[1]+"-"+m[2]+"-"+m[3]+"T"+m[4]+":"+m[5]+":"+m[6]+(m[8]?m[8]:"Z"));if(Number.isNaN(f))throw new Error(`cannot decode message ${l.$typeName} from JSON: invalid RFC 3339 string`);if(f<Date.parse("0001-01-01T00:00:00Z")||f>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot decode message ${l.$typeName} from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);l.seconds=$.parse(f/1e3),l.nanos=0,m[7]&&(l.nanos=parseInt("1"+m[7]+"0".repeat(9-m[7].length))-1e9)}(a.message,i),!0;case"google.protobuf.Duration":return function(l,c){if(typeof c!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${R(c)}`);const m=c.match(/^(-?[0-9]+)(?:\.([0-9]+))?s/);if(m===null)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${R(c)}`);const f=Number(m[1]);if(f>315576e6||f<-315576e6)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${R(c)}`);if(l.seconds=$.parse(f),typeof m[2]!="string")return;const p=m[2]+"0".repeat(9-m[2].length);l.nanos=parseInt(p),(f<0||Object.is(f,-0))&&(l.nanos=-l.nanos)}(a.message,i),!0;case"google.protobuf.FieldMask":return function(l,c){if(typeof c!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${R(c)}`);if(c==="")return;function m(f){if(f.includes("_"))throw new Error(`cannot decode message ${l.$typeName} from JSON: path names must be lowerCamelCase`);const p=f.replace(/[A-Z]/g,h=>"_"+h.toLowerCase());return p[0]==="_"?p.substring(1):p}l.paths=c.split(",").map(m)}(a.message,i),!0;case"google.protobuf.Struct":return Cr(a.message,i),!0;case"google.protobuf.Value":return gn(a.message,i),!0;case"google.protobuf.ListValue":return Ur(a.message,i),!0;default:if(Ke(a.desc)){const l=a.desc.fields[0];return i===null?a.clear(l):a.set(l,st(l,i,!0)),!0}return!1}}(e,t,n))return;if(t==null||Array.isArray(t)||typeof t!="object")throw new Error(`cannot decode ${e.desc} from JSON: ${R(t)}`);const o=new Map,r=new Map;for(const a of e.desc.fields)r.set(a.name,a).set(a.jsonName,a);for(const[a,i]of Object.entries(t)){const u=r.get(a);if(u){if(u.oneof){if(i===null&&u.fieldKind=="scalar")continue;const l=o.get(u.oneof);if(l!==void 0)throw new z(u.oneof,`oneof set multiple times by ${l.name} and ${u.name}`);o.set(u.oneof,u)}bs(e,u,i,n)}else{let l;if(a.startsWith("[")&&a.endsWith("]")&&(l=(s=n.registry)===null||s===void 0?void 0:s.getExtension(a.substring(1,a.length-1)))&&l.extendee.typeName===e.desc.typeName){const[c,m,f]=vt(l);bs(c,m,i,n),ga(e.message,l,f())}if(!l&&!n.ignoreUnknownFields)throw new Error(`cannot decode ${e.desc} from JSON: key "${a}" is unknown`)}}}function bs(e,t,n,s){switch(t.fieldKind){case"scalar":(function(o,r,a){const i=st(r,a,!1);i===it?o.clear(r):o.set(r,i)})(e,t,n);break;case"enum":(function(o,r,a,i){const u=At(r.enum,a,i.ignoreUnknownFields,!1);u===it?o.clear(r):u!==nt&&o.set(r,u)})(e,t,n,s);break;case"message":(function(o,r,a,i){if(a===null&&r.message.typeName!="google.protobuf.Value")return void o.clear(r);const u=o.isSet(r)?o.get(r):Q(r.message);$e(u,a,i),o.set(r,u)})(e,t,n,s);break;case"list":(function(o,r,a){if(r===null)return;const i=o.field();if(!Array.isArray(r))throw new z(i,"expected Array, got "+R(r));for(const u of r){if(u===null)throw new z(i,"list item must not be null");switch(i.listKind){case"message":const l=Q(i.message);$e(l,u,a),o.add(l);break;case"enum":const c=At(i.enum,u,a.ignoreUnknownFields,!0);c!==nt&&o.add(c);break;case"scalar":o.add(st(i,u,!0))}}})(e.get(t),n,s);break;case"map":(function(o,r,a){if(r===null)return;const i=o.field();if(typeof r!="object"||Array.isArray(r))throw new z(i,"expected object, got "+R(r));for(const[u,l]of Object.entries(r)){if(l===null)throw new z(i,"map value must not be null");let c;switch(i.mapKind){case"message":const f=Q(i.message);$e(f,l,a),c=f;break;case"enum":if(c=At(i.enum,l,a.ignoreUnknownFields,!0),c===nt)return;break;case"scalar":c=st(i,l,!0)}const m=Na(i.mapKey,u);o.set(m,c)}})(e.get(t),n,s)}}const nt=Symbol();function At(e,t,n,s){if(t===null)return e.typeName=="google.protobuf.NullValue"?0:s?e.values[0].number:it;switch(typeof t){case"number":if(Number.isInteger(t))return t;break;case"string":const o=e.values.find(r=>r.name===t);if(o!==void 0)return o.number;if(n)return nt}throw new Error(`cannot decode ${e} from JSON: ${R(t)}`)}const it=Symbol();function st(e,t,n){if(t===null)return n?Ne(e.scalar,!1):it;switch(e.scalar){case d.DOUBLE:case d.FLOAT:if(t==="NaN")return NaN;if(t==="Infinity")return Number.POSITIVE_INFINITY;if(t==="-Infinity")return Number.NEGATIVE_INFINITY;if(typeof t=="number"){if(Number.isNaN(t))throw new z(e,"unexpected NaN number");if(!Number.isFinite(t))throw new z(e,"unexpected infinite number");break}if(typeof t=="string"){if(t===""||t.trim().length!==t.length)break;const s=Number(t);if(!Number.isFinite(s))break;return s}break;case d.INT32:case d.FIXED32:case d.SFIXED32:case d.SINT32:case d.UINT32:return Fr(t);case d.BYTES:if(typeof t=="string"){if(t==="")return new Uint8Array(0);try{return br(t)}catch(s){const o=s instanceof Error?s.message:String(s);throw new z(e,o)}}}return t}function Na(e,t){switch(e){case d.BOOL:switch(t){case"true":return!0;case"false":return!1}return t;case d.INT32:case d.FIXED32:case d.UINT32:case d.SFIXED32:case d.SINT32:return Fr(t);default:return t}}function Fr(e){if(typeof e=="string"){if(e===""||e.trim().length!==e.length)return e;const t=Number(e);return Number.isNaN(t)?e:t}return e}function Cr(e,t){if(typeof t!="object"||t==null||Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${R(t)}`);for(const[n,s]of Object.entries(t)){const o=ue(Rr);gn(o,s),e.fields[n]=o}}function gn(e,t){switch(typeof t){case"number":e.kind={case:"numberValue",value:t};break;case"string":e.kind={case:"stringValue",value:t};break;case"boolean":e.kind={case:"boolValue",value:t};break;case"object":if(t===null)e.kind={case:"nullValue",value:qt.NULL_VALUE};else if(Array.isArray(t)){const n=ue(pa);Ur(n,t),e.kind={case:"listValue",value:n}}else{const n=ue(ma);Cr(n,t),e.kind={case:"structValue",value:n}}break;default:throw new Error(`cannot decode message ${e.$typeName} from JSON ${R(t)}`)}return e}function Ur(e,t){if(!Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${R(t)}`);for(const n of t){const s=ue(Rr);gn(s,n),e.values.push(s)}}const yn=class yn{constructor(t){this.target=t,this.pendingRequests=new Map,this.serviceRegistries=new Set,this.cleanup=this.target.onReceiveMessage(this.handleMessage.bind(this))}addServiceRegistry(t){this.serviceRegistries.add(t)}removeServiceRegistry(t){this.serviceRegistries.delete(t)}handleMessage(t){if(!t||typeof t!="object"||!this.isGrpcMessageLike(t))return;const n=t;n.type==="com.augmentcode.client.rpc.request"?this.handleRequest(n):n.type==="com.augmentcode.client.rpc.response"&&this.handleResponse(n)}isGrpcMessageLike(t){return"type"in t&&t.type==="com.augmentcode.client.rpc.request"||t.type==="com.augmentcode.client.rpc.response"}async handleRequest(t){for(const n of this.serviceRegistries)if(n.canHandle(t))try{return void await n.handleRequest(t,s=>{this.target.sendMessage(s)})}catch(s){Array.from(this.serviceRegistries).indexOf(n)===this.serviceRegistries.size-1&&this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:s instanceof Error?s.message:String(s)})}this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:`No handlers registered for service: ${t.serviceTypeName}`})}handleResponse(t){const n=this.pendingRequests.get(t.id);if(n)if(this.pendingRequests.delete(t.id),clearTimeout(n.timeout),t.error)n.reject(new Error(`gRPC server error for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${t.error}`));else try{if(!t.data&&t.data!==null&&t.data!=="")throw new Error(`gRPC response missing data field for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id})`);n.resolve(t)}catch(s){const o=s instanceof Error?s.message:String(s);n.reject(new Error(`Failed to process gRPC response for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${o}`))}}sendRequest(t,n){return new Promise((s,o)=>{let r;n&&(r=setTimeout(()=>{this.pendingRequests.delete(t.id),o(new Error(`gRPC request timed out after ${n}ms: ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}). This may indicate that the server is not responding or the message routing is broken.`))},n)),this.pendingRequests.set(t.id,{resolve:s,reject:o,timeout:r}),this.target.sendMessage(t)})}async unary(t,n,s,o,r,a){const i=crypto.randomUUID(),u=t.localName,l=t.parent.typeName;if(!l)throw new Error("Service name is required for unary calls");const c=r?ya(t.input,ue(t.input,r)):{};if(n!=null&&n.aborted)throw new Error(`gRPC request aborted before sending: ${l}.${u} (ID: ${i})`);let m;n&&(m=()=>{const p=this.pendingRequests.get(i);p&&(this.pendingRequests.delete(i),clearTimeout(p.timeout),p.reject(new Error(`gRPC request aborted during execution: ${l}.${u} (ID: ${i})`)))},n.addEventListener("abort",m));const f=await this.sendRequest({type:"com.augmentcode.client.rpc.request",id:i,methodLocalName:u,serviceTypeName:l,data:c,timeout:s},s);return n&&m&&n.removeEventListener("abort",m),{stream:!1,method:t,service:t.parent,header:new Headers(o),message:va(t.output,f.data),trailer:new Headers}}stream(t,n,s,o,r,a){throw new Error("Streaming is not supported by this transport")}dispose(){this.cleanup();for(const{timeout:t}of this.pendingRequests.values())clearTimeout(t);this.pendingRequests.clear(),this.serviceRegistries.clear()}};yn.PROTOCOL_NAME="com.augmentcode.client.rpc";let ys=yn;function Ta(e,t,...n){if(n.length>0)throw new Error;return e.services[t]}async function Xu(e){const t=await crypto.subtle.digest("SHA-256",e);return Array.from(new Uint8Array(t)).map(n=>n.toString(16).padStart(2,"0")).join("")}var Ia=(e=>(e.chat="chat",e))(Ia||{}),_a=(e=>(e.chatMentionFolder="chat-mention-folder",e.chatMentionFile="chat-mention-file",e.chatMentionExternalSource="chat-mention-external-source",e.chatClearContext="chat-clear-context",e.chatRestoreDefaultContext="chat-restore-default-context",e.chatUseActionFind="chat-use-action-find",e.chatUseActionExplain="chat-use-action-explain",e.chatUseActionWriteTest="chat-use-action-write-test",e.chatNewConversation="chat-new-conversation",e.chatEditConversationName="chat-edit-conversation-name",e.chatFailedSmartPasteResolveFile="chat-failed-smart-paste-resolve-file",e.chatPrecomputeSmartPaste="chat-precompute-smart-paste",e.chatSmartPaste="chat-smart-paste",e.chatCodeblockCopy="chat-codeblock-copy",e.chatCodeblockCreate="chat-codeblock-create",e.chatCodeblockGoToFile="chat-codeblock-go-to-file",e.chatCodespanGoToFile="chat-codespan-go-to-file",e.chatCodespanGoToSymbol="chat-codespan-go-to-symbol",e.chatMermaidblockInitialize="chat-mermaidblock-initialize",e.chatMermaidblockToggle="chat-mermaidblock-toggle",e.chatMermaidblockInteract="chat-mermaidblock-interact",e.chatMermaidBlockError="chat-mermaidblock-error",e.chatUseSuggestedQuestion="chat-use-suggested-question",e.chatDisplaySuggestedQuestions="chat-display-suggested-questions",e.setWorkspaceGuidelines="chat-set-workspace-guidelines",e.clearWorkspaceGuidelines="chat-clear-workspace-guidelines",e.setUserGuidelines="chat-set-user-guidelines",e.clearUserGuidelines="chat-clear-user-guidelines",e))(_a||{});function Wu(e){return e.replace(/^data:.*?;base64,/,"")}async function zu(e){return new Promise((t,n)=>{const s=new FileReader;s.onload=o=>{var r;return t((r=o.target)==null?void 0:r.result)},s.onerror=n,s.readAsDataURL(e)})}async function Hu(e){return e.length<1e4?Promise.resolve(function(t){const n=atob(t);return Uint8Array.from(n,s=>s.codePointAt(0)||0)}(e)):new Promise((t,n)=>{const s=new Worker(URL.createObjectURL(new Blob([`
            self.onmessage = function(e) {
              try {
                const base64 = e.data;
                const binString = atob(base64);
                const bytes = new Uint8Array(binString.length);
                for (let i = 0; i < binString.length; i++) {
                  bytes[i] = binString.charCodeAt(i);
                }
                self.postMessage(bytes, [bytes.buffer]);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `],{type:"application/javascript"})));s.onmessage=function(o){o.data.error?n(new Error(o.data.error)):t(o.data),s.terminate()},s.onerror=function(o){n(o.error),s.terminate()},s.postMessage(e)})}const Ju=Ta(mn("Ci5jbGllbnRzL3NpZGVjYXIvbGlicy9wcm90b3MvdGVzdF9zZXJ2aWNlLnByb3RvEgR0ZXN0IhoKC1Rlc3RSZXF1ZXN0EgsKA2ZvbxgBIAEoCSIeCgxUZXN0UmVzcG9uc2USDgoGcmVzdWx0GAEgASgJMngKC1Rlc3RTZXJ2aWNlEjMKClRlc3RNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2USNAoLRXJyb3JNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2ViBnByb3RvMw"),0);var wa=(e=>(e.getEditListRequest="agent-get-edit-list-request",e.getEditListResponse="agent-get-edit-list-response",e.getEditChangesByRequestIdRequest="agent-get-edit-changes-by-request-id-request",e.getEditChangesByRequestIdResponse="agent-get-edit-changes-by-request-id-response",e.setCurrentConversation="agent-set-current-conversation",e.migrateConversationId="agent-migrate-conversation-id",e.revertToTimestamp="revert-to-timestamp",e.chatAgentEditAcceptAll="chat-agent-edit-accept-all",e.reportAgentSessionEvent="report-agent-session-event",e.reportAgentRequestEvent="report-agent-request-event",e.chatReviewAgentFile="chat-review-agent-file",e.getAgentEditContentsByRequestId="get-agent-edit-contents-by-request-id",e.getAgentEditContentsByRequestIdResponse="get-agent-edit-contents-by-request-id-response",e.checkHasEverUsedAgent="check-has-ever-used-agent",e.checkHasEverUsedAgentResponse="check-has-ever-used-agent-response",e.setHasEverUsedAgent="set-has-ever-used-agent",e.checkHasEverUsedRemoteAgent="check-has-ever-used-remote-agent",e.checkHasEverUsedRemoteAgentResponse="check-has-ever-used-remote-agent-response",e.setHasEverUsedRemoteAgent="set-has-ever-used-remote-agent",e.getSoundSettings="get-sound-settings",e.getSoundSettingsResponse="get-sound-settings-response",e.updateSoundSettings="update-sound-settings",e.soundSettingsBroadcast="sound-settings-broadcast",e.getSwarmModeSettings="get-swarm-mode-settings",e.getSwarmModeSettingsResponse="get-swarm-mode-settings-response",e.updateSwarmModeSettings="update-swarm-mode-settings",e.swarmModeSettingsBroadcast="swarm-mode-settings-broadcast",e.getChatModeRequest="get-chat-mode-request",e.getChatModeResponse="get-chat-mode-response",e))(wa||{}),$a=(e=>(e.checkToolCallSafeRequest="check-tool-call-safe-request",e.checkToolCallSafeResponse="check-tool-call-safe-response",e.closeAllToolProcesses="close-all-tool-processes",e.getToolIdentifierRequest="get-tool-identifier-request",e.getToolIdentifierResponse="get-tool-identifier-response",e))($a||{}),Sa=(e=>(e.loadConversationExchangesRequest="load-conversation-exchanges-request",e.loadConversationExchangesResponse="load-conversation-exchanges-response",e.loadExchangesByUuidsRequest="load-exchanges-by-uuids-request",e.loadExchangesByUuidsResponse="load-exchanges-by-uuids-response",e.saveExchangesRequest="save-exchanges-request",e.saveExchangesResponse="save-exchanges-response",e.deleteExchangesRequest="delete-exchanges-request",e.deleteExchangesResponse="delete-exchanges-response",e.deleteConversationExchangesRequest="delete-conversation-exchanges-request",e.deleteConversationExchangesResponse="delete-conversation-exchanges-response",e.countExchangesRequest="count-exchanges-request",e.countExchangesResponse="count-exchanges-response",e))(Sa||{});class Zu{constructor(t=[]){T(this,"_items",[]);T(this,"_focusedItemIdx");T(this,"_subscribers",new Set);T(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));T(this,"setItems",t=>{this._items=t,this._items.length===0?this.setFocusIdx(void 0):this._focusedItemIdx!==void 0&&this._focusedItemIdx>=this._items.length?this.setFocusIdx(this._items.length-1):this._focusedItemIdx===void 0?this.setFocusIdx(void 0):this.setFocusIdx(this._focusedItemIdx)});T(this,"setFocus",t=>{if(t!==void 0&&t===this.focusedItem)return;const n=t?this._items.indexOf(t):-1;n===-1?this.setFocusIdx(void 0):this.setFocusIdx(n)});T(this,"setFocusIdx",t=>{if(t===this._focusedItemIdx||this._items.length===0)return;if(t===void 0)return this._focusedItemIdx=void 0,void this.notifySubscribers();const n=Math.floor(t/this._items.length)*this._items.length;this._focusedItemIdx=(t-n)%this._items.length,this.notifySubscribers()});T(this,"initFocusIdx",t=>this._focusedItemIdx===void 0&&(this.setFocusIdx(t),!0));T(this,"focusNext",()=>{const t=this.nextIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});T(this,"focusPrev",()=>{const t=this.prevIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});T(this,"prevIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?this._items.length-1:t.nowrap&&this._focusedItemIdx===0?0:(this._focusedItemIdx-1+this._items.length)%this._items.length});T(this,"nextIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?0:t.nowrap&&this._focusedItemIdx===this._items.length-1?this._items.length-1:(this._focusedItemIdx+1)%this._items.length});T(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});this._items=t}get items(){return this._items}get focusedItem(){if(this._focusedItemIdx!==void 0)return this._items[this._focusedItemIdx]}get focusedItemIdx(){return this._focusedItemIdx}}var Oa=(e=>(e[e.unspecified=0]="unspecified",e[e.userGuidelines=1]="userGuidelines",e[e.augmentGuidelines=2]="augmentGuidelines",e[e.rules=3]="rules",e))(Oa||{}),ka=(e=>(e[e.unspecified=0]="unspecified",e[e.manuallyCreated=1]="manuallyCreated",e[e.auto=2]="auto",e[e.selectedDirectory=3]="selectedDirectory",e[e.selectedFile=4]="selectedFile",e))(ka||{});function Ra(e){return e===void 0?{num_lines:-1,num_chars:-1}:{num_lines:e.split(`
`).length,num_chars:e.length}}class Lr{constructor(){this.tracingData={flags:{},nums:{},string_stats:{},request_ids:{}}}setFlag(t,n=!0){this.tracingData.flags[t]={value:n,timestamp:new Date().toISOString()}}getFlag(t){const n=this.tracingData.flags[t];return n==null?void 0:n.value}setNum(t,n){this.tracingData.nums[t]={value:n,timestamp:new Date().toISOString()}}getNum(t){const n=this.tracingData.nums[t];return n==null?void 0:n.value}setStringStats(t,n){this.tracingData.string_stats[t]={value:Ra(n),timestamp:new Date().toISOString()}}setRequestId(t,n){this.tracingData.request_ids[t]={value:n,timestamp:new Date().toISOString()}}}var Aa=(e=>(e[e.unspecified=0]="unspecified",e[e.classify_and_distill=1]="classify_and_distill",e[e.orientation=2]="orientation",e))(Aa||{}),Da=(e=>(e.start="start",e.end="end",e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noMemoryData="noMemoryData",e.agenticTurnHasRememberToolCall="agenticTurnHasRememberToolCall",e.emptyMemory="emptyMemory",e.removeUserExchangeMemoryFailed="removeUserExchangeMemoryFailed",e))(Da||{});class Pr extends Lr{constructor(){super()}static create(){return new Pr}}var xa=(e=>(e.openedAgentConversation="opened-agent-conversation",e.revertCheckpoint="revert-checkpoint",e.agentInterruption="agent-interruption",e.sentUserMessage="sent-user-message",e.rememberToolCall="remember-tool-call",e.openedMemoriesFile="opened-memories-file",e.initialOrientation="initial-orientation",e.classifyAndDistill="classify-and-distill",e.flushMemories="flush-memories",e.vsCodeTerminalShellIntegrationNotAvailable="vs-code-terminal-shell-integration-not-available",e.vsCodeTerminalReadingApproximateOutput="vs-code-terminal-reading-approximate-output",e.vsCodeTerminalTimedOutWaitingForNoopCommand="vs-code-terminal-timed-out-waiting-for-noop-command",e.vsCodeTerminalFailedToUseShellIntegration="vs-code-terminal-failed-to-use-shell-integration",e.vsCodeTerminalLastCommandIsSameAsCurrent="vs-code-terminal-last-command-is-same-as-current",e.vsCodeTerminalPollingDeterminedProcessIsDone="vs-code-terminal-polling-determined-process-is-done",e.vsCodeTerminalFailedToReadOutput="vs-code-terminal-failed-to-read-output",e.vsCodeTerminalBuggyOutput="vs-code-terminal-buggy-output",e.vsCodeTerminalBuggyExecutionEvents="vs-code-terminal-buggy-execution-events",e.vsCodeTerminalUnsupportedVSCodeShell="vs-code-terminal-unsupported-vscode-shell",e.vsCodeTerminalFailedToFindGitBash="vs-code-terminal-failed-to-find-git-bash",e.vsCodeTerminalFailedToFindPowerShell="vs-code-terminal-failed-to-find-powershell",e.vsCodeTerminalNoSupportedShellsFound="vs-code-terminal-no-supported-shells-found",e.vsCodeTerminalSettingsChanged="vs-code-terminal-settings-changed",e.vsCodeTerminalWaitTimeout="vs-code-terminal-wait-timeout",e.vsCodeTerminalErrorLoadingSettings="vs-code-terminal-error-loading-settings",e.vsCodeTerminalErrorCheckingForShellUpdates="vs-code-terminal-error-checking-for-shell-updates",e.vsCodeTerminalErrorCleaningUpTempDir="vs-code-terminal-error-cleaning-up-temp-dir",e.vsCodeTerminalErrorInitializingShells="vs-code-terminal-error-initializing-shells",e.vsCodeTerminalErrorCheckingShellCapability="vs-code-terminal-error-checking-shell-capability",e.vsCodeTerminalErrorCreatingZshEnvironment="vs-code-terminal-error-creating-zsh-environment",e.vsCodeTerminalMissedStartEvent="vs-code-terminal-missed-start-event",e.vsCodeTerminalReadStreamTimeoutWhenProcessIsComplete="vs-code-terminal-read-stream-timeout-when-process-is-complete",e.enhancedPrompt="enhanced-prompt",e.memoriesMove="memories-move",e.rulesImported="rules-imported",e.taskListUsage="task-list-usage",e.contentTruncation="content-truncation",e))(xa||{}),Fa=(e=>(e.sentUserMessage="sent-user-message",e.chatHistorySummarization="chat-history-summarization",e.enhancedPrompt="enhanced-prompt",e.firstTokenReceived="first-token-received",e.chatHistoryTruncated="chat-history-truncated",e))(Fa||{}),Ca=(e=>(e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.start="start",e.end="end",e.noPendingUserMessage="noPendingUserMessage",e.startSendSilentExchange="startSendSilentExchange",e.sendSilentExchangeRequestId="sendSilentExchangeRequestId",e.sendSilentExchangeResponseStats="sendSilentExchangeResponseStats",e.noRequestId="noRequestId",e.conversationChanged="conversationChanged",e.explanationStats="explanationStats",e.contentStats="contentStats",e.invalidResponse="invalidResponse",e.worthRemembering="worthRemembering",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noLastUserExchangeRequestId="noLastUserExchangeRequestId",e))(Ca||{});class Mr extends Lr{constructor(){super()}static create(){return new Mr}}var Ua=(e=>(e.remoteAgentSetup="remote-agent-setup",e.setupScript="setup-script",e.sshInteraction="ssh-interaction",e.notificationBell="notification-bell",e.diffPanel="diff-panel",e.setupPageOpened="setup-page-opened",e.githubAPIFailure="github-api-failure",e.remoteAgentCreated="remote-agent-created",e.changesApplied="changes-applied",e.createdPR="created-pr",e.modeSelector="mode-selector",e.remoteAgentSetupWindow="remote-agent-setup-window",e.remoteAgentThreadList="remote-agent-thread-list",e.remoteAgentNewThreadButton="remote-agent-new-thread-button",e))(Ua||{}),La=(e=>(e[e.unknownSourceControl=0]="unknownSourceControl",e[e.git=1]="git",e[e.github=2]="github",e))(La||{}),Pa=(e=>(e[e.unknownMode=0]="unknownMode",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(Pa||{}),Ma=(e=>(e[e.unknownModeSelectorAction=0]="unknownModeSelectorAction",e[e.open=1]="open",e[e.close=2]="close",e[e.select=3]="select",e[e.init=4]="init",e))(Ma||{}),Ya=(e=>(e[e.unknownSetupWindowAction=0]="unknownSetupWindowAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectRepo=3]="selectRepo",e[e.selectBranch=4]="selectBranch",e[e.selectSetupScript=5]="selectSetupScript",e[e.autoGenerateSetupScript=6]="autoGenerateSetupScript",e[e.manuallyCreateSetupScript=7]="manuallyCreateSetupScript",e[e.typeInPromptWindow=8]="typeInPromptWindow",e[e.clickRewritePrompt=9]="clickRewritePrompt",e[e.enableNotifications=10]="enableNotifications",e[e.disableNotifications=11]="disableNotifications",e[e.clickCreateAgent=12]="clickCreateAgent",e))(Ya||{}),Ga=(e=>(e[e.unknownAgentListAction=0]="unknownAgentListAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectAgent=3]="selectAgent",e[e.deleteAgent=4]="deleteAgent",e[e.pinAgent=5]="pinAgent",e[e.unpinAgent=6]="unpinAgent",e))(Ga||{}),Va=(e=>(e[e.unknown=0]="unknown",e[e.click=1]="click",e[e.open=2]="open",e[e.close=3]="close",e))(Va||{}),Ka=(e=>(e[e.unknown=0]="unknown",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(Ka||{}),Ba=(e=>(e[e.unknown=0]="unknown",e[e.addTask=1]="addTask",e[e.addSubtask=2]="addSubtask",e[e.updateTaskStatus=3]="updateTaskStatus",e[e.updateTaskName=4]="updateTaskName",e[e.updateTaskDescription=5]="updateTaskDescription",e[e.reorganizeTaskList=6]="reorganizeTaskList",e[e.deleteTask=7]="deleteTask",e[e.runSingleTask=8]="runSingleTask",e[e.runAllTasks=9]="runAllTasks",e[e.viewTaskList=10]="viewTaskList",e[e.exportTaskList=11]="exportTaskList",e[e.importTaskList=12]="importTaskList",e[e.syncTaskList=13]="syncTaskList",e))(Ba||{}),ja=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))(ja||{});function Qu(e){return e.rootPath+"/"+e.relPath}var qa=(e=>(e.longRunning="longRunning",e.running="running",e.done="done",e))(qa||{}),Xa=(e=>(e.initializing="initializing",e.enabled="enabled",e.disabled="disabled",e.partial="partial",e))(Xa||{});const En=class En{static hasFrontmatter(t){return this.frontmatterRegex.test(t)}static extractFrontmatter(t){const n=t.match(this.frontmatterRegex);return n&&n[1]?n[1]:null}static extractContent(t){return t.replace(this.frontmatterRegex,"")}static parseBoolean(t,n,s=!0){const o=this.extractFrontmatter(t);if(o){const r=new RegExp(`${n}\\s*:\\s*(true|false)`,"i"),a=o.match(r);if(a&&a[1])return a[1].toLowerCase()==="true"}return s}static parseString(t,n,s=""){const o=this.extractFrontmatter(t);if(o){const r=new RegExp(`${n}\\s*:\\s*["']?([^"'
]*)["']?`,"i"),a=o.match(r);if(a&&a[1])return a[1].trim()}return s}static updateFrontmatter(t,n,s){const o=t.match(this.frontmatterRegex),r=typeof s!="string"||/^(true|false)$/.test(s.toLowerCase())?String(s):`"${s}"`;if(o){const a=o[1],i=new RegExp(`(${n}\\s*:\\s*)([^\\n]*)`,"i");if(a.match(i)){const u=a.replace(i,`$1${r}`);return t.replace(this.frontmatterRegex,`---
${u}---
`)}{const u=`${a.endsWith(`
`)?a:a+`
`}${n}: ${r}
`;return t.replace(this.frontmatterRegex,`---
${u}---
`)}}return`---
${n}: ${r}
---

${t}`}static createFrontmatter(t,n){let s=t;this.hasFrontmatter(s)&&(s=this.extractContent(s));for(const[o,r]of Object.entries(n))s=this.updateFrontmatter(s,o,r);return s}};En.frontmatterRegex=/^---\s*\n([\s\S]*?)\n---\s*\n/;let J=En;const ve=class ve{static parseRuleFile(t,n){const s=J.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,""),o=J.extractContent(t);return{type:this.getRuleTypeFromContent(t),path:n,content:o,description:s||void 0}}static formatRuleFileForMarkdown(t){let n=t.content;return n=J.updateFrontmatter(n,this.TYPE_FRONTMATTER_KEY,this.mapRuleTypeToString(t.type)),t.description&&(n=J.updateFrontmatter(n,this.DESCRIPTION_FRONTMATTER_KEY,t.description)),n}static getAlwaysApplyFrontmatterKey(t){return J.parseBoolean(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,!1)}static extractContent(t){return J.extractContent(t)}static updateAlwaysApplyFrontmatterKey(t,n){return J.updateFrontmatter(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,n)}static getDescriptionFrontmatterKey(t){return J.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,"")}static updateDescriptionFrontmatterKey(t,n){return J.updateFrontmatter(t,this.DESCRIPTION_FRONTMATTER_KEY,n)}static mapStringToRuleType(t){switch(t.toLowerCase()){case"always_apply":return se.ALWAYS_ATTACHED;case"manual":return se.MANUAL;case"agent_requested":return se.AGENT_REQUESTED;default:return this.DEFAULT_RULE_TYPE}}static mapRuleTypeToString(t){switch(t){case se.ALWAYS_ATTACHED:return"always_apply";case se.MANUAL:return"manual";case se.AGENT_REQUESTED:return"agent_requested";default:return"manual"}}static isValidTypeValue(t){return this.VALID_TYPE_VALUES.includes(t.toLowerCase())}static getTypeFrontmatterKey(t){return J.parseString(t,this.TYPE_FRONTMATTER_KEY,"")}static updateTypeFrontmatterKey(t,n){const s=this.mapRuleTypeToString(n);return J.updateFrontmatter(t,this.TYPE_FRONTMATTER_KEY,s)}static getRuleTypeFromContent(t){const n=this.getTypeFrontmatterKey(t);if(n&&this.isValidTypeValue(n))return this.mapStringToRuleType(n);const s=this.getAlwaysApplyFrontmatterKey(t),o=this.getDescriptionFrontmatterKey(t);return s?se.ALWAYS_ATTACHED:o&&o.trim()!==""?se.AGENT_REQUESTED:se.MANUAL}};ve.ALWAYS_APPLY_FRONTMATTER_KEY="alwaysApply",ve.DESCRIPTION_FRONTMATTER_KEY="description",ve.TYPE_FRONTMATTER_KEY="type",ve.VALID_TYPE_VALUES=["always_apply","manual","agent_requested"],ve.DEFAULT_RULE_TYPE=se.MANUAL;let Es=ve;const el=".augment",tl="rules",nl=".augment-guidelines";var Wa=(e=>(e[e.unknown=0]="unknown",e[e.new=1]="new",e[e.checkingSafety=2]="checkingSafety",e[e.runnable=3]="runnable",e[e.running=4]="running",e[e.completed=5]="completed",e[e.error=6]="error",e[e.cancelling=7]="cancelling",e[e.cancelled=8]="cancelled",e))(Wa||{});function sl(e){return`${e.requestId};${e.toolUseId}`}function rl(e){const[t,n]=e.split(";");return{requestId:t,toolUseId:n}}var za=(e=>(e.readFile="read-file",e.saveFile="save-file",e.editFile="edit-file",e.clarify="clarify",e.onboardingSubAgent="onboarding-sub-agent",e.launchProcess="launch-process",e.killProcess="kill-process",e.readProcess="read-process",e.writeProcess="write-process",e.listProcesses="list-processes",e.waitProcess="wait-process",e.openBrowser="open-browser",e.strReplaceEditor="str-replace-editor",e.remember="remember",e.diagnostics="diagnostics",e.setupScript="setup-script",e.readTerminal="read-terminal",e.gitCommitRetrieval="git-commit-retrieval",e.memoryRetrieval="memory-retrieval",e.startWorkerAgent="start_worker_agent",e.readWorkerState="read_worker_state",e.waitForWorkerAgent="wait_for_worker_agent",e.sendInstructionToWorkerAgent="send_instruction_to_worker_agent",e.stopWorkerAgent="stop_worker_agent",e.deleteWorkerAgent="delete_worker_agent",e.readWorkerAgentEdits="read_worker_agent_edits",e.LocalSubAgent="local-sub-agent",e))(za||{}),Ha=(e=>(e.remoteToolHost="remoteToolHost",e.localToolHost="localToolHost",e.sidecarToolHost="sidecarToolHost",e.mcpHost="mcpHost",e))(Ha||{}),Ja=(e=>(e[e.ContentText=0]="ContentText",e[e.ContentImage=1]="ContentImage",e))(Ja||{}),Za=(e=>(e[e.Unsafe=0]="Unsafe",e[e.Safe=1]="Safe",e[e.Check=2]="Check",e))(Za||{}),Qa=(e=>(e[e.Unknown=0]="Unknown",e[e.WebSearch=1]="WebSearch",e[e.GitHubApi=8]="GitHubApi",e[e.Linear=12]="Linear",e[e.Jira=13]="Jira",e[e.Confluence=14]="Confluence",e[e.Notion=15]="Notion",e[e.Supabase=16]="Supabase",e[e.Glean=17]="Glean",e))(Qa||{}),Z=(e=>(e.NOT_STARTED="NOT_STARTED",e.IN_PROGRESS="IN_PROGRESS",e.CANCELLED="CANCELLED",e.COMPLETE="COMPLETE",e))(Z||{}),hn=(e=>(e.USER="USER",e.AGENT="AGENT",e))(hn||{}),Yr={},ut={},lt={};let ze;Object.defineProperty(lt,"__esModule",{value:!0}),lt.default=function(){if(!ze&&(ze=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!ze))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return ze(ei)};const ei=new Uint8Array(16);var be={},Te={},ct={};Object.defineProperty(ct,"__esModule",{value:!0}),ct.default=void 0;ct.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,Object.defineProperty(Te,"__esModule",{value:!0}),Te.default=void 0;var He,ti=(He=ct)&&He.__esModule?He:{default:He},ni=function(e){return typeof e=="string"&&ti.default.test(e)};Te.default=ni,Object.defineProperty(be,"__esModule",{value:!0}),be.default=void 0,be.unsafeStringify=Gr;var si=function(e){return e&&e.__esModule?e:{default:e}}(Te);const C=[];for(let e=0;e<256;++e)C.push((e+256).toString(16).slice(1));function Gr(e,t=0){return C[e[t+0]]+C[e[t+1]]+C[e[t+2]]+C[e[t+3]]+"-"+C[e[t+4]]+C[e[t+5]]+"-"+C[e[t+6]]+C[e[t+7]]+"-"+C[e[t+8]]+C[e[t+9]]+"-"+C[e[t+10]]+C[e[t+11]]+C[e[t+12]]+C[e[t+13]]+C[e[t+14]]+C[e[t+15]]}var ri=function(e,t=0){const n=Gr(e,t);if(!(0,si.default)(n))throw TypeError("Stringified UUID is invalid");return n};be.default=ri,Object.defineProperty(ut,"__esModule",{value:!0}),ut.default=void 0;var oi=function(e){return e&&e.__esModule?e:{default:e}}(lt),ai=be;let vs,Dt,xt=0,Ft=0;var ii=function(e,t,n){let s=t&&n||0;const o=t||new Array(16);let r=(e=e||{}).node||vs,a=e.clockseq!==void 0?e.clockseq:Dt;if(r==null||a==null){const f=e.random||(e.rng||oi.default)();r==null&&(r=vs=[1|f[0],f[1],f[2],f[3],f[4],f[5]]),a==null&&(a=Dt=16383&(f[6]<<8|f[7]))}let i=e.msecs!==void 0?e.msecs:Date.now(),u=e.nsecs!==void 0?e.nsecs:Ft+1;const l=i-xt+(u-Ft)/1e4;if(l<0&&e.clockseq===void 0&&(a=a+1&16383),(l<0||i>xt)&&e.nsecs===void 0&&(u=0),u>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");xt=i,Ft=u,Dt=a,i+=122192928e5;const c=(1e4*(268435455&i)+u)%4294967296;o[s++]=c>>>24&255,o[s++]=c>>>16&255,o[s++]=c>>>8&255,o[s++]=255&c;const m=i/4294967296*1e4&268435455;o[s++]=m>>>8&255,o[s++]=255&m,o[s++]=m>>>24&15|16,o[s++]=m>>>16&255,o[s++]=a>>>8|128,o[s++]=255&a;for(let f=0;f<6;++f)o[s+f]=r[f];return t||(0,ai.unsafeStringify)(o)};ut.default=ii;var dt={},ge={},Ge={};Object.defineProperty(Ge,"__esModule",{value:!0}),Ge.default=void 0;var ui=function(e){return e&&e.__esModule?e:{default:e}}(Te),li=function(e){if(!(0,ui.default)(e))throw TypeError("Invalid UUID");let t;const n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n};Ge.default=li,Object.defineProperty(ge,"__esModule",{value:!0}),ge.URL=ge.DNS=void 0,ge.default=function(e,t,n){function s(o,r,a,i){var u;if(typeof o=="string"&&(o=function(c){c=unescape(encodeURIComponent(c));const m=[];for(let f=0;f<c.length;++f)m.push(c.charCodeAt(f));return m}(o)),typeof r=="string"&&(r=(0,di.default)(r)),((u=r)===null||u===void 0?void 0:u.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let l=new Uint8Array(16+o.length);if(l.set(r),l.set(o,r.length),l=n(l),l[6]=15&l[6]|t,l[8]=63&l[8]|128,a){i=i||0;for(let c=0;c<16;++c)a[i+c]=l[c];return a}return(0,ci.unsafeStringify)(l)}try{s.name=e}catch{}return s.DNS=Vr,s.URL=Kr,s};var ci=be,di=function(e){return e&&e.__esModule?e:{default:e}}(Ge);const Vr="6ba7b810-9dad-11d1-80b4-00c04fd430c8";ge.DNS=Vr;const Kr="6ba7b811-9dad-11d1-80b4-00c04fd430c8";ge.URL=Kr;var mt={};function Ns(e){return 14+(e+64>>>9<<4)+1}function he(e,t){const n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function Nt(e,t,n,s,o,r){return he((a=he(he(t,e),he(s,r)))<<(i=o)|a>>>32-i,n);var a,i}function U(e,t,n,s,o,r,a){return Nt(t&n|~t&s,e,t,o,r,a)}function L(e,t,n,s,o,r,a){return Nt(t&s|n&~s,e,t,o,r,a)}function P(e,t,n,s,o,r,a){return Nt(t^n^s,e,t,o,r,a)}function M(e,t,n,s,o,r,a){return Nt(n^(t|~s),e,t,o,r,a)}Object.defineProperty(mt,"__esModule",{value:!0}),mt.default=void 0;var mi=function(e){if(typeof e=="string"){const t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(let n=0;n<t.length;++n)e[n]=t.charCodeAt(n)}return function(t){const n=[],s=32*t.length,o="0123456789abcdef";for(let r=0;r<s;r+=8){const a=t[r>>5]>>>r%32&255,i=parseInt(o.charAt(a>>>4&15)+o.charAt(15&a),16);n.push(i)}return n}(function(t,n){t[n>>5]|=128<<n%32,t[Ns(n)-1]=n;let s=1732584193,o=-271733879,r=-1732584194,a=271733878;for(let i=0;i<t.length;i+=16){const u=s,l=o,c=r,m=a;s=U(s,o,r,a,t[i],7,-680876936),a=U(a,s,o,r,t[i+1],12,-389564586),r=U(r,a,s,o,t[i+2],17,606105819),o=U(o,r,a,s,t[i+3],22,-1044525330),s=U(s,o,r,a,t[i+4],7,-176418897),a=U(a,s,o,r,t[i+5],12,1200080426),r=U(r,a,s,o,t[i+6],17,-1473231341),o=U(o,r,a,s,t[i+7],22,-45705983),s=U(s,o,r,a,t[i+8],7,1770035416),a=U(a,s,o,r,t[i+9],12,-1958414417),r=U(r,a,s,o,t[i+10],17,-42063),o=U(o,r,a,s,t[i+11],22,-1990404162),s=U(s,o,r,a,t[i+12],7,1804603682),a=U(a,s,o,r,t[i+13],12,-40341101),r=U(r,a,s,o,t[i+14],17,-1502002290),o=U(o,r,a,s,t[i+15],22,1236535329),s=L(s,o,r,a,t[i+1],5,-165796510),a=L(a,s,o,r,t[i+6],9,-1069501632),r=L(r,a,s,o,t[i+11],14,643717713),o=L(o,r,a,s,t[i],20,-373897302),s=L(s,o,r,a,t[i+5],5,-701558691),a=L(a,s,o,r,t[i+10],9,38016083),r=L(r,a,s,o,t[i+15],14,-660478335),o=L(o,r,a,s,t[i+4],20,-405537848),s=L(s,o,r,a,t[i+9],5,568446438),a=L(a,s,o,r,t[i+14],9,-1019803690),r=L(r,a,s,o,t[i+3],14,-187363961),o=L(o,r,a,s,t[i+8],20,1163531501),s=L(s,o,r,a,t[i+13],5,-1444681467),a=L(a,s,o,r,t[i+2],9,-51403784),r=L(r,a,s,o,t[i+7],14,1735328473),o=L(o,r,a,s,t[i+12],20,-1926607734),s=P(s,o,r,a,t[i+5],4,-378558),a=P(a,s,o,r,t[i+8],11,-2022574463),r=P(r,a,s,o,t[i+11],16,1839030562),o=P(o,r,a,s,t[i+14],23,-35309556),s=P(s,o,r,a,t[i+1],4,-1530992060),a=P(a,s,o,r,t[i+4],11,1272893353),r=P(r,a,s,o,t[i+7],16,-155497632),o=P(o,r,a,s,t[i+10],23,-1094730640),s=P(s,o,r,a,t[i+13],4,681279174),a=P(a,s,o,r,t[i],11,-358537222),r=P(r,a,s,o,t[i+3],16,-722521979),o=P(o,r,a,s,t[i+6],23,76029189),s=P(s,o,r,a,t[i+9],4,-640364487),a=P(a,s,o,r,t[i+12],11,-421815835),r=P(r,a,s,o,t[i+15],16,530742520),o=P(o,r,a,s,t[i+2],23,-995338651),s=M(s,o,r,a,t[i],6,-198630844),a=M(a,s,o,r,t[i+7],10,1126891415),r=M(r,a,s,o,t[i+14],15,-1416354905),o=M(o,r,a,s,t[i+5],21,-57434055),s=M(s,o,r,a,t[i+12],6,1700485571),a=M(a,s,o,r,t[i+3],10,-1894986606),r=M(r,a,s,o,t[i+10],15,-1051523),o=M(o,r,a,s,t[i+1],21,-2054922799),s=M(s,o,r,a,t[i+8],6,1873313359),a=M(a,s,o,r,t[i+15],10,-30611744),r=M(r,a,s,o,t[i+6],15,-1560198380),o=M(o,r,a,s,t[i+13],21,1309151649),s=M(s,o,r,a,t[i+4],6,-145523070),a=M(a,s,o,r,t[i+11],10,-1120210379),r=M(r,a,s,o,t[i+2],15,718787259),o=M(o,r,a,s,t[i+9],21,-343485551),s=he(s,u),o=he(o,l),r=he(r,c),a=he(a,m)}return[s,o,r,a]}(function(t){if(t.length===0)return[];const n=8*t.length,s=new Uint32Array(Ns(n));for(let o=0;o<n;o+=8)s[o>>5]|=(255&t[o/8])<<o%32;return s}(e),8*e.length))};mt.default=mi,Object.defineProperty(dt,"__esModule",{value:!0}),dt.default=void 0;var pi=Br(ge),fi=Br(mt);function Br(e){return e&&e.__esModule?e:{default:e}}var gi=(0,pi.default)("v3",48,fi.default);dt.default=gi;var pt={},ft={};Object.defineProperty(ft,"__esModule",{value:!0}),ft.default=void 0;var hi={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};ft.default=hi,Object.defineProperty(pt,"__esModule",{value:!0}),pt.default=void 0;var Ts=jr(ft),bi=jr(lt),yi=be;function jr(e){return e&&e.__esModule?e:{default:e}}var Ei=function(e,t,n){if(Ts.default.randomUUID&&!t&&!e)return Ts.default.randomUUID();const s=(e=e||{}).random||(e.rng||bi.default)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t){n=n||0;for(let o=0;o<16;++o)t[n+o]=s[o];return t}return(0,yi.unsafeStringify)(s)};pt.default=Ei;var gt={},ht={};function vi(e,t,n,s){switch(e){case 0:return t&n^~t&s;case 1:case 3:return t^n^s;case 2:return t&n^t&s^n&s}}function Ct(e,t){return e<<t|e>>>32-t}Object.defineProperty(ht,"__esModule",{value:!0}),ht.default=void 0;var Ni=function(e){const t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof e=="string"){const a=unescape(encodeURIComponent(e));e=[];for(let i=0;i<a.length;++i)e.push(a.charCodeAt(i))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);const s=e.length/4+2,o=Math.ceil(s/16),r=new Array(o);for(let a=0;a<o;++a){const i=new Uint32Array(16);for(let u=0;u<16;++u)i[u]=e[64*a+4*u]<<24|e[64*a+4*u+1]<<16|e[64*a+4*u+2]<<8|e[64*a+4*u+3];r[a]=i}r[o-1][14]=8*(e.length-1)/Math.pow(2,32),r[o-1][14]=Math.floor(r[o-1][14]),r[o-1][15]=8*(e.length-1)&4294967295;for(let a=0;a<o;++a){const i=new Uint32Array(80);for(let p=0;p<16;++p)i[p]=r[a][p];for(let p=16;p<80;++p)i[p]=Ct(i[p-3]^i[p-8]^i[p-14]^i[p-16],1);let u=n[0],l=n[1],c=n[2],m=n[3],f=n[4];for(let p=0;p<80;++p){const h=Math.floor(p/20),b=Ct(u,5)+vi(h,l,c,m)+f+t[h]+i[p]>>>0;f=m,m=c,c=Ct(l,30)>>>0,l=u,u=b}n[0]=n[0]+u>>>0,n[1]=n[1]+l>>>0,n[2]=n[2]+c>>>0,n[3]=n[3]+m>>>0,n[4]=n[4]+f>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]};ht.default=Ni,Object.defineProperty(gt,"__esModule",{value:!0}),gt.default=void 0;var Ti=qr(ge),Ii=qr(ht);function qr(e){return e&&e.__esModule?e:{default:e}}var _i=(0,Ti.default)("v5",80,Ii.default);gt.default=_i;var bt={};Object.defineProperty(bt,"__esModule",{value:!0}),bt.default=void 0;bt.default="00000000-0000-0000-0000-000000000000";var yt={};Object.defineProperty(yt,"__esModule",{value:!0}),yt.default=void 0;var wi=function(e){return e&&e.__esModule?e:{default:e}}(Te),$i=function(e){if(!(0,wi.default)(e))throw TypeError("Invalid UUID");return parseInt(e.slice(14,15),16)};function Xt(e,t){if(!(e&&t&&e.length&&t.length))throw new Error("Bad alphabet");this.srcAlphabet=e,this.dstAlphabet=t}yt.default=$i,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"NIL",{enumerable:!0,get:function(){return r.default}}),Object.defineProperty(e,"parse",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(e,"v1",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"v3",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(e,"v4",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(e,"v5",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(e,"validate",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(e,"version",{enumerable:!0,get:function(){return a.default}});var t=c(ut),n=c(dt),s=c(pt),o=c(gt),r=c(bt),a=c(yt),i=c(Te),u=c(be),l=c(Ge);function c(m){return m&&m.__esModule?m:{default:m}}}(Yr),Xt.prototype.convert=function(e){var t,n,s,o={},r=this.srcAlphabet.length,a=this.dstAlphabet.length,i=e.length,u=typeof e=="string"?"":[];if(!this.isValid(e))throw new Error('Number "'+e+'" contains of non-alphabetic digits ('+this.srcAlphabet+")");if(this.srcAlphabet===this.dstAlphabet)return e;for(t=0;t<i;t++)o[t]=this.srcAlphabet.indexOf(e[t]);do{for(n=0,s=0,t=0;t<i;t++)(n=n*r+o[t])>=a?(o[s++]=parseInt(n/a,10),n%=a):s>0&&(o[s++]=0);i=s,u=this.dstAlphabet.slice(n,n+1).concat(u)}while(s!==0);return u},Xt.prototype.isValid=function(e){for(var t=0;t<e.length;++t)if(this.srcAlphabet.indexOf(e[t])===-1)return!1;return!0};var Si=Xt;function De(e,t){var n=new Si(e,t);return function(s){return n.convert(s)}}De.BIN="01",De.OCT="01234567",De.DEC="0123456789",De.HEX="0123456789abcdef";var Oi=De;const{v4:Ut,validate:ki}=Yr,Je=Oi,Lt={cookieBase90:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!#$%&'()*+-./:<=>?@[]^_`{|}~",flickrBase58:"123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ",uuid25Base36:"0123456789abcdefghijklmnopqrstuvwxyz"},Ri={consistentLength:!0};let Pt;const Is=(e,t,n)=>{const s=t(e.toLowerCase().replace(/-/g,""));return n&&n.consistentLength?s.padStart(n.shortIdLength,n.paddingChar):s},_s=(e,t)=>{const n=t(e).padStart(32,"0").match(/(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})/);return[n[1],n[2],n[3],n[4],n[5]].join("-")};var Ai=(()=>{const e=(t,n)=>{const s=t||Lt.flickrBase58,o={...Ri,...n};if([...new Set(Array.from(s))].length!==s.length)throw new Error("The provided Alphabet has duplicate characters resulting in unreliable results");const r=(a=s.length,Math.ceil(Math.log(2**128)/Math.log(a)));var a;const i={shortIdLength:r,consistentLength:o.consistentLength,paddingChar:s[0]},u=Je(Je.HEX,s),l=Je(s,Je.HEX),c=()=>Is(Ut(),u,i),m={alphabet:s,fromUUID:f=>Is(f,u,i),maxLength:r,generate:c,new:c,toUUID:f=>_s(f,l),uuid:Ut,validate:(f,p=!1)=>{if(!f||typeof f!="string")return!1;const h=o.consistentLength?f.length===r:f.length<=r,b=f.split("").every(E=>s.includes(E));return p===!1?h&&b:h&&b&&ki(_s(f,l))}};return Object.freeze(m),m};return e.constants=Lt,e.uuid=Ut,e.generate=()=>(Pt||(Pt=e(Lt.flickrBase58).generate),Pt()),e})();const Di=ao(Ai),Xr={[Z.NOT_STARTED]:"[ ]",[Z.IN_PROGRESS]:"[/]",[Z.COMPLETE]:"[x]",[Z.CANCELLED]:"[-]"},Wr=Di(void 0,{consistentLength:!0});function xi(e,t){if(e.uuid===t)return e;if(e.subTasksData)for(const n of e.subTasksData){const s=xi(n,t);if(s)return s}}function zr(e,t={}){const{shallow:n=!1,excludeUuid:s=!1,shortUuid:o=!0}=t;return Hr(e,{shallow:n,excludeUuid:s,shortUuid:o}).join(`
`)}function Hr(e,t={}){const{shallow:n=!1,excludeUuid:s=!1,shortUuid:o=!0}=t;let r="";s||(r=`UUID:${o?function(i){try{return Wr.fromUUID(i)}catch{return i}}(e.uuid):e.uuid} `);const a=`${Xr[e.state]} ${r}NAME:${e.name} DESCRIPTION:${e.description}`;return n||!e.subTasksData||e.subTasksData.length===0?[a]:[a,...(e.subTasksData||[]).map(i=>Hr(i,t).map(u=>`-${u}`)).flat()]}function Fi(e,t){var s;const n=(s=e.subTasksData)==null?void 0:s.map(o=>Fi(o,t));return{...e,uuid:t!=null&&t.keepUuid?e.uuid:crypto.randomUUID(),subTasks:(n==null?void 0:n.map(o=>o.uuid))||[],subTasksData:n}}function ol(e,t={}){if(!e.trim())throw new Error("Empty markdown");const n=e.split(`
`);let s=0;for(const l of n)if(l.trim()&&ws(l)===0)try{Wt(l,t),s++}catch{}if(s===0)throw new Error("No root task found");if(s>1)throw new Error(`Multiple root tasks found (${s}). There can only be one root task per conversation. All other tasks must be subtasks (indented with dashes). Root task format: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (no dashes). Subtask format: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (with dashes).`);const o=e.split(`
`);function r(){for(;o.length>0;){const l=o.shift(),c=ws(l);try{return{task:Wt(l,t),level:c}}catch{}}}const a=r();if(!a)throw new Error("No root task found");const i=[a.task];let u;for(;u=r();){const l=i[u.level-1];if(!l)throw new Error(`Invalid markdown: level ${u.level+1} has no parent
Line: ${u.task.name} is missing a parent
Current tasks: 
${zr(a.task)}`);l.subTasksData&&l.subTasks||(l.subTasks=[],l.subTasksData=[]),l.subTasksData.push(u.task),l.subTasks.push(u.task.uuid),i[u.level]=u.task,i.splice(u.level+1)}return a.task}function ws(e){let t=0,n=0;for(;n<e.length&&(e[n]===" "||e[n]==="	");)e[n]===" "?t+=.5:e[n]==="	"&&(t+=1),n++;for(;n<e.length&&e[n]==="-";)t+=1,n++;return Math.floor(t)}function Wt(e,t={}){const{excludeUuid:n=!1,shortUuid:s=!0}=t;let o=0;for(;o<e.length&&(e[o]===" "||e[o]==="	"||e[o]==="-");)o++;const r=e.substring(o),a=r.match(/^\s*\[([ x\-/?])\]/);if(!a)throw new Error(`Invalid task line: ${e} (missing state)`);const i=a[1],u=Object.entries(Xr).reduce((p,[h,b])=>(p[b.substring(1,2)]=h,p),{})[i]||Z.NOT_STARTED,l=r.substring(a.index+a[0].length).trim();let c,m,f;if(n){const p=/(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,h=l.match(p);if(!h){const b=/\b(?:name|NAME):/i.test(l),E=/\b(?:description|DESCRIPTION):/i.test(l);throw!b||!E?new Error(`Invalid task line: ${e} (missing required fields)`):l.toLowerCase().indexOf("name:")<l.toLowerCase().indexOf("description:")?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(m=h[1].trim(),f=h[2].trim(),!m)throw new Error(`Invalid task line: ${e} (missing required fields)`);c=crypto.randomUUID()}else{const p=/(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,h=l.match(p);if(!h){const b=/\b(?:uuid|UUID):/i.test(l),E=/\b(?:name|NAME):/i.test(l),N=/\b(?:description|DESCRIPTION):/i.test(l);if(!b||!E||!N)throw new Error(`Invalid task line: ${e} (missing required fields)`);const F=l.toLowerCase().indexOf("uuid:"),g=l.toLowerCase().indexOf("name:"),Ie=l.toLowerCase().indexOf("description:");throw F<g&&g<Ie?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(c=h[1].trim(),m=h[2].trim(),f=h[3].trim(),!c||!m)throw new Error(`Invalid task line: ${e} (missing required fields)`);if(c==="NEW_UUID")c=crypto.randomUUID();else if(s)try{c=function(b){try{return Wr.toUUID(b)}catch{return b}}(c)}catch{}}return{uuid:c,name:m,description:f,state:u,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:hn.USER}}const Oe=e=>({uuid:crypto.randomUUID(),name:"New Task",description:"New task description",state:Z.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:hn.USER,...e}),$s=Oe({name:"Task 1.1",description:"This is the first sub task",state:Z.IN_PROGRESS}),Ss=Oe({name:"Task 1.2.1",description:"This is a nested sub task, child of Task 1.2",state:Z.NOT_STARTED}),Os=Oe({name:"Task 1.2.2",description:"This is another nested sub task, child of Task 1.2",state:Z.IN_PROGRESS}),ks=Oe({name:"Task 1.2",description:"This is the second sub task",state:Z.COMPLETE,subTasks:[Ss.uuid,Os.uuid],subTasksData:[Ss,Os]}),Rs=Oe({name:"Task 1.3",description:"This is the third sub task",state:Z.CANCELLED}),al=zr(Oe({name:"Task 1",description:"This is the first task",state:Z.NOT_STARTED,subTasks:[$s.uuid,ks.uuid,Rs.uuid],subTasksData:[$s,ks,Rs]}));function Jr(e){const t=e.split(`
`);let n=null;const s={created:[],updated:[],deleted:[]};for(const o of t){const r=o.trim();if(r!=="## Created Tasks")if(r!=="## Updated Tasks")if(r!=="## Deleted Tasks"){if(n&&(r.startsWith("[ ]")||r.startsWith("[/]")||r.startsWith("[x]")||r.startsWith("[-]")))try{const a=Wt(r,{excludeUuid:!1,shortUuid:!0});a&&s[n].push(a)}catch{}}else n="deleted";else n="updated";else n="created"}return s}function il(e){const t=e.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);if(t)return{created:parseInt(t[1],10),updated:parseInt(t[2],10),deleted:parseInt(t[3],10)};const n=Jr(Zr(e));return{created:n.created.length,updated:n.updated.length,deleted:n.deleted.length}}function Zr(e){const t=e.indexOf("# Task Changes");if(t===-1)return"";const n=e.substring(t),s=[`
New and Updated Tasks:`,`
Remember:`,`

---`];let o=n.length;for(const i of s){const u=n.indexOf(i);u!==-1&&u<o&&(o=u)}const r=n.substring(0,o),a=r.indexOf(`
`);return a===-1?"":r.substring(a+1).trim()}function ul(e){return Jr(Zr(e))}class ll{static getTaskOrchestratorPrompt(t){const{taskTree:n,surroundingContext:s}=t,o=this.buildTaskContext(n,s);return`Please utilize sub-agents to complete the following task tree.
Here are the details, along with a suggestion prompt.
You may use 1 or more sub-agents in to complete the below task.
For each sub-agent, please give it the relevant context and breakdown of the below task.

## Task Details
**Name:** ${n.name}
${n.description?`**Description:** ${n.description}`:""}
**Status:** ${n.state}

## Task Context
${o}

## Instructions
Please complete this task according to the requirements.
When you are done, report back on the completion status with a summary of changes made,
important context, and other relevant information for the supervisor.

Focus on this specific task tree while being aware of the broader context provided above.`}static getTaskMentionId(t){return`task:${t.taskUuid}:${t.taskTree.name.replace(/\s+/g,"_")}`}static getTaskMentionLabel(t){const{taskTree:n,surroundingContext:s}=t;return s.targetTaskPath.length>1?`${s.targetTaskPath.slice(0,-1).join(" → ")} → ${n.name}`:n.name}static buildTaskContext(t,n){const{rootTask:s,targetTaskPath:o}=n;let r=`This task is part of a larger project: "${s.name}"`;return s.description&&(r+=`

**Project Description:** ${s.description}`),o.length>1&&(r+=`

**Task Path:** ${o.join(" → ")}`),t.subTasksData&&t.subTasksData.length>0&&(r+=`

**Subtasks:**`,t.subTasksData.forEach((a,i)=>{r+=`
${i+1}. ${a.name} (${a.state})`,a.description&&(r+=` - ${a.description}`)})),r}}class cl{constructor(){this._controllers=new Set,this._timeoutIds=new Set}addCallback(t,n){const s=new AbortController,o=setTimeout(()=>{t(s.signal),this._controllers.delete(s),this._timeoutIds.delete(o)},n);this._controllers.add(s),this._timeoutIds.add(o)}cancelAll(){this._controllers.forEach(t=>t.abort()),this._timeoutIds.forEach(t=>clearTimeout(t)),this._controllers.clear(),this._timeoutIds.clear()}}function dl(e){return e.reduce((t,n)=>t+Qr(n),0)}function Qr(e){let t=0;return e.request_nodes?t+=JSON.stringify(e.request_nodes).length:t+=(e.request_message||"").length,e.response_nodes?t+=JSON.stringify(e.response_nodes).length:t+=(e.response_text||"").length,t}function ml(e,t,n,s){if(e.length===0)return{head:[],tail:[],headSizeChars:0,tailSizeChars:0};const o=[],r=[];let a=0,i=0,u=0;for(let l=e.length-1;l>=0;l--){const c=e[l],m=Qr(c);a+m<t||r.length<s?(r.push(c),u+=m):(o.push(c),i+=m),a+=m}return a<n?(r.push(...o),{head:[],tail:r.reverse(),headSizeChars:0,tailSizeChars:a}):{head:o.reverse(),tail:r.reverse(),headSizeChars:i,tailSizeChars:u}}var Ci=(e=>(e.getHydratedTaskRequest="get-hydrated-task-request",e.getHydratedTaskResponse="get-hydrated-task-response",e.setCurrentRootTaskUuid="set-current-root-task-uuid",e.createTaskRequest="create-task-request",e.createTaskResponse="create-task-response",e.updateTaskRequest="update-task-request",e.updateTaskResponse="update-task-response",e.updateHydratedTaskRequest="update-hydrated-task-request",e.updateHydratedTaskResponse="update-hydrated-task-response",e))(Ci||{}),Ui=(e=>(e.getRulesListRequest="get-rules-list-request",e.getRulesListResponse="get-rules-list-response",e.createRule="create-rule",e.createRuleResponse="create-rule-response",e.openRule="open-rule",e.openGuidelines="open-guidelines",e.deleteRule="delete-rule",e.updateRuleFile="update-rule-file",e.updateRuleFileResponse="update-rule-file-response",e.getWorkspaceRoot="get-workspace-root",e.getWorkspaceRootResponse="get-workspace-root-response",e.autoImportRules="auto-import-rules",e.autoImportRulesOptionsResponse="auto-import-rules-options-response",e.autoImportRulesSelectionRequest="auto-import-rules-selection-request",e.autoImportRulesResponse="auto-import-rules-response",e.processSelectedPathsRequest="process-selected-paths-request",e.processSelectedPathsResponse="process-selected-paths-response",e))(Ui||{}),Li=(e=>(e.loadConversationToolUseStatesRequest="load-conversation-tooluse-states-request",e.loadConversationToolUseStatesResponse="load-conversation-tooluse-states-response",e.saveToolUseStatesRequest="save-tooluse-states-request",e.saveToolUseStatesResponse="save-tooluse-states-response",e.deleteConversationToolUseStatesRequest="delete-conversation-tooluse-states-request",e.deleteConversationToolUseStatesResponse="delete-conversation-tooluse-states-response",e))(Li||{});function Pi(e){let t,n;return{c(){t=Mt("svg"),n=Mt("path"),I(n,"fill-rule","evenodd"),I(n,"clip-rule","evenodd"),I(n,"d","M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z"),I(n,"fill","currentColor"),I(t,"width","15"),I(t,"height","15"),I(t,"viewBox","0 0 15 15"),I(t,"fill","none"),I(t,"xmlns","http://www.w3.org/2000/svg")},m(s,o){k(s,t,o),Gs(t,n)},p:re,i:re,o:re,d(s){s&&S(t)}}}class Mi extends Y{constructor(t){super(),G(this,t,null,Pi,V,{})}}const Ce=class Ce{constructor(t=void 0){T(this,"_lastFocusAnchorElement");T(this,"_focusedIndexStore",Vs(void 0));T(this,"focusedIndex",this._focusedIndexStore);T(this,"_rootElement");T(this,"_triggerElement");T(this,"_getItems",()=>{var s;const t=(s=this._rootElement)==null?void 0:s.querySelectorAll(`.${Ce.ITEM_CLASS}`),n=t==null?void 0:t[0];return n instanceof HTMLElement&&this._recomputeFocusAnchor(n),Array.from(t??[])});T(this,"_recomputeFocusAnchor",t=>{var r;const n=(r=this._parentContext)==null?void 0:r._getItems(),s=n==null?void 0:n.indexOf(t);if(s===void 0||n===void 0)return;const o=Math.max(s-1,0);this._lastFocusAnchorElement=n[o]});T(this,"registerRoot",t=>{this._rootElement=t,t.addEventListener("keydown",this._onKeyDown);const n=()=>{this.getCurrentFocusedIdx()},s=o=>{t.contains(o.relatedTarget)||this._focusedIndexStore.set(void 0)};return t.addEventListener("focusin",n),t.addEventListener("focusout",s),this._getItems(),{destroy:()=>{this._rootElement=void 0,t.removeEventListener("keydown",this._onKeyDown),t.removeEventListener("focusin",n),t.removeEventListener("focusout",s),this._focusedIndexStore.set(void 0)}}});T(this,"registerTrigger",t=>(this._triggerElement=t.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')??t,{destroy:()=>{this._triggerElement=void 0}}));T(this,"_onKeyDown",t=>{var n;switch(t.key){case"ArrowUp":t.preventDefault(),this.focusPrev();break;case"ArrowDown":t.preventDefault(),this.focusNext();break;case"ArrowLeft":this._requestClose();break;case"ArrowRight":this.clickFocusedItem();break;case"Tab":{const s=this.getCurrentFocusedIdx();if(s===void 0||this.parentContext)break;(!t.shiftKey&&s===this._getItems().length-1||t.shiftKey&&s===0)&&(t.preventDefault(),(n=this._triggerElement)==null||n.focus());break}}});T(this,"_requestClose",()=>{var t;(t=this._rootElement)==null||t.dispatchEvent(new Eo)});T(this,"getCurrentFocusedIdx",()=>{const t=this._getItems().findIndex(s=>s===document.activeElement),n=t===-1?void 0:t;return this._focusedIndexStore.set(n),n});T(this,"setFocusedIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const s=Ze(t,n.length);this._focusedIndexStore.set(s)});T(this,"focusIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const s=Ze(t,n.length),o=n[s];o==null||o.focus(),this._focusedIndexStore.set(s)});T(this,"popNestedFocus",()=>{if(this._parentContext){this._focusedIndexStore.set(void 0);const t=this._lastFocusAnchorElement,n=t?this._parentContext._getItems().indexOf(t):void 0;return n===void 0?(this._parentContext.focusIdx(0),!0):(this._parentContext.focusIdx(n),!0)}return!1});T(this,"focusNext",()=>{const t=this._getItems();if(t.length===0)return;const n=Ze(t.findIndex(s=>s===document.activeElement)+1,t.length);t[n].focus(),this._focusedIndexStore.set(n)});T(this,"focusPrev",()=>{var s;const t=this._getItems();if(t.length===0)return;const n=Ze(t.findIndex(o=>o===document.activeElement)-1,t.length);(s=t[n])==null||s.focus(),this._focusedIndexStore.set(n)});T(this,"clickFocusedItem",async()=>{const t=document.activeElement;t&&(t.click(),await Ks())});this._parentContext=t}get rootElement(){return this._rootElement}get triggerElement(){return this._triggerElement}get parentContext(){return this._parentContext}};T(Ce,"CONTEXT_KEY","augment-dropdown-menu-focus"),T(Ce,"ITEM_CLASS","js-dropdown-menu__focusable-item");let ee=Ce;function Ze(e,t){return(e%t+t)%t}function Yi(e){let t,n,s,o,r;const a=e[11].default,i=j(a,e,e[13],null);return{c(){t=H("div"),i&&i.c(),I(t,"class",n=ae(`l-dropdown-menu-augment__contents l-dropdown-menu-augment__contents--size-${e[6]}`)+" svelte-mbbzty")},m(u,l){k(u,t,l),i&&i.m(t,null),s=!0,o||(r=js(e[8].registerRoot(t)),o=!0)},p(u,l){i&&i.p&&(!s||8192&l)&&q(i,a,u,u[13],s?W(a,u[13],l,null):X(u[13]),null),(!s||64&l&&n!==(n=ae(`l-dropdown-menu-augment__contents l-dropdown-menu-augment__contents--size-${u[6]}`)+" svelte-mbbzty"))&&I(t,"class",n)},i(u){s||(y(i,u),s=!0)},o(u){v(i,u),s=!1},d(u){u&&S(t),i&&i.d(u),o=!1,r()}}}function Gi(e){let t,n,s;return n=new No({props:{size:e[6],insetContent:!0,includeBackground:!1,$$slots:{default:[Yi]},$$scope:{ctx:e}}}),{c(){t=H("div"),A(n.$$.fragment),I(t,"class","l-dropdown-menu-augment__container svelte-mbbzty")},m(o,r){k(o,t,r),D(n,t,null),s=!0},p(o,r){const a={};64&r&&(a.size=o[6]),8256&r&&(a.$$scope={dirty:r,ctx:o}),n.$set(a)},i(o){s||(y(n.$$.fragment,o),s=!0)},o(o){v(n.$$.fragment,o),s=!1},d(o){o&&S(t),x(n)}}}function Vi(e){let t,n,s,o;return t=new vo({props:{onEscapeKeyDown:e[0],onClickOutside:e[1],onRequestClose:e[2],side:e[3],align:e[4],$$slots:{default:[Gi]},$$scope:{ctx:e}}}),t.$on("keydown",e[12]),{c(){A(t.$$.fragment)},m(r,a){D(t,r,a),n=!0,s||(o=te(window,"keydown",e[9]),s=!0)},p(r,[a]){const i={};1&a&&(i.onEscapeKeyDown=r[0]),2&a&&(i.onClickOutside=r[1]),4&a&&(i.onRequestClose=r[2]),8&a&&(i.side=r[3]),16&a&&(i.align=r[4]),8256&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(y(t.$$.fragment,r),n=!0)},o(r){v(t.$$.fragment,r),n=!1},d(r){x(t,r),s=!1,o()}}}const ke="augment-dropdown-menu-content";function Ki(e,t,n){let s,o,r,a=re;e.$$.on_destroy.push(()=>a());let{$$slots:i={},$$scope:u}=t,{size:l=2}=t,{onEscapeKeyDown:c=()=>{}}=t,{onClickOutside:m=()=>{}}=t,{onRequestClose:f=()=>{}}=t,{side:p="top"}=t,{align:h="center"}=t;const b={size:Vs(l)},E=b.size;oe(e,E,g=>n(6,r=g)),Bs(ke,b);const N=B(ee.CONTEXT_KEY),F=B(Ve.CONTEXT_KEY);return e.$$set=g=>{"size"in g&&n(10,l=g.size),"onEscapeKeyDown"in g&&n(0,c=g.onEscapeKeyDown),"onClickOutside"in g&&n(1,m=g.onClickOutside),"onRequestClose"in g&&n(2,f=g.onRequestClose),"side"in g&&n(3,p=g.side),"align"in g&&n(4,h=g.align),"$$scope"in g&&n(13,u=g.$$scope)},e.$$.update=()=>{1024&e.$$.dirty&&E.set(l)},n(5,s=F.state),a(),a=io(s,g=>n(14,o=g)),[c,m,f,p,h,s,r,E,N,function(g){if(o.open&&g.key==="Tab"&&!g.shiftKey){if(N.getCurrentFocusedIdx()!==void 0)return;g.preventDefault(),N==null||N.focusIdx(0)}},l,i,function(g){le.call(this,e,g)},u]}class eo extends Y{constructor(t){super(),G(this,t,Ki,Vi,V,{size:10,onEscapeKeyDown:0,onClickOutside:1,onRequestClose:2,side:3,align:4})}}const Bi=e=>({}),As=e=>({}),ji=e=>({}),Ds=e=>({});function xs(e){let t,n;const s=e[14].iconLeft,o=j(s,e,e[18],Ds);return{c(){t=H("div"),o&&o.c(),I(t,"class","c-dropdown-menu-augment__item-icon svelte-toijgi")},m(r,a){k(r,t,a),o&&o.m(t,null),n=!0},p(r,a){o&&o.p&&(!n||262144&a)&&q(o,s,r,r[18],n?W(s,r[18],a,ji):X(r[18]),Ds)},i(r){n||(y(o,r),n=!0)},o(r){v(o,r),n=!1},d(r){r&&S(t),o&&o.d(r)}}}function qi(e){let t;const n=e[14].default,s=j(n,e,e[18],null);return{c(){s&&s.c()},m(o,r){s&&s.m(o,r),t=!0},p(o,r){s&&s.p&&(!t||262144&r)&&q(s,n,o,o[18],t?W(n,o[18],r,null):X(o[18]),null)},i(o){t||(y(s,o),t=!0)},o(o){v(s,o),t=!1},d(o){s&&s.d(o)}}}function Fs(e){let t,n;const s=e[14].iconRight,o=j(s,e,e[18],As);return{c(){t=H("div"),o&&o.c(),I(t,"class","c-dropdown-menu-augment__item-icon svelte-toijgi")},m(r,a){k(r,t,a),o&&o.m(t,null),n=!0},p(r,a){o&&o.p&&(!n||262144&a)&&q(o,s,r,r[18],n?W(s,r[18],a,Bi):X(r[18]),As)},i(r){n||(y(o,r),n=!0)},o(r){v(o,r),n=!1},d(r){r&&S(t),o&&o.d(r)}}}function Xi(e){let t,n,s,o,r,a=e[11].iconLeft&&xs(e);n=new Xs({props:{size:e[7],$$slots:{default:[qi]},$$scope:{ctx:e}}});let i=e[11].iconRight&&Fs(e);return{c(){a&&a.c(),t=Ue(),A(n.$$.fragment),s=Ue(),i&&i.c(),o=Ws()},m(u,l){a&&a.m(u,l),k(u,t,l),D(n,u,l),k(u,s,l),i&&i.m(u,l),k(u,o,l),r=!0},p(u,l){u[11].iconLeft?a?(a.p(u,l),2048&l&&y(a,1)):(a=xs(u),a.c(),y(a,1),a.m(t.parentNode,t)):a&&(Le(),v(a,1,1,()=>{a=null}),Pe());const c={};128&l&&(c.size=u[7]),262144&l&&(c.$$scope={dirty:l,ctx:u}),n.$set(c),u[11].iconRight?i?(i.p(u,l),2048&l&&y(i,1)):(i=Fs(u),i.c(),y(i,1),i.m(o.parentNode,o)):i&&(Le(),v(i,1,1,()=>{i=null}),Pe())},i(u){r||(y(a),y(n.$$.fragment,u),y(i),r=!0)},o(u){v(a),v(n.$$.fragment,u),v(i),r=!1},d(u){u&&(S(t),S(s),S(o)),a&&a.d(u),x(n,u),i&&i.d(u)}}}function Wi(e){let t,n;const s=[{class:e[5]},{size:e[7]},{variant:e[4]?"solid":"ghost"},{color:e[2]??(e[4]?"accent":"neutral")},{highContrast:!e[2]&&!e[4]},{alignment:"left"},{disabled:e[1]},je("dropdown-menu-item","highlighted",e[0]),je("dropdown-menu-item","disabled",e[1]),e[6]];let o={$$slots:{default:[Xi]},$$scope:{ctx:e}};for(let r=0;r<s.length;r+=1)o=_(o,s[r]);return t=new Io({props:o}),t.$on("click",e[15]),t.$on("mouseover",e[16]),t.$on("mouseleave",e[17]),t.$on("mousedown",zi),{c(){A(t.$$.fragment)},m(r,a){D(t,r,a),n=!0},p(r,[a]){const i=247&a?pe(s,[32&a&&{class:r[5]},128&a&&{size:r[7]},16&a&&{variant:r[4]?"solid":"ghost"},20&a&&{color:r[2]??(r[4]?"accent":"neutral")},20&a&&{highContrast:!r[2]&&!r[4]},s[5],2&a&&{disabled:r[1]},1&a&&me(je("dropdown-menu-item","highlighted",r[0])),2&a&&me(je("dropdown-menu-item","disabled",r[1])),64&a&&me(r[6])]):{};264320&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(y(t.$$.fragment,r),n=!0)},o(r){v(t.$$.fragment,r),n=!1},d(r){x(t,r)}}}const zi=e=>{e.preventDefault(),e.stopPropagation()};function Hi(e,t,n){let s,o,r;const a=["highlight","disabled","color","onSelect"];let i,u,l=K(t,a),{$$slots:c={},$$scope:m}=t;const f=qs(c);let{highlight:p}=t,{disabled:h}=t,{color:b}=t,{onSelect:E=()=>{}}=t,N=!1;const F=B(ke),g=B(Ve.CONTEXT_KEY),Ie=B(ee.CONTEXT_KEY),vn=F.size;oe(e,vn,w=>n(7,u=w));const Nn=g.state;function Tt(w){var _n;if(h)return;const Tn=(_n=Ie.rootElement)==null?void 0:_n.querySelectorAll(`.${ee.ITEM_CLASS}`);if(!Tn)return;const In=Array.from(Tn).findIndex(so=>so===w);In!==-1&&Ie.setFocusedIdx(In)}return oe(e,Nn,w=>n(13,i=w)),e.$$set=w=>{t=_(_({},t),ie(w)),n(22,l=K(t,a)),"highlight"in w&&n(0,p=w.highlight),"disabled"in w&&n(1,h=w.disabled),"color"in w&&n(2,b=w.color),"onSelect"in w&&n(3,E=w.onSelect),"$$scope"in w&&n(18,m=w.$$scope)},e.$$.update=()=>{n(12,{class:s,...o}=l,s,(n(6,o),n(22,l))),4099&e.$$.dirty&&n(5,r=[h?"":ee.ITEM_CLASS,"c-dropdown-menu-augment__item",p?"c-dropdown-menu-augment__item--highlighted":"",s].join(" ")),8192&e.$$.dirty&&(i.open||n(4,N=!1))},[p,h,b,E,N,r,o,u,vn,Nn,Tt,f,s,i,c,w=>{w.currentTarget instanceof HTMLElement&&Tt(w.currentTarget),E(w)},w=>{n(4,N=!0),w.currentTarget instanceof HTMLElement&&Tt(w.currentTarget)},()=>{n(4,N=!1)},m]}class bn extends Y{constructor(t){super(),G(this,t,Hi,Wi,V,{highlight:0,disabled:1,color:2,onSelect:3})}}function Ji(e){let t;const n=e[1].default,s=j(n,e,e[2],null);return{c(){s&&s.c()},m(o,r){s&&s.m(o,r),t=!0},p(o,r){s&&s.p&&(!t||4&r)&&q(s,n,o,o[2],t?W(n,o[2],r,null):X(o[2]),null)},i(o){t||(y(s,o),t=!0)},o(o){v(s,o),t=!1},d(o){s&&s.d(o)}}}function Zi(e){let t,n;return t=new Mi({props:{slot:"iconLeft"}}),{c(){A(t.$$.fragment)},m(s,o){D(t,s,o),n=!0},p:re,i(s){n||(y(t.$$.fragment,s),n=!0)},o(s){v(t.$$.fragment,s),n=!1},d(s){x(t,s)}}}function Qi(e){let t,n;const s=[{class:"c-dropdown-menu-augment__breadcrumb-back-chevron"},e[0]];let o={$$slots:{iconLeft:[Zi],default:[Ji]},$$scope:{ctx:e}};for(let r=0;r<s.length;r+=1)o=_(o,s[r]);return t=new bn({props:o}),{c(){A(t.$$.fragment)},m(r,a){D(t,r,a),n=!0},p(r,[a]){const i=1&a?pe(s,[s[0],me(r[0])]):{};4&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(y(t.$$.fragment,r),n=!0)},o(r){v(t.$$.fragment,r),n=!1},d(r){x(t,r)}}}function eu(e,t,n){const s=[];let o=K(t,s),{$$slots:r={},$$scope:a}=t;return e.$$set=i=>{t=_(_({},t),ie(i)),n(0,o=K(t,s)),"$$scope"in i&&n(2,a=i.$$scope)},[o,r,a]}function tu(e){let t,n,s=[{xmlns:"http://www.w3.org/2000/svg"},{width:"16"},{height:"16"},{"data-ds-icon":"fa"},{viewBox:"0 0 16 16"},e[0]],o={};for(let r=0;r<s.length;r+=1)o=_(o,s[r]);return{c(){t=Mt("svg"),n=new uo(!0),this.h()},l(r){t=lo(r,"svg",{xmlns:!0,width:!0,height:!0,"data-ds-icon":!0,viewBox:!0});var a=co(t);n=mo(a,!0),a.forEach(S),this.h()},h(){n.a=null,wn(t,o)},m(r,a){po(r,t,a),n.m('<path fill-opacity=".01" d="M0 0h16v16H0z"/><path fill-opacity=".365" d="M10.149 7.602a.56.56 0 0 1 0 .794l-3.5 3.502a.562.562 0 0 1-.795-.795L8.956 8 5.852 4.898a.562.562 0 0 1 .795-.795z"/>',t)},p(r,[a]){wn(t,o=pe(s,[{xmlns:"http://www.w3.org/2000/svg"},{width:"16"},{height:"16"},{"data-ds-icon":"fa"},{viewBox:"0 0 16 16"},1&a&&r[0]]))},i:re,o:re,d(r){r&&S(t)}}}function nu(e,t,n){return e.$$set=s=>{n(0,t=_(_({},t),ie(s)))},[t=ie(t)]}class su extends Y{constructor(t){super(),G(this,t,nu,tu,V,{})}}function ru(e){let t;const n=e[1].default,s=j(n,e,e[2],null);return{c(){s&&s.c()},m(o,r){s&&s.m(o,r),t=!0},p(o,r){s&&s.p&&(!t||4&r)&&q(s,n,o,o[2],t?W(n,o[2],r,null):X(o[2]),null)},i(o){t||(y(s,o),t=!0)},o(o){v(s,o),t=!1},d(o){s&&s.d(o)}}}function ou(e){let t,n;return t=new su({props:{slot:"iconRight"}}),{c(){A(t.$$.fragment)},m(s,o){D(t,s,o),n=!0},p:re,i(s){n||(y(t.$$.fragment,s),n=!0)},o(s){v(t.$$.fragment,s),n=!1},d(s){x(t,s)}}}function au(e){let t,n;const s=[{class:"c-dropdown-menu-augment__breadcrumb-chevron"},e[0]];let o={$$slots:{iconRight:[ou],default:[ru]},$$scope:{ctx:e}};for(let r=0;r<s.length;r+=1)o=_(o,s[r]);return t=new bn({props:o}),{c(){A(t.$$.fragment)},m(r,a){D(t,r,a),n=!0},p(r,[a]){const i=1&a?pe(s,[s[0],me(r[0])]):{};4&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(y(t.$$.fragment,r),n=!0)},o(r){v(t.$$.fragment,r),n=!1},d(r){x(t,r)}}}function iu(e,t,n){const s=[];let o=K(t,s),{$$slots:r={},$$scope:a}=t;return e.$$set=i=>{t=_(_({},t),ie(i)),n(0,o=K(t,s)),"$$scope"in i&&n(2,a=i.$$scope)},[o,r,a]}class to extends Y{constructor(t){super(),G(this,t,iu,au,V,{})}}function uu(e){let t;const n=e[3].default,s=j(n,e,e[4],null);return{c(){s&&s.c()},m(o,r){s&&s.m(o,r),t=!0},p(o,r){s&&s.p&&(!t||16&r)&&q(s,n,o,o[4],t?W(n,o[4],r,null):X(o[4]),null)},i(o){t||(y(s,o),t=!0)},o(o){v(s,o),t=!1},d(o){s&&s.d(o)}}}function lu(e){let t,n,s,o;return n=new Xs({props:{size:e[0],weight:"regular",$$slots:{default:[uu]},$$scope:{ctx:e}}}),{c(){t=H("div"),A(n.$$.fragment),I(t,"class",s=ae(e[1])+" svelte-gehsvg")},m(r,a){k(r,t,a),D(n,t,null),o=!0},p(r,[a]){const i={};1&a&&(i.size=r[0]),16&a&&(i.$$scope={dirty:a,ctx:r}),n.$set(i),(!o||2&a&&s!==(s=ae(r[1])+" svelte-gehsvg"))&&I(t,"class",s)},i(r){o||(y(n.$$.fragment,r),o=!0)},o(r){v(n.$$.fragment,r),o=!1},d(r){r&&S(t),x(n)}}}function cu(e,t,n){let s,o,{$$slots:r={},$$scope:a}=t;const i=B(ke).size;return oe(e,i,u=>n(0,o=u)),e.$$set=u=>{"$$scope"in u&&n(4,a=u.$$scope)},e.$$.update=()=>{1&e.$$.dirty&&n(1,s=["c-dropdown-menu-augment__label-item",`c-dropdown-menu-augment__label-item--size-${o}`].join(" "))},[o,s,i,r,a]}function du(e){let t;const n=e[16].default,s=j(n,e,e[18],null);return{c(){s&&s.c()},m(o,r){s&&s.m(o,r),t=!0},p(o,r){s&&s.p&&(!t||262144&r)&&q(s,n,o,o[18],t?W(n,o[18],r,null):X(o[18]),null)},i(o){t||(y(s,o),t=!0)},o(o){v(s,o),t=!1},d(o){s&&s.d(o)}}}function mu(e){let t,n;const s=[{defaultOpen:e[0]},{open:e[1]},{onOpenChange:e[2]},{delayDurationMs:e[3]},{onHoverStart:e[5]},{onHoverEnd:e[6]},{triggerOn:e[7]},{nested:e[4]},e[9]];let o={$$slots:{default:[du]},$$scope:{ctx:e}};for(let r=0;r<s.length;r+=1)o=_(o,s[r]);return t=new To({props:o}),e[17](t),{c(){A(t.$$.fragment)},m(r,a){D(t,r,a),n=!0},p(r,[a]){const i=767&a?pe(s,[1&a&&{defaultOpen:r[0]},2&a&&{open:r[1]},4&a&&{onOpenChange:r[2]},8&a&&{delayDurationMs:r[3]},32&a&&{onHoverStart:r[5]},64&a&&{onHoverEnd:r[6]},128&a&&{triggerOn:r[7]},16&a&&{nested:r[4]},512&a&&me(r[9])]):{};262144&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(y(t.$$.fragment,r),n=!0)},o(r){v(t.$$.fragment,r),n=!1},d(r){e[17](null),x(t,r)}}}function pu(e,t,n){const s=["defaultOpen","open","onOpenChange","delayDurationMs","nested","onHoverStart","onHoverEnd","triggerOn","requestOpen","requestClose","focusIdx","setFocusedIdx","getCurrentFocusedIdx","focusedIndex"];let o,r=K(t,s),{$$slots:a={},$$scope:i}=t,{defaultOpen:u}=t,{open:l}=t,{onOpenChange:c}=t,{delayDurationMs:m}=t,{nested:f}=t,{onHoverStart:p=()=>{}}=t,{onHoverEnd:h=()=>{}}=t,{triggerOn:b=[Yt.Click]}=t;const E=B(ee.CONTEXT_KEY),N=new ee(E);Bs(ee.CONTEXT_KEY,N);const F=N.focusedIndex;return e.$$set=g=>{t=_(_({},t),ie(g)),n(9,r=K(t,s)),"defaultOpen"in g&&n(0,u=g.defaultOpen),"open"in g&&n(1,l=g.open),"onOpenChange"in g&&n(2,c=g.onOpenChange),"delayDurationMs"in g&&n(3,m=g.delayDurationMs),"nested"in g&&n(4,f=g.nested),"onHoverStart"in g&&n(5,p=g.onHoverStart),"onHoverEnd"in g&&n(6,h=g.onHoverEnd),"triggerOn"in g&&n(7,b=g.triggerOn),"$$scope"in g&&n(18,i=g.$$scope)},[u,l,c,m,f,p,h,b,o,r,()=>o==null?void 0:o.requestOpen(),()=>o==null?void 0:o.requestClose(),g=>N.focusIdx(g),g=>N.setFocusedIdx(g),()=>N.getCurrentFocusedIdx(),F,a,function(g){zt[g?"unshift":"push"](()=>{o=g,n(8,o)})},i]}class no extends Y{constructor(t){super(),G(this,t,pu,mu,V,{defaultOpen:0,open:1,onOpenChange:2,delayDurationMs:3,nested:4,onHoverStart:5,onHoverEnd:6,triggerOn:7,requestOpen:10,requestClose:11,focusIdx:12,setFocusedIdx:13,getCurrentFocusedIdx:14,focusedIndex:15})}get requestOpen(){return this.$$.ctx[10]}get requestClose(){return this.$$.ctx[11]}get focusIdx(){return this.$$.ctx[12]}get setFocusedIdx(){return this.$$.ctx[13]}get getCurrentFocusedIdx(){return this.$$.ctx[14]}get focusedIndex(){return this.$$.ctx[15]}}function fu(e){let t,n;return{c(){t=H("div"),I(t,"class",n=ae(`c-separator c-separator--size-${e[0]===.5?"0_5":e[0]} c-separator--orientation-${e[1]} ${e[3]}`)+" svelte-o0csoy"),fe(t,"c-separator--current-color",e[2])},m(s,o){k(s,t,o)},p(s,[o]){11&o&&n!==(n=ae(`c-separator c-separator--size-${s[0]===.5?"0_5":s[0]} c-separator--orientation-${s[1]} ${s[3]}`)+" svelte-o0csoy")&&I(t,"class",n),15&o&&fe(t,"c-separator--current-color",s[2])},i:re,o:re,d(s){s&&S(t)}}}function gu(e,t,n){let{size:s=1}=t,{orientation:o="horizontal"}=t,{useCurrentColor:r=!1}=t,{class:a=""}=t;return e.$$set=i=>{"size"in i&&n(0,s=i.size),"orientation"in i&&n(1,o=i.orientation),"useCurrentColor"in i&&n(2,r=i.useCurrentColor),"class"in i&&n(3,a=i.class)},[s,o,r,a]}class hu extends Y{constructor(t){super(),G(this,t,gu,fu,V,{size:0,orientation:1,useCurrentColor:2,class:3})}}function bu(e){let t,n,s,o;return n=new hu({props:{size:4,orientation:"horizontal"}}),{c(){t=H("div"),A(n.$$.fragment),I(t,"class",s=ae(`c-dropdown-menu-augment__separator c-dropdown-menu-augment__separator--size-${e[0]}`)+" svelte-24h9u")},m(r,a){k(r,t,a),D(n,t,null),o=!0},p(r,[a]){(!o||1&a&&s!==(s=ae(`c-dropdown-menu-augment__separator c-dropdown-menu-augment__separator--size-${r[0]}`)+" svelte-24h9u"))&&I(t,"class",s)},i(r){o||(y(n.$$.fragment,r),o=!0)},o(r){v(n.$$.fragment,r),o=!1},d(r){r&&S(t),x(n)}}}function yu(e,t,n){let s;const o=B(ke).size;return oe(e,o,r=>n(0,s=r)),[s,o]}function Eu(e){let t;const n=e[1].default,s=j(n,e,e[2],null);return{c(){s&&s.c()},m(o,r){s&&s.m(o,r),t=!0},p(o,r){s&&s.p&&(!t||4&r)&&q(s,n,o,o[2],t?W(n,o[2],r,null):X(o[2]),null)},i(o){t||(y(s,o),t=!0)},o(o){v(s,o),t=!1},d(o){s&&s.d(o)}}}function vu(e){let t,n;const s=[{nested:!0},{triggerOn:[Yt.Click,Yt.Hover]},e[0]];let o={$$slots:{default:[Eu]},$$scope:{ctx:e}};for(let r=0;r<s.length;r+=1)o=_(o,s[r]);return t=new no({props:o}),{c(){A(t.$$.fragment)},m(r,a){D(t,r,a),n=!0},p(r,[a]){const i=1&a?pe(s,[s[0],s[1],me(r[0])]):{};4&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(y(t.$$.fragment,r),n=!0)},o(r){v(t.$$.fragment,r),n=!1},d(r){x(t,r)}}}function Nu(e,t,n){const s=[];let o=K(t,s),{$$slots:r={},$$scope:a}=t;return e.$$set=i=>{t=_(_({},t),ie(i)),n(0,o=K(t,s)),"$$scope"in i&&n(2,a=i.$$scope)},[o,r,a]}function Tu(e){let t;const n=e[5].default,s=j(n,e,e[6],null);return{c(){s&&s.c()},m(o,r){s&&s.m(o,r),t=!0},p(o,r){s&&s.p&&(!t||64&r)&&q(s,n,o,o[6],t?W(n,o[6],r,null):X(o[6]),null)},i(o){t||(y(s,o),t=!0)},o(o){v(s,o),t=!1},d(o){s&&s.d(o)}}}function Iu(e){let t,n;const s=[e[3],{side:"right"},{align:"start"},{size:e[0]}];let o={$$slots:{default:[Tu]},$$scope:{ctx:e}};for(let r=0;r<s.length;r+=1)o=_(o,s[r]);return t=new eo({props:o}),{c(){A(t.$$.fragment)},m(r,a){D(t,r,a),n=!0},p(r,[a]){const i=9&a?pe(s,[8&a&&me(r[3]),s[1],s[2],1&a&&{size:r[0]}]):{};64&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(y(t.$$.fragment,r),n=!0)},o(r){v(t.$$.fragment,r),n=!1},d(r){x(t,r)}}}function _u(e,t,n){const s=[];let o,r,a=K(t,s),{$$slots:i={},$$scope:u}=t;const l=B(ke).size;oe(e,l,p=>n(0,r=p));const c=B(ee.CONTEXT_KEY),m=B(Ve.CONTEXT_KEY),f=fo(m.state,p=>p.open);return oe(e,f,p=>n(4,o=p)),e.$$set=p=>{t=_(_({},t),ie(p)),n(3,a=K(t,s)),"$$scope"in p&&n(6,u=p.$$scope)},e.$$.update=()=>{16&e.$$.dirty&&o&&Ks().then(()=>c==null?void 0:c.focusIdx(0)),16&e.$$.dirty&&!o&&(c==null||c.popNestedFocus())},[r,l,f,a,o,i,u]}function wu(e){let t;const n=e[2].default,s=j(n,e,e[3],null);return{c(){s&&s.c()},m(o,r){s&&s.m(o,r),t=!0},p(o,r){s&&s.p&&(!t||8&r)&&q(s,n,o,o[3],t?W(n,o[3],r,null):X(o[3]),null)},i(o){t||(y(s,o),t=!0)},o(o){v(s,o),t=!1},d(o){s&&s.d(o)}}}function $u(e){let t,n;return t=new to({props:{highlight:e[0].open,$$slots:{default:[wu]},$$scope:{ctx:e}}}),{c(){A(t.$$.fragment)},m(s,o){D(t,s,o),n=!0},p(s,o){const r={};1&o&&(r.highlight=s[0].open),8&o&&(r.$$scope={dirty:o,ctx:s}),t.$set(r)},i(s){n||(y(t.$$.fragment,s),n=!0)},o(s){v(t.$$.fragment,s),n=!1},d(s){x(t,s)}}}function Su(e){let t,n;return t=new zs({props:{$$slots:{default:[$u]},$$scope:{ctx:e}}}),{c(){A(t.$$.fragment)},m(s,o){D(t,s,o),n=!0},p(s,[o]){const r={};9&o&&(r.$$scope={dirty:o,ctx:s}),t.$set(r)},i(s){n||(y(t.$$.fragment,s),n=!0)},o(s){v(t.$$.fragment,s),n=!1},d(s){x(t,s)}}}function Ou(e,t,n){let s,{$$slots:o={},$$scope:r}=t;const a=B(Ve.CONTEXT_KEY).state;return oe(e,a,i=>n(0,s=i)),e.$$set=i=>{"$$scope"in i&&n(3,r=i.$$scope)},[s,a,o,r]}const ku=e=>({}),Cs=e=>({}),Ru=e=>({}),Us=e=>({}),Au=e=>({}),Ls=e=>({});function Ps(e){let t,n;const s=e[11].label,o=j(s,e,e[22],Ls);return{c(){t=H("label"),o&&o.c(),I(t,"class","c-text-field-label svelte-vuqlvc"),I(t,"for",e[7])},m(r,a){k(r,t,a),o&&o.m(t,null),n=!0},p(r,a){o&&o.p&&(!n||4194304&a)&&q(o,s,r,r[22],n?W(s,r[22],a,Au):X(r[22]),Ls),(!n||128&a)&&I(t,"for",r[7])},i(r){n||(y(o,r),n=!0)},o(r){v(o,r),n=!1},d(r){r&&S(t),o&&o.d(r)}}}function Ms(e){let t,n;const s=e[11].iconLeft,o=j(s,e,e[22],Us);return{c(){t=H("div"),o&&o.c(),I(t,"class","c-text-field__slot c-base-text-input__slot")},m(r,a){k(r,t,a),o&&o.m(t,null),n=!0},p(r,a){o&&o.p&&(!n||4194304&a)&&q(o,s,r,r[22],n?W(s,r[22],a,Ru):X(r[22]),Us)},i(r){n||(y(o,r),n=!0)},o(r){v(o,r),n=!1},d(r){r&&S(t),o&&o.d(r)}}}function Ys(e){let t,n;const s=e[11].iconRight,o=j(s,e,e[22],Cs);return{c(){t=H("div"),o&&o.c(),I(t,"class","c-text-field__slot c-base-text-input__slot")},m(r,a){k(r,t,a),o&&o.m(t,null),n=!0},p(r,a){o&&o.p&&(!n||4194304&a)&&q(o,s,r,r[22],n?W(s,r[22],a,ku):X(r[22]),Cs)},i(r){n||(y(o,r),n=!0)},o(r){v(o,r),n=!1},d(r){r&&S(t),o&&o.d(r)}}}function Du(e){let t,n,s,o,r,a,i,u,l=e[9].iconLeft&&Ms(e),c=[{spellcheck:"false"},{class:s=`c-text-field__input c-base-text-input__input ${e[6]}`},{id:e[7]},e[5]],m={};for(let p=0;p<c.length;p+=1)m=_(m,c[p]);let f=e[9].iconRight&&Ys(e);return{c(){l&&l.c(),t=Ue(),n=H("input"),o=Ue(),f&&f.c(),r=Ws(),$n(n,m),fe(n,"svelte-vuqlvc",!0)},m(p,h){l&&l.m(p,h),k(p,t,h),k(p,n,h),n.autofocus&&n.focus(),e[20](n),Sn(n,e[1]),k(p,o,h),f&&f.m(p,h),k(p,r,h),a=!0,i||(u=[te(n,"input",e[21]),te(n,"change",e[8]),te(n,"click",e[12]),te(n,"keydown",e[13]),te(n,"input",e[14]),te(n,"blur",e[15]),te(n,"dblclick",e[16]),te(n,"focus",e[17]),te(n,"mouseup",e[18]),te(n,"selectionchange",e[19])],i=!0)},p(p,h){p[9].iconLeft?l?(l.p(p,h),512&h&&y(l,1)):(l=Ms(p),l.c(),y(l,1),l.m(t.parentNode,t)):l&&(Le(),v(l,1,1,()=>{l=null}),Pe()),$n(n,m=pe(c,[{spellcheck:"false"},(!a||64&h&&s!==(s=`c-text-field__input c-base-text-input__input ${p[6]}`))&&{class:s},(!a||128&h)&&{id:p[7]},32&h&&p[5]])),2&h&&n.value!==p[1]&&Sn(n,p[1]),fe(n,"svelte-vuqlvc",!0),p[9].iconRight?f?(f.p(p,h),512&h&&y(f,1)):(f=Ys(p),f.c(),y(f,1),f.m(r.parentNode,r)):f&&(Le(),v(f,1,1,()=>{f=null}),Pe())},i(p){a||(y(l),y(f),a=!0)},o(p){v(l),v(f),a=!1},d(p){p&&(S(t),S(n),S(o),S(r)),l&&l.d(p),e[20](null),f&&f.d(p),i=!1,ho(u)}}}function xu(e){let t,n,s,o,r=e[9].label&&Ps(e);return s=new _o({props:{variant:e[2],size:e[3],color:e[4],$$slots:{default:[Du]},$$scope:{ctx:e}}}),{c(){t=H("div"),r&&r.c(),n=Ue(),A(s.$$.fragment),I(t,"class","c-text-field svelte-vuqlvc"),fe(t,"c-text-field--has-left-icon",e[9].iconLeft!==void 0),fe(t,"c-text-field--has-right-icon",e[9].iconRight!==void 0)},m(a,i){k(a,t,i),r&&r.m(t,null),Gs(t,n),D(s,t,null),o=!0},p(a,[i]){a[9].label?r?(r.p(a,i),512&i&&y(r,1)):(r=Ps(a),r.c(),y(r,1),r.m(t,n)):r&&(Le(),v(r,1,1,()=>{r=null}),Pe());const u={};4&i&&(u.variant=a[2]),8&i&&(u.size=a[3]),16&i&&(u.color=a[4]),4195043&i&&(u.$$scope={dirty:i,ctx:a}),s.$set(u),(!o||512&i)&&fe(t,"c-text-field--has-left-icon",a[9].iconLeft!==void 0),(!o||512&i)&&fe(t,"c-text-field--has-right-icon",a[9].iconRight!==void 0)},i(a){o||(y(r),y(s.$$.fragment,a),o=!0)},o(a){v(r),v(s.$$.fragment,a),o=!1},d(a){a&&S(t),r&&r.d(),x(s)}}}function Fu(e,t,n){let s,o,r;const a=["variant","size","color","textInput","value","id"];let i=K(t,a),{$$slots:u={},$$scope:l}=t;const c=qs(u),m=go();let{variant:f="surface"}=t,{size:p=2}=t,{color:h}=t,{textInput:b}=t,{value:E=""}=t,{id:N}=t;const F=`text-field-${Math.random().toString(36).substring(2,11)}`;return e.$$set=g=>{t=_(_({},t),ie(g)),n(25,i=K(t,a)),"variant"in g&&n(2,f=g.variant),"size"in g&&n(3,p=g.size),"color"in g&&n(4,h=g.color),"textInput"in g&&n(0,b=g.textInput),"value"in g&&n(1,E=g.value),"id"in g&&n(10,N=g.id),"$$scope"in g&&n(22,l=g.$$scope)},e.$$.update=()=>{1024&e.$$.dirty&&n(7,s=N||F),n(6,{class:o,...r}=i,o,(n(5,r),n(25,i)))},[b,E,f,p,h,r,o,s,function(g){m("change",g)},c,N,u,function(g){le.call(this,e,g)},function(g){le.call(this,e,g)},function(g){le.call(this,e,g)},function(g){le.call(this,e,g)},function(g){le.call(this,e,g)},function(g){le.call(this,e,g)},function(g){le.call(this,e,g)},function(g){le.call(this,e,g)},function(g){zt[g?"unshift":"push"](()=>{b=g,n(0,b)})},function(){E=this.value,n(1,E)},l]}class Cu extends Y{constructor(t){super(),G(this,t,Fu,xu,V,{variant:2,size:3,color:4,textInput:0,value:1,id:10})}}function Uu(e){let t,n,s,o,r;const a=[{class:ee.ITEM_CLASS},{size:e[1]},e[4]];function i(l){e[5](l)}let u={};for(let l=0;l<a.length;l+=1)u=_(u,a[l]);return e[0]!==void 0&&(u.value=e[0]),n=new Cu({props:u}),zt.push(()=>bo(n,"value",i)),{c(){t=H("div"),A(n.$$.fragment),I(t,"class",o=ae(e[2])+" svelte-1xu00bc")},m(l,c){k(l,t,c),D(n,t,null),r=!0},p(l,[c]){const m=18&c?pe(a,[a[0],2&c&&{size:l[1]},16&c&&me(l[4])]):{};!s&&1&c&&(s=!0,m.value=l[0],yo(()=>s=!1)),n.$set(m),(!r||4&c&&o!==(o=ae(l[2])+" svelte-1xu00bc"))&&I(t,"class",o)},i(l){r||(y(n.$$.fragment,l),r=!0)},o(l){v(n.$$.fragment,l),r=!1},d(l){l&&S(t),x(n)}}}function Lu(e,t,n){let s;const o=["value"];let r,a=K(t,o),{value:i=""}=t;const u=B(ke).size;return oe(e,u,l=>n(1,r=l)),e.$$set=l=>{t=_(_({},t),ie(l)),n(4,a=K(t,o)),"value"in l&&n(0,i=l.value)},e.$$.update=()=>{2&e.$$.dirty&&n(2,s=["c-dropdown-menu-augment__text-field-item",`c-dropdown-menu-augment__text-field-item--size-${r}`].join(" "))},[i,r,s,u,a,function(l){i=l,n(0,i)}]}function Pu(e){let t,n,s,o;const r=e[4].default,a=j(r,e,e[5],null);return{c(){t=H("div"),a&&a.c()},m(i,u){k(i,t,u),a&&a.m(t,null),n=!0,s||(o=js(e[1].registerTrigger(t)),s=!0)},p(i,u){a&&a.p&&(!n||32&u)&&q(a,r,i,i[5],n?W(r,i[5],u,null):X(i[5]),null)},i(i){n||(y(a,i),n=!0)},o(i){v(a,i),n=!1},d(i){i&&S(t),a&&a.d(i),s=!1,o()}}}function Mu(e){let t,n;return t=new zs({props:{referenceClientRect:e[0],$$slots:{default:[Pu]},$$scope:{ctx:e}}}),t.$on("keydown",e[3]),{c(){A(t.$$.fragment)},m(s,o){D(t,s,o),n=!0},p(s,[o]){const r={};1&o&&(r.referenceClientRect=s[0]),32&o&&(r.$$scope={dirty:o,ctx:s}),t.$set(r)},i(s){n||(y(t.$$.fragment,s),n=!0)},o(s){v(t.$$.fragment,s),n=!1},d(s){x(t,s)}}}function Yu(e,t,n){let s,{$$slots:o={},$$scope:r}=t,{referenceClientRect:a}=t;const i=B(ee.CONTEXT_KEY),u=B(Ve.CONTEXT_KEY).state;return oe(e,u,l=>n(6,s=l)),e.$$set=l=>{"referenceClientRect"in l&&n(0,a=l.referenceClientRect),"$$scope"in l&&n(5,r=l.$$scope)},[a,i,u,async l=>{switch(l.key){case"ArrowUp":l.preventDefault(),l.stopPropagation(),s.open||await i.clickFocusedItem(),i==null||i.focusIdx(-1);break;case"ArrowDown":l.preventDefault(),l.stopPropagation(),s.open||await i.clickFocusedItem(),i==null||i.focusIdx(0);break;case"Enter":l.preventDefault(),l.stopPropagation(),i==null||i.clickFocusedItem()}},o,r]}const pl={BreadcrumbBackItem:class extends Y{constructor(e){super(),G(this,e,eu,Qi,V,{})}},BreadcrumbItem:to,Content:eo,Item:bn,Label:class extends Y{constructor(e){super(),G(this,e,cu,lu,V,{})}},Root:no,Separator:class extends Y{constructor(e){super(),G(this,e,yu,bu,V,{})}},Sub:class extends Y{constructor(e){super(),G(this,e,Nu,vu,V,{})}},SubContent:class extends Y{constructor(e){super(),G(this,e,_u,Iu,V,{})}},SubTrigger:class extends Y{constructor(e){super(),G(this,e,Ou,Su,V,{})}},TextFieldItem:class extends Y{constructor(e){super(),G(this,e,Lu,Uu,V,{value:0})}},Trigger:class extends Y{constructor(e){super(),G(this,e,Yu,Mu,V,{referenceClientRect:0})}}};export{Ba as $,nl as A,Ju as B,_a as C,pl as D,Sa as E,Zu as F,Za as G,Ha as H,qa as I,Xa as J,bn as K,za as L,Oa as M,Qa as N,su as O,ka as P,Ya as Q,Ui as R,La as S,Cu as T,Pr as U,Da as V,Ia as W,Aa as X,Pa as Y,Ma as Z,hu as _,Es as a,ja as a0,Z as a1,Va as a2,Ka as a3,Ga as a4,Mi as a5,Mr as a6,Ca as a7,xi as a8,zr as a9,al as aa,ol as ab,hn as ac,Fi as ad,ul as ae,il as af,el as b,tl as c,xa as d,Ua as e,Wa as f,ue as g,wr as h,Ja as i,cl as j,dl as k,Fa as l,rl as m,sl as n,ll as o,Qu as p,Ci as q,wa as r,ml as s,$a as t,Wu as u,zu as v,Xu as w,Hu as x,Li as y,ys as z};

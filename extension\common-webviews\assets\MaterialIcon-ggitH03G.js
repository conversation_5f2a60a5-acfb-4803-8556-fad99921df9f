import{S as h,i as p,s as y,D as b,Y as v,d as o,a9 as $,P as g,f as w,h as L,Z as N,n as u,k}from"./SpinnerAugment-uKUHz-bK.js";function D(a){let t,e,s;return{c(){t=b("span"),e=v(a[1]),o(t,"class",s=$(`material-symbols-outlined ${a[0]}`)+" svelte-htlsjs"),g(t,"font-variation-settings","'FILL' "+a[3]+", 'wght' "+a[4]+", 'GRAD' "+a[5]),o(t,"title",a[2])},m(i,l){w(i,t,l),L(t,e)},p(i,[l]){2&l&&N(e,i[1]),1&l&&s!==(s=$(`material-symbols-outlined ${i[0]}`)+" svelte-htlsjs")&&o(t,"class",s),56&l&&g(t,"font-variation-settings","'FILL' "+i[3]+", 'wght' "+i[4]+", 'GRAD' "+i[5]),4&l&&o(t,"title",i[2])},i:u,o:u,d(i){i&&k(t)}}}function j(a,t,e){let s,i,l,{class:d=""}=t,{iconName:m=""}=t,{fill:r=!1}=t,{grade:c="normal"}=t,{title:f}=t;return a.$$set=n=>{"class"in n&&e(0,d=n.class),"iconName"in n&&e(1,m=n.iconName),"fill"in n&&e(6,r=n.fill),"grade"in n&&e(7,c=n.grade),"title"in n&&e(2,f=n.title)},a.$$.update=()=>{if(64&a.$$.dirty&&e(3,s=r?"1":"0"),64&a.$$.dirty&&e(4,i=r?"700":"400"),128&a.$$.dirty)switch(c){case"low":e(5,l="-25");break;case"normal":e(5,l="0");break;case"high":e(5,l="200")}},[d,m,f,s,i,l,r,c]}class A extends h{constructor(t){super(),p(this,t,j,D,y,{class:0,iconName:1,fill:6,grade:7,title:2})}}export{A as M};

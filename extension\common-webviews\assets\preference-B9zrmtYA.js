import{S as H,i as J,s as K,D as f,V as b,d as c,a0 as F,f as I,h as r,Q as O,n as Q,k as S,T as $e,Y as se,Z as ie,ad as ue,ab as ge,E as P,F as E,t as re,u as D,v as ce,w as B,G as N,O as ve,ac as be,a5 as ee,a6 as te,a7 as ne,N as ye}from"./SpinnerAugment-uKUHz-bK.js";import{h as z,W as G}from"./IconButtonAugment-CQzh_Hae.js";import{aJ as oe,aK as we,aL as ke}from"./AugmentMessage-DN__F6lq.js";import{M as xe}from"./message-broker-DdVtH9Vr.js";import{s as Ce}from"./go-to-website-mini-B1akcZ0N.js";import{M as qe}from"./index-D0JCd9Au.js";import"./CardAugment-BqjOeIg4.js";import"./chevron-down-BMBumfK8.js";import"./index-DP6mqmYw.js";import"./BaseTextInput-BTYl5feP.js";import"./async-messaging-D4p6YcQf.js";import"./types-CGlLNakm.js";import"./file-paths-CAgP5Fvb.js";import"./partner-mcp-utils-Di7HqDS-.js";import"./folder-opened-D8PSJjEt.js";import"./CalloutAugment-CznTrv4g.js";import"./index-Bcx5x-t6.js";import"./diff-utils-D5NvkEWZ.js";import"./LanguageIcon-DGbwX4zn.js";import"./preload-helper-Dv6uf1Os.js";import"./keypress-DD1aQVr0.js";import"./await_block-CiMtb4wh.js";import"./CollapseButtonAugment-ffrJmKr6.js";import"./ButtonAugment-D5QDitBR.js";import"./MaterialIcon-ggitH03G.js";import"./CopyButton-D9TioOfA.js";import"./copy-CfR4-ke6.js";import"./ellipsis-Btdwvghx.js";import"./IconFilePath-B4EaQlM7.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-DNGY17t7.js";import"./TextAreaAugment-CoQvc_01.js";import"./index-GYuo8qik.js";import"./pen-to-square-BWYRDHTI.js";import"./check-BgkDRaNt.js";import"./augment-logo-btpqr34Z.js";import"./utils-CThHGEuY.js";import"./types-DDm27S8B.js";import"./ra-diff-ops-model-DLmlSKam.js";function pe(s){let t,n;return{c(){t=f("div"),n=se(s[1]),c(t,"class","header svelte-1894wv4")},m(e,i){I(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&S(t)}}}function Ae(s){let t,n,e,i,l,o,p,h,d,u,A,y,$,C,k,w,m,M,q=s[1]&&pe(s);return{c(){t=f("div"),q&&q.c(),n=b(),e=f("div"),i=f("button"),i.textContent="A",l=b(),o=f("button"),o.textContent="A",p=b(),h=f("button"),h.textContent="A",d=b(),u=f("button"),u.textContent="=",A=b(),y=f("button"),y.textContent="B",$=b(),C=f("button"),C.textContent="B",k=b(),w=f("button"),w.textContent="B",c(i,"type","button"),c(i,"class","button large svelte-1894wv4"),F(i,"highlighted",s[0]==="A3"),c(o,"type","button"),c(o,"class","button medium svelte-1894wv4"),F(o,"highlighted",s[0]==="A2"),c(h,"type","button"),c(h,"class","button small svelte-1894wv4"),F(h,"highlighted",s[0]==="A1"),c(u,"type","button"),c(u,"class","button equal svelte-1894wv4"),F(u,"highlighted",s[0]==="="),c(y,"type","button"),c(y,"class","button small svelte-1894wv4"),F(y,"highlighted",s[0]==="B1"),c(C,"type","button"),c(C,"class","button medium svelte-1894wv4"),F(C,"highlighted",s[0]==="B2"),c(w,"type","button"),c(w,"class","button large svelte-1894wv4"),F(w,"highlighted",s[0]==="B3"),c(e,"class","buttons svelte-1894wv4"),c(t,"class","container svelte-1894wv4")},m(g,R){I(g,t,R),q&&q.m(t,null),r(t,n),r(t,e),r(e,i),r(e,l),r(e,o),r(e,p),r(e,h),r(e,d),r(e,u),r(e,A),r(e,y),r(e,$),r(e,C),r(e,k),r(e,w),m||(M=[O(i,"click",s[3]),O(o,"click",s[4]),O(h,"click",s[5]),O(u,"click",s[6]),O(y,"click",s[7]),O(C,"click",s[8]),O(w,"click",s[9])],m=!0)},p(g,[R]){g[1]?q?q.p(g,R):(q=pe(g),q.c(),q.m(t,n)):q&&(q.d(1),q=null),1&R&&F(i,"highlighted",g[0]==="A3"),1&R&&F(o,"highlighted",g[0]==="A2"),1&R&&F(h,"highlighted",g[0]==="A1"),1&R&&F(u,"highlighted",g[0]==="="),1&R&&F(y,"highlighted",g[0]==="B1"),1&R&&F(C,"highlighted",g[0]==="B2"),1&R&&F(w,"highlighted",g[0]==="B3")},i:Q,o:Q,d(g){g&&S(t),q&&q.d(),m=!1,$e(M)}}}function Be(s,t,n){let{selected:e=null}=t,{question:i=null}=t;function l(o){n(0,e=o)}return s.$$set=o=>{"selected"in o&&n(0,e=o.selected),"question"in o&&n(1,i=o.question)},[e,i,l,()=>l("A3"),()=>l("A2"),()=>l("A1"),()=>l("="),()=>l("B1"),()=>l("B2"),()=>l("B3")]}class ae extends H{constructor(t){super(),J(this,t,Be,Ae,K,{selected:0,question:1})}}function de(s){let t,n;return{c(){t=f("div"),n=se(s[1]),c(t,"class","question svelte-1i0f73l")},m(e,i){I(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&S(t)}}}function De(s){let t,n,e,i,l,o=s[1]&&de(s);return{c(){t=f("div"),o&&o.c(),n=b(),e=f("textarea"),c(e,"class","input svelte-1i0f73l"),c(e,"placeholder",s[2]),c(e,"rows","3"),c(t,"class","container svelte-1i0f73l")},m(p,h){I(p,t,h),o&&o.m(t,null),r(t,n),r(t,e),ue(e,s[0]),i||(l=O(e,"input",s[3]),i=!0)},p(p,[h]){p[1]?o?o.p(p,h):(o=de(p),o.c(),o.m(t,n)):o&&(o.d(1),o=null),4&h&&c(e,"placeholder",p[2]),1&h&&ue(e,p[0])},i:Q,o:Q,d(p){p&&S(t),o&&o.d(),i=!1,l()}}}function Me(s,t,n){let{value:e=""}=t,{question:i=null}=t,{placeholder:l=""}=t;return s.$$set=o=>{"value"in o&&n(0,e=o.value),"question"in o&&n(1,i=o.question),"placeholder"in o&&n(2,l=o.placeholder)},[e,i,l,function(){e=this.value,n(0,e)}]}class Re extends H{constructor(t){super(),J(this,t,Me,De,K,{value:0,question:1,placeholder:2})}}function Ie(s){let t,n,e,i;return{c(){t=f("button"),n=se(s[0]),c(t,"class","button svelte-2k5n")},m(l,o){I(l,t,o),r(t,n),e||(i=O(t,"click",function(){ge(s[1])&&s[1].apply(this,arguments)}),e=!0)},p(l,[o]){s=l,1&o&&ie(n,s[0])},i:Q,o:Q,d(l){l&&S(t),e=!1,i()}}}function Se(s,t,n){let{label:e="Submit"}=t,{onClick:i}=t;return s.$$set=l=>{"label"in l&&n(0,e=l.label),"onClick"in l&&n(1,i=l.onClick)},[e,i]}class We extends H{constructor(t){super(),J(this,t,Se,Ie,K,{label:0,onClick:1})}}function me(s){let t,n;return{c(){t=f("div"),n=se(s[1])},m(e,i){I(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&S(t)}}}function _e(s){let t,n,e,i,l,o,p,h,d=s[1]&&me(s);return{c(){t=f("div"),d&&d.c(),n=b(),e=f("label"),i=f("input"),l=b(),o=f("span"),c(i,"type","checkbox"),c(i,"class","svelte-n0uy88"),c(o,"class","svelte-n0uy88"),c(e,"class","custom-checkbox svelte-n0uy88"),c(t,"class","container svelte-n0uy88")},m(u,A){I(u,t,A),d&&d.m(t,null),r(t,n),r(t,e),r(e,i),i.checked=s[0],r(e,l),r(e,o),p||(h=O(i,"change",s[2]),p=!0)},p(u,[A]){u[1]?d?d.p(u,A):(d=me(u),d.c(),d.m(t,n)):d&&(d.d(1),d=null),1&A&&(i.checked=u[0])},i:Q,o:Q,d(u){u&&S(t),d&&d.d(),p=!1,h()}}}function Fe(s,t,n){let{isChecked:e=!1}=t,{question:i=null}=t;return s.$$set=l=>{"isChecked"in l&&n(0,e=l.isChecked),"question"in l&&n(1,i=l.question)},[e,i,function(){e=this.checked,n(0,e)}]}class Le extends H{constructor(t){super(),J(this,t,Fe,_e,K,{isChecked:0,question:1})}}function Oe(s){let t;return{c(){t=f("p"),t.textContent="Streaming in progress... Please wait for both responses to complete."},m(n,e){I(n,t,e)},p:Q,i:Q,o:Q,d(n){n&&S(t)}}}function Pe(s){let t,n,e,i,l,o,p,h,d,u,A,y,$,C,k,w,m;function M(a){s[12](a)}let q={question:"Which response is formatted better? (e.g. level of detail style, structure)?"};function g(a){s[13](a)}s[2]!==void 0&&(q.selected=s[2]),t=new ae({props:q}),ee.push(()=>te(t,"selected",M));let R={question:"Which response follows your instruction better?"};function V(a){s[14](a)}s[3]!==void 0&&(R.selected=s[3]),i=new ae({props:R}),ee.push(()=>te(i,"selected",g));let Y={question:"Which response is better overall?"};function W(a){s[15](a)}s[1]!==void 0&&(Y.selected=s[1]),p=new ae({props:Y}),ee.push(()=>te(p,"selected",V));let _={question:s[9]};function T(a){s[16](a)}s[5]!==void 0&&(_.isChecked=s[5]),u=new Le({props:_}),ee.push(()=>te(u,"isChecked",W));let Z={question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions."};return s[4]!==void 0&&(Z.value=s[4]),$=new Re({props:Z}),ee.push(()=>te($,"value",T)),w=new We({props:{label:"Submit",onClick:s[10]}}),{c(){P(t.$$.fragment),e=b(),P(i.$$.fragment),o=b(),P(p.$$.fragment),d=b(),P(u.$$.fragment),y=b(),P($.$$.fragment),k=b(),P(w.$$.fragment)},m(a,x){E(t,a,x),I(a,e,x),E(i,a,x),I(a,o,x),E(p,a,x),I(a,d,x),E(u,a,x),I(a,y,x),E($,a,x),I(a,k,x),E(w,a,x),m=!0},p(a,x){const v={};!n&&4&x&&(n=!0,v.selected=a[2],ne(()=>n=!1)),t.$set(v);const L={};!l&&8&x&&(l=!0,L.selected=a[3],ne(()=>l=!1)),i.$set(L);const U={};!h&&2&x&&(h=!0,U.selected=a[1],ne(()=>h=!1)),p.$set(U);const j={};512&x&&(j.question=a[9]),!A&&32&x&&(A=!0,j.isChecked=a[5],ne(()=>A=!1)),u.$set(j);const X={};!C&&16&x&&(C=!0,X.value=a[4],ne(()=>C=!1)),$.$set(X)},i(a){m||(B(t.$$.fragment,a),B(i.$$.fragment,a),B(p.$$.fragment,a),B(u.$$.fragment,a),B($.$$.fragment,a),B(w.$$.fragment,a),m=!0)},o(a){D(t.$$.fragment,a),D(i.$$.fragment,a),D(p.$$.fragment,a),D(u.$$.fragment,a),D($.$$.fragment,a),D(w.$$.fragment,a),m=!1},d(a){a&&(S(e),S(o),S(d),S(y),S(k)),N(t,a),N(i,a),N(p,a),N(u,a),N($,a),N(w,a)}}}function Ee(s){let t,n,e,i,l,o,p,h,d,u,A,y,$,C,k,w,m,M,q,g,R,V,Y,W,_,T;l=new oe({props:{markdown:s[0].data.a.message}}),$=new oe({props:{markdown:s[8]}}),g=new oe({props:{markdown:s[7]}});const Z=[Pe,Oe],a=[];function x(v,L){return v[6]?0:1}return W=x(s),_=a[W]=Z[W](s),{c(){t=f("main"),n=f("div"),e=f("h1"),e.textContent="Input message",i=b(),P(l.$$.fragment),o=b(),p=f("hr"),h=b(),d=f("div"),u=f("div"),A=f("h1"),A.textContent="Option A",y=b(),P($.$$.fragment),C=b(),k=f("div"),w=b(),m=f("div"),M=f("h1"),M.textContent="Option B",q=b(),P(g.$$.fragment),R=b(),V=f("hr"),Y=b(),_.c(),c(e,"class","svelte-751nif"),c(p,"class","l-side-by-side svelte-751nif"),c(A,"class","svelte-751nif"),c(u,"class","l-side-by-side__child svelte-751nif"),c(k,"class","divider svelte-751nif"),c(M,"class","svelte-751nif"),c(m,"class","l-side-by-side__child svelte-751nif"),c(d,"class","l-side-by-side svelte-751nif"),c(V,"class","svelte-751nif"),c(n,"class","l-pref svelte-751nif")},m(v,L){I(v,t,L),r(t,n),r(n,e),r(n,i),E(l,n,null),r(n,o),r(n,p),r(n,h),r(n,d),r(d,u),r(u,A),r(u,y),E($,u,null),r(d,C),r(d,k),r(d,w),r(d,m),r(m,M),r(m,q),E(g,m,null),r(n,R),r(n,V),r(n,Y),a[W].m(n,null),T=!0},p(v,[L]){const U={};1&L&&(U.markdown=v[0].data.a.message),l.$set(U);const j={};256&L&&(j.markdown=v[8]),$.$set(j);const X={};128&L&&(X.markdown=v[7]),g.$set(X);let le=W;W=x(v),W===le?a[W].p(v,L):(re(),D(a[le],1,1,()=>{a[le]=null}),ce(),_=a[W],_?_.p(v,L):(_=a[W]=Z[W](v),_.c()),B(_,1),_.m(n,null))},i(v){T||(B(l.$$.fragment,v),B($.$$.fragment,v),B(g.$$.fragment,v),B(_),T=!0)},o(v){D(l.$$.fragment,v),D($.$$.fragment,v),D(g.$$.fragment,v),D(_),T=!1},d(v){v&&S(t),N(l),N($),N(g),a[W].d()}}}function Ne(s,t,n){let e,i,l,{inputData:o}=t;const p=ve();let h=new we(new xe(z),z,new ke);Ce(h);let d=null,u=null,A=null,y=null,$="",C=!1,k={a:null,b:null},w=o.data.a.response.length>0&&o.data.b.response.length>0;return be(()=>{window.addEventListener("message",m=>{const M=m.data;M.type===G.chatModelReply?(M.stream==="A"?n(11,k.a=M.data.text,k):M.stream==="B"&&n(11,k.b=M.data.text,k),n(11,k)):M.type===G.chatStreamDone&&n(6,w=!0)})}),s.$$set=m=>{"inputData"in m&&n(0,o=m.inputData)},s.$$.update=()=>{var m;2&s.$$.dirty&&n(9,e=(m=y)==="="||m===null?"Is this a high quality comparison?":`Are you completely happy with response '${m.startsWith("A")?"A":"B"}'?`),2049&s.$$.dirty&&n(8,i=k.a!==null?k.a:o.data.a.response),2049&s.$$.dirty&&n(7,l=k.b!==null?k.b:o.data.b.response),1&s.$$.dirty&&n(6,w=o.data.a.response.length>0&&o.data.b.response.length>0)},[o,y,d,u,$,C,w,l,i,e,function(){if(A="=",y===null)return void p("notify","Overall rating is required");p("result",{overallRating:y,formattingRating:d||"=",hallucinationRating:A||"=",instructionFollowingRating:u||"=",isHighQuality:C,textFeedback:$})},k,function(m){d=m,n(2,d)},function(m){u=m,n(3,u)},function(m){y=m,n(1,y)},function(m){C=m,n(5,C)},function(m){$=m,n(4,$)}]}class Qe extends H{constructor(t){super(),J(this,t,Ne,Ee,K,{inputData:0})}}function fe(s){let t,n,e=s[0].type==="Chat"&&he(s);return{c(){e&&e.c(),t=ye()},m(i,l){e&&e.m(i,l),I(i,t,l),n=!0},p(i,l){i[0].type==="Chat"?e?(e.p(i,l),1&l&&B(e,1)):(e=he(i),e.c(),B(e,1),e.m(t.parentNode,t)):e&&(re(),D(e,1,1,()=>{e=null}),ce())},i(i){n||(B(e),n=!0)},o(i){D(e),n=!1},d(i){i&&S(t),e&&e.d(i)}}}function he(s){let t,n;return t=new Qe({props:{inputData:s[0]}}),t.$on("result",s[2]),t.$on("notify",s[3]),{c(){P(t.$$.fragment)},m(e,i){E(t,e,i),n=!0},p(e,i){const l={};1&i&&(l.inputData=e[0]),t.$set(l)},i(e){n||(B(t.$$.fragment,e),n=!0)},o(e){D(t.$$.fragment,e),n=!1},d(e){N(t,e)}}}function ze(s){let t,n,e=s[0]&&fe(s);return{c(){t=f("main"),e&&e.c()},m(i,l){I(i,t,l),e&&e.m(t,null),n=!0},p(i,l){i[0]?e?(e.p(i,l),1&l&&B(e,1)):(e=fe(i),e.c(),B(e,1),e.m(t,null)):e&&(re(),D(e,1,1,()=>{e=null}),ce())},i(i){n||(B(e),n=!0)},o(i){D(e),n=!1},d(i){i&&S(t),e&&e.d()}}}function Te(s){let t,n,e,i;return t=new qe.Root({props:{$$slots:{default:[ze]},$$scope:{ctx:s}}}),{c(){P(t.$$.fragment)},m(l,o){E(t,l,o),n=!0,e||(i=O(window,"message",s[1]),e=!0)},p(l,[o]){const p={};17&o&&(p.$$scope={dirty:o,ctx:l}),t.$set(p)},i(l){n||(B(t.$$.fragment,l),n=!0)},o(l){D(t.$$.fragment,l),n=!1},d(l){N(t,l),e=!1,i()}}}function je(s,t,n){let e;return z.postMessage({type:G.preferencePanelLoaded}),[e,function(i){const l=i.data;l.type===G.preferenceInit&&n(0,e=l.data)},function(i){const l=i.detail;z.postMessage({type:G.preferenceResultMessage,data:l})},function(i){z.postMessage({type:G.preferenceNotify,data:i.detail})}]}class Ge extends H{constructor(t){super(),J(this,t,je,Te,K,{})}}(async function(){z&&z.initialize&&await z.initialize(),new Ge({target:document.getElementById("app")})})();

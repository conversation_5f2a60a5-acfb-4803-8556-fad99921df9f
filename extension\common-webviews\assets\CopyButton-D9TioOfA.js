import{S,i as T,s as q,D,E as k,d as j,f as v,F as N,w as u,u as $,k as w,G as z,I as A,J as b,K as h,L,M as P,N as E,t as F,v as G,n as I}from"./SpinnerAugment-uKUHz-bK.js";import{S as J,C as K}from"./copy-CfR4-ke6.js";const M=n=>({}),g=n=>({}),V=n=>({}),x=n=>({});function B(n){let e;const t=n[10].text,i=b(t,n,n[11],g);return{c(){i&&i.c()},m(o,s){i&&i.m(o,s),e=!0},p(o,s){i&&i.p&&(!e||2048&s)&&h(i,t,o,o[11],e?P(t,o[11],s,M):L(o[11]),g)},i(o){e||(u(i,o),e=!0)},o(o){$(i,o),e=!1},d(o){i&&i.d(o)}}}function H(n){let e,t;return e=new K({}),{c(){k(e.$$.fragment)},m(i,o){N(e,i,o),t=!0},p:I,i(i){t||(u(e.$$.fragment,i),t=!0)},o(i){$(e.$$.fragment,i),t=!1},d(i){z(e,i)}}}function O(n){let e;const t=n[10].icon,i=b(t,n,n[11],x);return{c(){i&&i.c()},m(o,s){i&&i.m(o,s),e=!0},p(o,s){i&&i.p&&(!e||2048&s)&&h(i,t,o,o[11],e?P(t,o[11],s,V):L(o[11]),x)},i(o){e||(u(i,o),e=!0)},o(o){$(i,o),e=!1},d(o){i&&i.d(o)}}}function Q(n){let e,t,i,o;const s=[O,H],a=[];function d(r,l){return r[8].icon?0:1}return e=d(n),t=a[e]=s[e](n),{c(){t.c(),i=E()},m(r,l){a[e].m(r,l),v(r,i,l),o=!0},p(r,l){let p=e;e=d(r),e===p?a[e].p(r,l):(F(),$(a[p],1,1,()=>{a[p]=null}),G(),t=a[e],t?t.p(r,l):(t=a[e]=s[e](r),t.c()),u(t,1),t.m(i.parentNode,i))},i(r){o||(u(t),o=!0)},o(r){$(t),o=!1},d(r){r&&w(i),a[e].d(r)}}}function R(n){let e,t,i;return t=new J({props:{defaultColor:n[2],size:n[0],variant:n[1],loading:n[7],stickyColor:n[4],tooltip:{neutral:n[3],success:"Copied!"},stateVariant:{success:"soft"},onClick:n[5],icon:!n[8].text,tooltipNested:n[6],$$slots:{iconLeft:[Q],default:[B]},$$scope:{ctx:n}}}),{c(){e=D("span"),k(t.$$.fragment),j(e,"class","c-copy-button svelte-tq93gm")},m(o,s){v(o,e,s),N(t,e,null),i=!0},p(o,[s]){const a={};4&s&&(a.defaultColor=o[2]),1&s&&(a.size=o[0]),2&s&&(a.variant=o[1]),128&s&&(a.loading=o[7]),16&s&&(a.stickyColor=o[4]),8&s&&(a.tooltip={neutral:o[3],success:"Copied!"}),32&s&&(a.onClick=o[5]),256&s&&(a.icon=!o[8].text),64&s&&(a.tooltipNested=o[6]),2304&s&&(a.$$scope={dirty:s,ctx:o}),t.$set(a)},i(o){i||(u(t.$$.fragment,o),i=!0)},o(o){$(t.$$.fragment,o),i=!1},d(o){o&&w(e),z(t)}}}function U(n,e){return new Promise(t=>setTimeout(t,n,e))}function W(n,e,t){let{$$slots:i={},$$scope:o}=e;const s=A(i);let{size:a=1}=e,{variant:d="ghost-block"}=e,{color:r="neutral"}=e,{text:l}=e,{tooltip:p="Copy"}=e,{stickyColor:m=!1}=e,{onCopy:C=async()=>{if(l!==void 0){t(7,f=!0);try{await Promise.all([navigator.clipboard.writeText(typeof l=="string"?l:await l()),U(250)])}finally{t(7,f=!1)}return"success"}}}=e,{tooltipNested:y}=e,f=!1;return n.$$set=c=>{"size"in c&&t(0,a=c.size),"variant"in c&&t(1,d=c.variant),"color"in c&&t(2,r=c.color),"text"in c&&t(9,l=c.text),"tooltip"in c&&t(3,p=c.tooltip),"stickyColor"in c&&t(4,m=c.stickyColor),"onCopy"in c&&t(5,C=c.onCopy),"tooltipNested"in c&&t(6,y=c.tooltipNested),"$$scope"in c&&t(11,o=c.$$scope)},[a,d,r,p,m,C,y,f,s,l,i,o]}class Z extends S{constructor(e){super(),T(this,e,W,R,q,{size:0,variant:1,color:2,text:9,tooltip:3,stickyColor:4,onCopy:5,tooltipNested:6})}}export{Z as C};

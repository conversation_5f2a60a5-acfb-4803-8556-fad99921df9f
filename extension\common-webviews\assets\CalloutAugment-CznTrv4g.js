import{S,i as A,s as J,X as K,_ as x,a as g,D as m,E as L,$ as w,a0 as p,f as d,F as M,j as N,w as u,u as $,k as f,G as V,a1 as y,I as X,l as _,J as k,V as B,d as D,t as H,v as O,K as E,L as F,M as G}from"./SpinnerAugment-uKUHz-bK.js";const P=o=>({}),b=o=>({});function j(o){let a,l;const i=o[8].icon,c=k(i,o,o[9],b);return{c(){a=m("div"),c&&c.c(),D(a,"class","c-callout-icon svelte-1u5qnh6")},m(e,n){d(e,a,n),c&&c.m(a,null),l=!0},p(e,n){c&&c.p&&(!l||512&n)&&E(c,i,e,e[9],l?G(i,e[9],n,P):F(e[9]),b)},i(e){l||(u(c,e),l=!0)},o(e){$(c,e),l=!1},d(e){e&&f(a),c&&c.d(e)}}}function Q(o){let a,l,i,c=o[7].icon&&j(o);const e=o[8].default,n=k(e,o,o[9],null);return{c(){c&&c.c(),a=B(),l=m("div"),n&&n.c(),D(l,"class","c-callout-body svelte-1u5qnh6")},m(t,s){c&&c.m(t,s),d(t,a,s),d(t,l,s),n&&n.m(l,null),i=!0},p(t,s){t[7].icon?c?(c.p(t,s),128&s&&u(c,1)):(c=j(t),c.c(),u(c,1),c.m(a.parentNode,a)):c&&(H(),$(c,1,1,()=>{c=null}),O()),n&&n.p&&(!i||512&s)&&E(n,e,t,t[9],i?G(e,t[9],s,null):F(t[9]),null)},i(t){i||(u(c),u(n,t),i=!0)},o(t){$(c),$(n,t),i=!1},d(t){t&&(f(a),f(l)),c&&c.d(t),n&&n.d(t)}}}function R(o){let a,l,i,c;l=new K({props:{size:o[6],$$slots:{default:[Q]},$$scope:{ctx:o}}});let e=[x(o[0]),{class:i=`c-callout c-callout--${o[0]} c-callout--${o[1]} c-callout--size-${o[2]} ${o[5]}`},o[4]],n={};for(let t=0;t<e.length;t+=1)n=g(n,e[t]);return{c(){a=m("div"),L(l.$$.fragment),w(a,n),p(a,"c-callout--highContrast",o[3]),p(a,"svelte-1u5qnh6",!0)},m(t,s){d(t,a,s),M(l,a,null),c=!0},p(t,[s]){const h={};640&s&&(h.$$scope={dirty:s,ctx:t}),l.$set(h),w(a,n=N(e,[1&s&&x(t[0]),(!c||39&s&&i!==(i=`c-callout c-callout--${t[0]} c-callout--${t[1]} c-callout--size-${t[2]} ${t[5]}`))&&{class:i},16&s&&t[4]])),p(a,"c-callout--highContrast",t[3]),p(a,"svelte-1u5qnh6",!0)},i(t){c||(u(l.$$.fragment,t),c=!0)},o(t){$(l.$$.fragment,t),c=!1},d(t){t&&f(a),V(l)}}}function T(o,a,l){let i,c;const e=["color","variant","size","highContrast"];let n=y(a,e),{$$slots:t={},$$scope:s}=a;const h=X(t);let{color:z="info"}=a,{variant:C="soft"}=a,{size:v=2}=a,{highContrast:q=!1}=a;const I=v;return o.$$set=r=>{a=g(g({},a),_(r)),l(10,n=y(a,e)),"color"in r&&l(0,z=r.color),"variant"in r&&l(1,C=r.variant),"size"in r&&l(2,v=r.size),"highContrast"in r&&l(3,q=r.highContrast),"$$scope"in r&&l(9,s=r.$$scope)},o.$$.update=()=>{l(5,{class:i,...c}=n,i,(l(4,c),l(10,n)))},[z,C,v,q,c,i,I,h,t,s]}class W extends S{constructor(a){super(),A(this,a,T,R,J,{color:0,variant:1,size:2,highContrast:3})}}export{W as C};

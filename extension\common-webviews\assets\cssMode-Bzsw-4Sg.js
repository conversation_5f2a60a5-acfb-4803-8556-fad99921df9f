import{m as Jt}from"./LanguageIcon-DGbwX4zn.js";import"./preload-helper-Dv6uf1Os.js";import"./SpinnerAugment-uKUHz-bK.js";var Yt=Object.defineProperty,Zt=Object.getOwnPropertyDescriptor,en=Object.getOwnPropertyNames,tn=Object.prototype.hasOwnProperty,u={};((t,e,n,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let o of en(e))tn.call(t,o)||o===n||Yt(t,o,{get:()=>e[o],enumerable:!(i=Zt(e,o))||i.enumerable})})(u,Jt,"default");var ce,X,de,R,j,L,k,le,h,ge,N,fe,he,me,z,pe,ve,be,_e,ke,D,$,we,ye,B,xe,S,U,Ie,Ee,Ae,V,Ce,E,Se,A,O,q,Re,C,Le,K,Q,Te,G,Me,J,De,Y,Pe,Z,Fe,je,Ne,Ue,ee,Ve,Oe,Ke,te,T,M,m,g,se,We,He,Xe,ze,$e,Be,qe,Qe,Ge,W,Je,Ye,Ze,et,P,ne,tt,p,l,nt,rt,it,ot,at,st,y,H,ut,ct,dt,lt,gt,ft,ht,mt,pt,vt,bt,_t,re,kt,f,wt,w,yt,xt,It,Et,At,Ct,St,Rt,Lt,ie,oe,ae,Tt,Mt,Dt,Pt,Ft,jt,Nt,Ut,Vt,Ot,Kt,Wt,nn=class{constructor(t){this._defaults=t,this._worker=null,this._client=null,this._idleCheckInterval=window.setInterval(()=>this._checkIfIdle(),3e4),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange(()=>this._stopWorker())}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}dispose(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()}_checkIfIdle(){this._worker&&Date.now()-this._lastUsedTime>12e4&&this._stopWorker()}_getClient(){return this._lastUsedTime=Date.now(),this._client||(this._worker=u.editor.createWebWorker({moduleId:"vs/language/css/cssWorker",label:this._defaults.languageId,createData:{options:this._defaults.options,languageId:this._defaults.languageId}}),this._client=this._worker.getProxy()),this._client}getLanguageServiceWorker(...t){let e;return this._getClient().then(n=>{e=n}).then(n=>{if(this._worker)return this._worker.withSyncedResources(t)}).then(n=>e)}};(ce||(ce={})).is=function(t){return typeof t=="string"},(X||(X={})).is=function(t){return typeof t=="string"},(R=de||(de={})).MIN_VALUE=-2147483648,R.MAX_VALUE=2147483647,R.is=function(t){return typeof t=="number"&&R.MIN_VALUE<=t&&t<=R.MAX_VALUE},(L=j||(j={})).MIN_VALUE=0,L.MAX_VALUE=2147483647,L.is=function(t){return typeof t=="number"&&L.MIN_VALUE<=t&&t<=L.MAX_VALUE},(le=k||(k={})).create=function(t,e){return t===Number.MAX_VALUE&&(t=j.MAX_VALUE),e===Number.MAX_VALUE&&(e=j.MAX_VALUE),{line:t,character:e}},le.is=function(t){let e=t;return r.objectLiteral(e)&&r.uinteger(e.line)&&r.uinteger(e.character)},(ge=h||(h={})).create=function(t,e,n,i){if(r.uinteger(t)&&r.uinteger(e)&&r.uinteger(n)&&r.uinteger(i))return{start:k.create(t,e),end:k.create(n,i)};if(k.is(t)&&k.is(e))return{start:t,end:e};throw new Error(`Range#create called with invalid arguments[${t}, ${e}, ${n}, ${i}]`)},ge.is=function(t){let e=t;return r.objectLiteral(e)&&k.is(e.start)&&k.is(e.end)},(fe=N||(N={})).create=function(t,e){return{uri:t,range:e}},fe.is=function(t){let e=t;return r.objectLiteral(e)&&h.is(e.range)&&(r.string(e.uri)||r.undefined(e.uri))},(me=he||(he={})).create=function(t,e,n,i){return{targetUri:t,targetRange:e,targetSelectionRange:n,originSelectionRange:i}},me.is=function(t){let e=t;return r.objectLiteral(e)&&h.is(e.targetRange)&&r.string(e.targetUri)&&h.is(e.targetSelectionRange)&&(h.is(e.originSelectionRange)||r.undefined(e.originSelectionRange))},(pe=z||(z={})).create=function(t,e,n,i){return{red:t,green:e,blue:n,alpha:i}},pe.is=function(t){const e=t;return r.objectLiteral(e)&&r.numberRange(e.red,0,1)&&r.numberRange(e.green,0,1)&&r.numberRange(e.blue,0,1)&&r.numberRange(e.alpha,0,1)},(be=ve||(ve={})).create=function(t,e){return{range:t,color:e}},be.is=function(t){const e=t;return r.objectLiteral(e)&&h.is(e.range)&&z.is(e.color)},(ke=_e||(_e={})).create=function(t,e,n){return{label:t,textEdit:e,additionalTextEdits:n}},ke.is=function(t){const e=t;return r.objectLiteral(e)&&r.string(e.label)&&(r.undefined(e.textEdit)||A.is(e))&&(r.undefined(e.additionalTextEdits)||r.typedArray(e.additionalTextEdits,A.is))},($=D||(D={})).Comment="comment",$.Imports="imports",$.Region="region",(ye=we||(we={})).create=function(t,e,n,i,o,a){const s={startLine:t,endLine:e};return r.defined(n)&&(s.startCharacter=n),r.defined(i)&&(s.endCharacter=i),r.defined(o)&&(s.kind=o),r.defined(a)&&(s.collapsedText=a),s},ye.is=function(t){const e=t;return r.objectLiteral(e)&&r.uinteger(e.startLine)&&r.uinteger(e.startLine)&&(r.undefined(e.startCharacter)||r.uinteger(e.startCharacter))&&(r.undefined(e.endCharacter)||r.uinteger(e.endCharacter))&&(r.undefined(e.kind)||r.string(e.kind))},(xe=B||(B={})).create=function(t,e){return{location:t,message:e}},xe.is=function(t){let e=t;return r.defined(e)&&N.is(e.location)&&r.string(e.message)},(U=S||(S={})).Error=1,U.Warning=2,U.Information=3,U.Hint=4,(Ee=Ie||(Ie={})).Unnecessary=1,Ee.Deprecated=2,(Ae||(Ae={})).is=function(t){const e=t;return r.objectLiteral(e)&&r.string(e.href)},(Ce=V||(V={})).create=function(t,e,n,i,o,a){let s={range:t,message:e};return r.defined(n)&&(s.severity=n),r.defined(i)&&(s.code=i),r.defined(o)&&(s.source=o),r.defined(a)&&(s.relatedInformation=a),s},Ce.is=function(t){var e;let n=t;return r.defined(n)&&h.is(n.range)&&r.string(n.message)&&(r.number(n.severity)||r.undefined(n.severity))&&(r.integer(n.code)||r.string(n.code)||r.undefined(n.code))&&(r.undefined(n.codeDescription)||r.string((e=n.codeDescription)===null||e===void 0?void 0:e.href))&&(r.string(n.source)||r.undefined(n.source))&&(r.undefined(n.relatedInformation)||r.typedArray(n.relatedInformation,B.is))},(Se=E||(E={})).create=function(t,e,...n){let i={title:t,command:e};return r.defined(n)&&n.length>0&&(i.arguments=n),i},Se.is=function(t){let e=t;return r.defined(e)&&r.string(e.title)&&r.string(e.command)},(O=A||(A={})).replace=function(t,e){return{range:t,newText:e}},O.insert=function(t,e){return{range:{start:t,end:t},newText:e}},O.del=function(t){return{range:t,newText:""}},O.is=function(t){const e=t;return r.objectLiteral(e)&&r.string(e.newText)&&h.is(e.range)},(Re=q||(q={})).create=function(t,e,n){const i={label:t};return e!==void 0&&(i.needsConfirmation=e),n!==void 0&&(i.description=n),i},Re.is=function(t){const e=t;return r.objectLiteral(e)&&r.string(e.label)&&(r.boolean(e.needsConfirmation)||e.needsConfirmation===void 0)&&(r.string(e.description)||e.description===void 0)},(C||(C={})).is=function(t){const e=t;return r.string(e)},(K=Le||(Le={})).replace=function(t,e,n){return{range:t,newText:e,annotationId:n}},K.insert=function(t,e,n){return{range:{start:t,end:t},newText:e,annotationId:n}},K.del=function(t,e){return{range:t,newText:"",annotationId:e}},K.is=function(t){const e=t;return A.is(e)&&(q.is(e.annotationId)||C.is(e.annotationId))},(Te=Q||(Q={})).create=function(t,e){return{textDocument:t,edits:e}},Te.is=function(t){let e=t;return r.defined(e)&&ee.is(e.textDocument)&&Array.isArray(e.edits)},(Me=G||(G={})).create=function(t,e,n){let i={kind:"create",uri:t};return e===void 0||e.overwrite===void 0&&e.ignoreIfExists===void 0||(i.options=e),n!==void 0&&(i.annotationId=n),i},Me.is=function(t){let e=t;return e&&e.kind==="create"&&r.string(e.uri)&&(e.options===void 0||(e.options.overwrite===void 0||r.boolean(e.options.overwrite))&&(e.options.ignoreIfExists===void 0||r.boolean(e.options.ignoreIfExists)))&&(e.annotationId===void 0||C.is(e.annotationId))},(De=J||(J={})).create=function(t,e,n,i){let o={kind:"rename",oldUri:t,newUri:e};return n===void 0||n.overwrite===void 0&&n.ignoreIfExists===void 0||(o.options=n),i!==void 0&&(o.annotationId=i),o},De.is=function(t){let e=t;return e&&e.kind==="rename"&&r.string(e.oldUri)&&r.string(e.newUri)&&(e.options===void 0||(e.options.overwrite===void 0||r.boolean(e.options.overwrite))&&(e.options.ignoreIfExists===void 0||r.boolean(e.options.ignoreIfExists)))&&(e.annotationId===void 0||C.is(e.annotationId))},(Pe=Y||(Y={})).create=function(t,e,n){let i={kind:"delete",uri:t};return e===void 0||e.recursive===void 0&&e.ignoreIfNotExists===void 0||(i.options=e),n!==void 0&&(i.annotationId=n),i},Pe.is=function(t){let e=t;return e&&e.kind==="delete"&&r.string(e.uri)&&(e.options===void 0||(e.options.recursive===void 0||r.boolean(e.options.recursive))&&(e.options.ignoreIfNotExists===void 0||r.boolean(e.options.ignoreIfNotExists)))&&(e.annotationId===void 0||C.is(e.annotationId))},(Z||(Z={})).is=function(t){let e=t;return e&&(e.changes!==void 0||e.documentChanges!==void 0)&&(e.documentChanges===void 0||e.documentChanges.every(n=>r.string(n.kind)?G.is(n)||J.is(n)||Y.is(n):Q.is(n)))},(je=Fe||(Fe={})).create=function(t){return{uri:t}},je.is=function(t){let e=t;return r.defined(e)&&r.string(e.uri)},(Ue=Ne||(Ne={})).create=function(t,e){return{uri:t,version:e}},Ue.is=function(t){let e=t;return r.defined(e)&&r.string(e.uri)&&r.integer(e.version)},(Ve=ee||(ee={})).create=function(t,e){return{uri:t,version:e}},Ve.is=function(t){let e=t;return r.defined(e)&&r.string(e.uri)&&(e.version===null||r.integer(e.version))},(Ke=Oe||(Oe={})).create=function(t,e,n,i){return{uri:t,languageId:e,version:n,text:i}},Ke.is=function(t){let e=t;return r.defined(e)&&r.string(e.uri)&&r.string(e.languageId)&&r.integer(e.version)&&r.string(e.text)},(T=te||(te={})).PlainText="plaintext",T.Markdown="markdown",T.is=function(t){const e=t;return e===T.PlainText||e===T.Markdown},(M||(M={})).is=function(t){const e=t;return r.objectLiteral(t)&&te.is(e.kind)&&r.string(e.value)},(g=m||(m={})).Text=1,g.Method=2,g.Function=3,g.Constructor=4,g.Field=5,g.Variable=6,g.Class=7,g.Interface=8,g.Module=9,g.Property=10,g.Unit=11,g.Value=12,g.Enum=13,g.Keyword=14,g.Snippet=15,g.Color=16,g.File=17,g.Reference=18,g.Folder=19,g.EnumMember=20,g.Constant=21,g.Struct=22,g.Event=23,g.Operator=24,g.TypeParameter=25,(We=se||(se={})).PlainText=1,We.Snippet=2,(He||(He={})).Deprecated=1,(ze=Xe||(Xe={})).create=function(t,e,n){return{newText:t,insert:e,replace:n}},ze.is=function(t){const e=t;return e&&r.string(e.newText)&&h.is(e.insert)&&h.is(e.replace)},(Be=$e||($e={})).asIs=1,Be.adjustIndentation=2,(qe||(qe={})).is=function(t){const e=t;return e&&(r.string(e.detail)||e.detail===void 0)&&(r.string(e.description)||e.description===void 0)},(Qe||(Qe={})).create=function(t){return{label:t}},(Ge||(Ge={})).create=function(t,e){return{items:t||[],isIncomplete:!!e}},(Je=W||(W={})).fromPlainText=function(t){return t.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},Je.is=function(t){const e=t;return r.string(e)||r.objectLiteral(e)&&r.string(e.language)&&r.string(e.value)},(Ye||(Ye={})).is=function(t){let e=t;return!!e&&r.objectLiteral(e)&&(M.is(e.contents)||W.is(e.contents)||r.typedArray(e.contents,W.is))&&(t.range===void 0||h.is(t.range))},(Ze||(Ze={})).create=function(t,e){return e?{label:t,documentation:e}:{label:t}},(et||(et={})).create=function(t,e,...n){let i={label:t};return r.defined(e)&&(i.documentation=e),r.defined(n)?i.parameters=n:i.parameters=[],i},(ne=P||(P={})).Text=1,ne.Read=2,ne.Write=3,(tt||(tt={})).create=function(t,e){let n={range:t};return r.number(e)&&(n.kind=e),n},(l=p||(p={})).File=1,l.Module=2,l.Namespace=3,l.Package=4,l.Class=5,l.Method=6,l.Property=7,l.Field=8,l.Constructor=9,l.Enum=10,l.Interface=11,l.Function=12,l.Variable=13,l.Constant=14,l.String=15,l.Number=16,l.Boolean=17,l.Array=18,l.Object=19,l.Key=20,l.Null=21,l.EnumMember=22,l.Struct=23,l.Event=24,l.Operator=25,l.TypeParameter=26,(nt||(nt={})).Deprecated=1,(rt||(rt={})).create=function(t,e,n,i,o){let a={name:t,kind:e,location:{uri:i,range:n}};return o&&(a.containerName=o),a},(it||(it={})).create=function(t,e,n,i){return i!==void 0?{name:t,kind:e,location:{uri:n,range:i}}:{name:t,kind:e,location:{uri:n}}},(at=ot||(ot={})).create=function(t,e,n,i,o,a){let s={name:t,detail:e,kind:n,range:i,selectionRange:o};return a!==void 0&&(s.children=a),s},at.is=function(t){let e=t;return e&&r.string(e.name)&&r.number(e.kind)&&h.is(e.range)&&h.is(e.selectionRange)&&(e.detail===void 0||r.string(e.detail))&&(e.deprecated===void 0||r.boolean(e.deprecated))&&(e.children===void 0||Array.isArray(e.children))&&(e.tags===void 0||Array.isArray(e.tags))},(y=st||(st={})).Empty="",y.QuickFix="quickfix",y.Refactor="refactor",y.RefactorExtract="refactor.extract",y.RefactorInline="refactor.inline",y.RefactorRewrite="refactor.rewrite",y.Source="source",y.SourceOrganizeImports="source.organizeImports",y.SourceFixAll="source.fixAll",(ut=H||(H={})).Invoked=1,ut.Automatic=2,(dt=ct||(ct={})).create=function(t,e,n){let i={diagnostics:t};return e!=null&&(i.only=e),n!=null&&(i.triggerKind=n),i},dt.is=function(t){let e=t;return r.defined(e)&&r.typedArray(e.diagnostics,V.is)&&(e.only===void 0||r.typedArray(e.only,r.string))&&(e.triggerKind===void 0||e.triggerKind===H.Invoked||e.triggerKind===H.Automatic)},(gt=lt||(lt={})).create=function(t,e,n){let i={title:t},o=!0;return typeof e=="string"?(o=!1,i.kind=e):E.is(e)?i.command=e:i.edit=e,o&&n!==void 0&&(i.kind=n),i},gt.is=function(t){let e=t;return e&&r.string(e.title)&&(e.diagnostics===void 0||r.typedArray(e.diagnostics,V.is))&&(e.kind===void 0||r.string(e.kind))&&(e.edit!==void 0||e.command!==void 0)&&(e.command===void 0||E.is(e.command))&&(e.isPreferred===void 0||r.boolean(e.isPreferred))&&(e.edit===void 0||Z.is(e.edit))},(ht=ft||(ft={})).create=function(t,e){let n={range:t};return r.defined(e)&&(n.data=e),n},ht.is=function(t){let e=t;return r.defined(e)&&h.is(e.range)&&(r.undefined(e.command)||E.is(e.command))},(pt=mt||(mt={})).create=function(t,e){return{tabSize:t,insertSpaces:e}},pt.is=function(t){let e=t;return r.defined(e)&&r.uinteger(e.tabSize)&&r.boolean(e.insertSpaces)},(bt=vt||(vt={})).create=function(t,e,n){return{range:t,target:e,data:n}},bt.is=function(t){let e=t;return r.defined(e)&&h.is(e.range)&&(r.undefined(e.target)||r.string(e.target))},(re=_t||(_t={})).create=function(t,e){return{range:t,parent:e}},re.is=function(t){let e=t;return r.objectLiteral(e)&&h.is(e.range)&&(e.parent===void 0||re.is(e.parent))},(f=kt||(kt={})).namespace="namespace",f.type="type",f.class="class",f.enum="enum",f.interface="interface",f.struct="struct",f.typeParameter="typeParameter",f.parameter="parameter",f.variable="variable",f.property="property",f.enumMember="enumMember",f.event="event",f.function="function",f.method="method",f.macro="macro",f.keyword="keyword",f.modifier="modifier",f.comment="comment",f.string="string",f.number="number",f.regexp="regexp",f.operator="operator",f.decorator="decorator",(w=wt||(wt={})).declaration="declaration",w.definition="definition",w.readonly="readonly",w.static="static",w.deprecated="deprecated",w.abstract="abstract",w.async="async",w.modification="modification",w.documentation="documentation",w.defaultLibrary="defaultLibrary",(yt||(yt={})).is=function(t){const e=t;return r.objectLiteral(e)&&(e.resultId===void 0||typeof e.resultId=="string")&&Array.isArray(e.data)&&(e.data.length===0||typeof e.data[0]=="number")},(It=xt||(xt={})).create=function(t,e){return{range:t,text:e}},It.is=function(t){const e=t;return e!=null&&h.is(e.range)&&r.string(e.text)},(At=Et||(Et={})).create=function(t,e,n){return{range:t,variableName:e,caseSensitiveLookup:n}},At.is=function(t){const e=t;return e!=null&&h.is(e.range)&&r.boolean(e.caseSensitiveLookup)&&(r.string(e.variableName)||e.variableName===void 0)},(St=Ct||(Ct={})).create=function(t,e){return{range:t,expression:e}},St.is=function(t){const e=t;return e!=null&&h.is(e.range)&&(r.string(e.expression)||e.expression===void 0)},(Lt=Rt||(Rt={})).create=function(t,e){return{frameId:t,stoppedLocation:e}},Lt.is=function(t){const e=t;return r.defined(e)&&h.is(t.stoppedLocation)},(oe=ie||(ie={})).Type=1,oe.Parameter=2,oe.is=function(t){return t===1||t===2},(Tt=ae||(ae={})).create=function(t){return{value:t}},Tt.is=function(t){const e=t;return r.objectLiteral(e)&&(e.tooltip===void 0||r.string(e.tooltip)||M.is(e.tooltip))&&(e.location===void 0||N.is(e.location))&&(e.command===void 0||E.is(e.command))},(Dt=Mt||(Mt={})).create=function(t,e,n){const i={position:t,label:e};return n!==void 0&&(i.kind=n),i},Dt.is=function(t){const e=t;return r.objectLiteral(e)&&k.is(e.position)&&(r.string(e.label)||r.typedArray(e.label,ae.is))&&(e.kind===void 0||ie.is(e.kind))&&e.textEdits===void 0||r.typedArray(e.textEdits,A.is)&&(e.tooltip===void 0||r.string(e.tooltip)||M.is(e.tooltip))&&(e.paddingLeft===void 0||r.boolean(e.paddingLeft))&&(e.paddingRight===void 0||r.boolean(e.paddingRight))},(Pt||(Pt={})).createSnippet=function(t){return{kind:"snippet",value:t}},(Ft||(Ft={})).create=function(t,e,n,i){return{insertText:t,filterText:e,range:n,command:i}},(jt||(jt={})).create=function(t){return{items:t}},(Ut=Nt||(Nt={})).Invoked=0,Ut.Automatic=1,(Vt||(Vt={})).create=function(t,e){return{range:t,text:e}},(Ot||(Ot={})).create=function(t,e){return{triggerKind:t,selectedCompletionInfo:e}},(Kt||(Kt={})).is=function(t){const e=t;return r.objectLiteral(e)&&X.is(e.uri)&&r.string(e.name)},function(t){function e(n,i){if(n.length<=1)return n;const o=n.length/2|0,a=n.slice(0,o),s=n.slice(o);e(a,i),e(s,i);let c=0,v=0,d=0;for(;c<a.length&&v<s.length;){let _=i(a[c],s[v]);n[d++]=_<=0?a[c++]:s[v++]}for(;c<a.length;)n[d++]=a[c++];for(;v<s.length;)n[d++]=s[v++];return n}t.create=function(n,i,o,a){return new rn(n,i,o,a)},t.is=function(n){let i=n;return!!(r.defined(i)&&r.string(i.uri)&&(r.undefined(i.languageId)||r.string(i.languageId))&&r.uinteger(i.lineCount)&&r.func(i.getText)&&r.func(i.positionAt)&&r.func(i.offsetAt))},t.applyEdits=function(n,i){let o=n.getText(),a=e(i,(c,v)=>{let d=c.range.start.line-v.range.start.line;return d===0?c.range.start.character-v.range.start.character:d}),s=o.length;for(let c=a.length-1;c>=0;c--){let v=a[c],d=n.offsetAt(v.range.start),_=n.offsetAt(v.range.end);if(!(_<=s))throw new Error("Overlapping edit");o=o.substring(0,d)+v.newText+o.substring(_,o.length),s=d}return o}}(Wt||(Wt={}));var r,rn=class{constructor(t,e,n,i){this._uri=t,this._languageId=e,this._version=n,this._content=i,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(t){if(t){let e=this.offsetAt(t.start),n=this.offsetAt(t.end);return this._content.substring(e,n)}return this._content}update(t,e){this._content=t.text,this._version=e,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let t=[],e=this._content,n=!0;for(let i=0;i<e.length;i++){n&&(t.push(i),n=!1);let o=e.charAt(i);n=o==="\r"||o===`
`,o==="\r"&&i+1<e.length&&e.charAt(i+1)===`
`&&i++}n&&e.length>0&&t.push(e.length),this._lineOffsets=t}return this._lineOffsets}positionAt(t){t=Math.max(Math.min(t,this._content.length),0);let e=this.getLineOffsets(),n=0,i=e.length;if(i===0)return k.create(0,t);for(;n<i;){let a=Math.floor((n+i)/2);e[a]>t?i=a:n=a+1}let o=n-1;return k.create(o,t-e[o])}offsetAt(t){let e=this.getLineOffsets();if(t.line>=e.length)return this._content.length;if(t.line<0)return 0;let n=e[t.line],i=t.line+1<e.length?e[t.line+1]:this._content.length;return Math.max(Math.min(n+t.character,i),n)}get lineCount(){return this.getLineOffsets().length}};(function(t){const e=Object.prototype.toString;t.defined=function(n){return n!==void 0},t.undefined=function(n){return n===void 0},t.boolean=function(n){return n===!0||n===!1},t.string=function(n){return e.call(n)==="[object String]"},t.number=function(n){return e.call(n)==="[object Number]"},t.numberRange=function(n,i,o){return e.call(n)==="[object Number]"&&i<=n&&n<=o},t.integer=function(n){return e.call(n)==="[object Number]"&&-2147483648<=n&&n<=2147483647},t.uinteger=function(n){return e.call(n)==="[object Number]"&&0<=n&&n<=2147483647},t.func=function(n){return e.call(n)==="[object Function]"},t.objectLiteral=function(n){return n!==null&&typeof n=="object"},t.typedArray=function(n,i){return Array.isArray(n)&&n.every(i)}})(r||(r={}));var on=class{constructor(t,e,n){this._languageId=t,this._worker=e,this._disposables=[],this._listener=Object.create(null);const i=a=>{let s,c=a.getLanguageId();c===this._languageId&&(this._listener[a.uri.toString()]=a.onDidChangeContent(()=>{window.clearTimeout(s),s=window.setTimeout(()=>this._doValidate(a.uri,c),500)}),this._doValidate(a.uri,c))},o=a=>{u.editor.setModelMarkers(a,this._languageId,[]);let s=a.uri.toString(),c=this._listener[s];c&&(c.dispose(),delete this._listener[s])};this._disposables.push(u.editor.onDidCreateModel(i)),this._disposables.push(u.editor.onWillDisposeModel(o)),this._disposables.push(u.editor.onDidChangeModelLanguage(a=>{o(a.model),i(a.model)})),this._disposables.push(n(a=>{u.editor.getModels().forEach(s=>{s.getLanguageId()===this._languageId&&(o(s),i(s))})})),this._disposables.push({dispose:()=>{u.editor.getModels().forEach(o);for(let a in this._listener)this._listener[a].dispose()}}),u.editor.getModels().forEach(i)}dispose(){this._disposables.forEach(t=>t&&t.dispose()),this._disposables.length=0}_doValidate(t,e){this._worker(t).then(n=>n.doValidation(t.toString())).then(n=>{const i=n.map(a=>function(s,c){let v=typeof c.code=="number"?String(c.code):c.code;return{severity:an(c.severity),startLineNumber:c.range.start.line+1,startColumn:c.range.start.character+1,endLineNumber:c.range.end.line+1,endColumn:c.range.end.character+1,message:c.message,code:v,source:c.source}}(0,a));let o=u.editor.getModel(t);o&&o.getLanguageId()===e&&u.editor.setModelMarkers(o,e,i)}).then(void 0,n=>{console.error(n)})}};function an(t){switch(t){case S.Error:return u.MarkerSeverity.Error;case S.Warning:return u.MarkerSeverity.Warning;case S.Information:return u.MarkerSeverity.Info;case S.Hint:return u.MarkerSeverity.Hint;default:return u.MarkerSeverity.Info}}var sn=class{constructor(t,e){this._worker=t,this._triggerCharacters=e}get triggerCharacters(){return this._triggerCharacters}provideCompletionItems(t,e,n,i){const o=t.uri;return this._worker(o).then(a=>a.doComplete(o.toString(),x(e))).then(a=>{if(!a)return;const s=t.getWordUntilPosition(e),c=new u.Range(e.lineNumber,s.startColumn,e.lineNumber,s.endColumn),v=a.items.map(d=>{const _={label:d.label,insertText:d.insertText||d.label,sortText:d.sortText,filterText:d.filterText,documentation:d.documentation,detail:d.detail,command:(I=d.command,I&&I.command==="editor.action.triggerSuggest"?{id:I.command,title:I.title,arguments:I.arguments}:void 0),range:c,kind:un(d.kind)};var I,ue;return d.textEdit&&((ue=d.textEdit).insert!==void 0&&ue.replace!==void 0?_.range={insert:b(d.textEdit.insert),replace:b(d.textEdit.replace)}:_.range=b(d.textEdit.range),_.insertText=d.textEdit.newText),d.additionalTextEdits&&(_.additionalTextEdits=d.additionalTextEdits.map(F)),d.insertTextFormat===se.Snippet&&(_.insertTextRules=u.languages.CompletionItemInsertTextRule.InsertAsSnippet),_});return{isIncomplete:a.isIncomplete,suggestions:v}})}};function x(t){if(t)return{character:t.column-1,line:t.lineNumber-1}}function zt(t){if(t)return{start:{line:t.startLineNumber-1,character:t.startColumn-1},end:{line:t.endLineNumber-1,character:t.endColumn-1}}}function b(t){if(t)return new u.Range(t.start.line+1,t.start.character+1,t.end.line+1,t.end.character+1)}function un(t){const e=u.languages.CompletionItemKind;switch(t){case m.Text:return e.Text;case m.Method:return e.Method;case m.Function:return e.Function;case m.Constructor:return e.Constructor;case m.Field:return e.Field;case m.Variable:return e.Variable;case m.Class:return e.Class;case m.Interface:return e.Interface;case m.Module:return e.Module;case m.Property:return e.Property;case m.Unit:return e.Unit;case m.Value:return e.Value;case m.Enum:return e.Enum;case m.Keyword:return e.Keyword;case m.Snippet:return e.Snippet;case m.Color:return e.Color;case m.File:return e.File;case m.Reference:return e.Reference}return e.Property}function F(t){if(t)return{range:b(t.range),text:t.newText}}var cn=class{constructor(t){this._worker=t}provideHover(t,e,n){let i=t.uri;return this._worker(i).then(o=>o.doHover(i.toString(),x(e))).then(o=>{if(o)return{range:b(o.range),contents:dn(o.contents)}})}};function Ht(t){return typeof t=="string"?{value:t}:(e=t)&&typeof e=="object"&&typeof e.kind=="string"?t.kind==="plaintext"?{value:t.value.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}:{value:t.value}:{value:"```"+t.language+`
`+t.value+"\n```\n"};var e}function dn(t){if(t)return Array.isArray(t)?t.map(Ht):[Ht(t)]}var ln=class{constructor(t){this._worker=t}provideDocumentHighlights(t,e,n){const i=t.uri;return this._worker(i).then(o=>o.findDocumentHighlights(i.toString(),x(e))).then(o=>{if(o)return o.map(a=>({range:b(a.range),kind:gn(a.kind)}))})}};function gn(t){switch(t){case P.Read:return u.languages.DocumentHighlightKind.Read;case P.Write:return u.languages.DocumentHighlightKind.Write;case P.Text:return u.languages.DocumentHighlightKind.Text}return u.languages.DocumentHighlightKind.Text}var fn=class{constructor(t){this._worker=t}provideDefinition(t,e,n){const i=t.uri;return this._worker(i).then(o=>o.findDefinition(i.toString(),x(e))).then(o=>{if(o)return[$t(o)]})}};function $t(t){return{uri:u.Uri.parse(t.uri),range:b(t.range)}}var hn=class{constructor(t){this._worker=t}provideReferences(t,e,n,i){const o=t.uri;return this._worker(o).then(a=>a.findReferences(o.toString(),x(e))).then(a=>{if(a)return a.map($t)})}},mn=class{constructor(t){this._worker=t}provideRenameEdits(t,e,n,i){const o=t.uri;return this._worker(o).then(a=>a.doRename(o.toString(),x(e),n)).then(a=>function(s){if(!s||!s.changes)return;let c=[];for(let v in s.changes){const d=u.Uri.parse(v);for(let _ of s.changes[v])c.push({resource:d,versionId:void 0,textEdit:{range:b(_.range),text:_.newText}})}return{edits:c}}(a))}},pn=class{constructor(t){this._worker=t}provideDocumentSymbols(t,e){const n=t.uri;return this._worker(n).then(i=>i.findDocumentSymbols(n.toString())).then(i=>{if(i)return i.map(o=>"children"in o?Bt(o):{name:o.name,detail:"",containerName:o.containerName,kind:qt(o.kind),range:b(o.location.range),selectionRange:b(o.location.range),tags:[]})})}};function Bt(t){return{name:t.name,detail:t.detail??"",kind:qt(t.kind),range:b(t.range),selectionRange:b(t.selectionRange),tags:t.tags??[],children:(t.children??[]).map(e=>Bt(e))}}function qt(t){let e=u.languages.SymbolKind;switch(t){case p.File:return e.File;case p.Module:return e.Module;case p.Namespace:return e.Namespace;case p.Package:return e.Package;case p.Class:return e.Class;case p.Method:return e.Method;case p.Property:return e.Property;case p.Field:return e.Field;case p.Constructor:return e.Constructor;case p.Enum:return e.Enum;case p.Interface:return e.Interface;case p.Function:return e.Function;case p.Variable:return e.Variable;case p.Constant:return e.Constant;case p.String:return e.String;case p.Number:return e.Number;case p.Boolean:return e.Boolean;case p.Array:return e.Array}return e.Function}var En=class{constructor(t){this._worker=t}provideLinks(t,e){const n=t.uri;return this._worker(n).then(i=>i.findDocumentLinks(n.toString())).then(i=>{if(i)return{links:i.map(o=>({range:b(o.range),url:o.target}))}})}},vn=class{constructor(t){this._worker=t}provideDocumentFormattingEdits(t,e,n){const i=t.uri;return this._worker(i).then(o=>o.format(i.toString(),null,Qt(e)).then(a=>{if(a&&a.length!==0)return a.map(F)}))}},bn=class{constructor(t){this._worker=t,this.canFormatMultipleRanges=!1}provideDocumentRangeFormattingEdits(t,e,n,i){const o=t.uri;return this._worker(o).then(a=>a.format(o.toString(),zt(e),Qt(n)).then(s=>{if(s&&s.length!==0)return s.map(F)}))}};function Qt(t){return{tabSize:t.tabSize,insertSpaces:t.insertSpaces}}var _n=class{constructor(t){this._worker=t}provideDocumentColors(t,e){const n=t.uri;return this._worker(n).then(i=>i.findDocumentColors(n.toString())).then(i=>{if(i)return i.map(o=>({color:o.color,range:b(o.range)}))})}provideColorPresentations(t,e,n){const i=t.uri;return this._worker(i).then(o=>o.getColorPresentations(i.toString(),e.color,zt(e.range))).then(o=>{if(o)return o.map(a=>{let s={label:a.label};return a.textEdit&&(s.textEdit=F(a.textEdit)),a.additionalTextEdits&&(s.additionalTextEdits=a.additionalTextEdits.map(F)),s})})}},kn=class{constructor(t){this._worker=t}provideFoldingRanges(t,e,n){const i=t.uri;return this._worker(i).then(o=>o.getFoldingRanges(i.toString(),e)).then(o=>{if(o)return o.map(a=>{const s={start:a.startLine+1,end:a.endLine+1};return a.kind!==void 0&&(s.kind=function(c){switch(c){case D.Comment:return u.languages.FoldingRangeKind.Comment;case D.Imports:return u.languages.FoldingRangeKind.Imports;case D.Region:return u.languages.FoldingRangeKind.Region}}(a.kind)),s})})}},wn=class{constructor(t){this._worker=t}provideSelectionRanges(t,e,n){const i=t.uri;return this._worker(i).then(o=>o.getSelectionRanges(i.toString(),e.map(x))).then(o=>{if(o)return o.map(a=>{const s=[];for(;a;)s.push({range:b(a.range)}),a=a.parent;return s})})}};function An(t){const e=[],n=[],i=new nn(t);e.push(i);const o=(...a)=>i.getLanguageServiceWorker(...a);return function(){const{languageId:a,modeConfiguration:s}=t;Gt(n),s.completionItems&&n.push(u.languages.registerCompletionItemProvider(a,new sn(o,["/","-",":"]))),s.hovers&&n.push(u.languages.registerHoverProvider(a,new cn(o))),s.documentHighlights&&n.push(u.languages.registerDocumentHighlightProvider(a,new ln(o))),s.definitions&&n.push(u.languages.registerDefinitionProvider(a,new fn(o))),s.references&&n.push(u.languages.registerReferenceProvider(a,new hn(o))),s.documentSymbols&&n.push(u.languages.registerDocumentSymbolProvider(a,new pn(o))),s.rename&&n.push(u.languages.registerRenameProvider(a,new mn(o))),s.colors&&n.push(u.languages.registerColorProvider(a,new _n(o))),s.foldingRanges&&n.push(u.languages.registerFoldingRangeProvider(a,new kn(o))),s.diagnostics&&n.push(new on(a,o,t.onDidChange)),s.selectionRanges&&n.push(u.languages.registerSelectionRangeProvider(a,new wn(o))),s.documentFormattingEdits&&n.push(u.languages.registerDocumentFormattingEditProvider(a,new vn(o))),s.documentRangeFormattingEdits&&n.push(u.languages.registerDocumentRangeFormattingEditProvider(a,new bn(o)))}(),e.push(Xt(n)),Xt(e)}function Xt(t){return{dispose:()=>Gt(t)}}function Gt(t){for(;t.length;)t.pop().dispose()}export{sn as CompletionAdapter,fn as DefinitionAdapter,on as DiagnosticsAdapter,_n as DocumentColorAdapter,vn as DocumentFormattingEditProvider,ln as DocumentHighlightAdapter,En as DocumentLinkAdapter,bn as DocumentRangeFormattingEditProvider,pn as DocumentSymbolAdapter,kn as FoldingRangeAdapter,cn as HoverAdapter,hn as ReferenceAdapter,mn as RenameAdapter,wn as SelectionRangeAdapter,nn as WorkerManager,x as fromPosition,zt as fromRange,An as setupMode,b as toRange,F as toTextEdit};

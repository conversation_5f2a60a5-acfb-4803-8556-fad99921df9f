import{m as In}from"./toggleHighContrast-BSg_W9Au.js";import"./preload-helper-Dv6uf1Os.js";var An=Object.defineProperty,Sn=Object.getOwnPropertyDescriptor,Tn=Object.getOwnPropertyNames,Ln=Object.prototype.hasOwnProperty,d={};((t,e,r,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let a of Tn(e))Ln.call(t,a)||a===r||An(t,a,{get:()=>e[a],enumerable:!(n=Sn(e,a))||n.enumerable})})(d,In,"default");var Se,se,Te,H,Z,X,R,Le,w,Re,ee,Me,Fe,Pe,ce,je,De,Ne,Oe,Ue,J,ue,Ve,Be,de,Ke,W,te,<PERSON>,<PERSON>,<PERSON>e,ne,ze,O,qe,U,re,le,$e,V,Qe,ie,ge,Je,fe,Ge,he,Ye,me,Ze,pe,et,tt,nt,rt,ve,it,ot,at,be,z,q,x,k,Ee,st,ct,ut,dt,lt,gt,ft,ht,mt,oe,pt,vt,bt,kt,G,ke,Ct,E,v,_t,wt,yt,xt,Et,It,F,ae,At,St,Tt,Lt,Rt,Mt,Ft,Pt,jt,Dt,Nt,Ot,Ce,Ut,_,Vt,M,Bt,Kt,Wt,Ht,Xt,zt,qt,$t,Qt,_e,we,ye,Jt,Gt,Yt,Zt,en,tn,nn,rn,on,an,sn,cn,Rn=class{constructor(t){this._defaults=t,this._worker=null,this._client=null,this._idleCheckInterval=window.setInterval(()=>this._checkIfIdle(),3e4),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange(()=>this._stopWorker())}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}dispose(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()}_checkIfIdle(){this._worker&&Date.now()-this._lastUsedTime>12e4&&this._stopWorker()}_getClient(){return this._lastUsedTime=Date.now(),this._client||(this._worker=d.editor.createWebWorker({moduleId:"vs/language/json/jsonWorker",label:this._defaults.languageId,createData:{languageSettings:this._defaults.diagnosticsOptions,languageId:this._defaults.languageId,enableSchemaRequest:this._defaults.diagnosticsOptions.enableSchemaRequest}}),this._client=this._worker.getProxy()),this._client}getLanguageServiceWorker(...t){let e;return this._getClient().then(r=>{e=r}).then(r=>{if(this._worker)return this._worker.withSyncedResources(t)}).then(r=>e)}};(Se||(Se={})).is=function(t){return typeof t=="string"},(se||(se={})).is=function(t){return typeof t=="string"},(H=Te||(Te={})).MIN_VALUE=-2147483648,H.MAX_VALUE=2147483647,H.is=function(t){return typeof t=="number"&&H.MIN_VALUE<=t&&t<=H.MAX_VALUE},(X=Z||(Z={})).MIN_VALUE=0,X.MAX_VALUE=2147483647,X.is=function(t){return typeof t=="number"&&X.MIN_VALUE<=t&&t<=X.MAX_VALUE},(Le=R||(R={})).create=function(t,e){return t===Number.MAX_VALUE&&(t=Z.MAX_VALUE),e===Number.MAX_VALUE&&(e=Z.MAX_VALUE),{line:t,character:e}},Le.is=function(t){let e=t;return o.objectLiteral(e)&&o.uinteger(e.line)&&o.uinteger(e.character)},(Re=w||(w={})).create=function(t,e,r,n){if(o.uinteger(t)&&o.uinteger(e)&&o.uinteger(r)&&o.uinteger(n))return{start:R.create(t,e),end:R.create(r,n)};if(R.is(t)&&R.is(e))return{start:t,end:e};throw new Error(`Range#create called with invalid arguments[${t}, ${e}, ${r}, ${n}]`)},Re.is=function(t){let e=t;return o.objectLiteral(e)&&R.is(e.start)&&R.is(e.end)},(Me=ee||(ee={})).create=function(t,e){return{uri:t,range:e}},Me.is=function(t){let e=t;return o.objectLiteral(e)&&w.is(e.range)&&(o.string(e.uri)||o.undefined(e.uri))},(Pe=Fe||(Fe={})).create=function(t,e,r,n){return{targetUri:t,targetRange:e,targetSelectionRange:r,originSelectionRange:n}},Pe.is=function(t){let e=t;return o.objectLiteral(e)&&w.is(e.targetRange)&&o.string(e.targetUri)&&w.is(e.targetSelectionRange)&&(w.is(e.originSelectionRange)||o.undefined(e.originSelectionRange))},(je=ce||(ce={})).create=function(t,e,r,n){return{red:t,green:e,blue:r,alpha:n}},je.is=function(t){const e=t;return o.objectLiteral(e)&&o.numberRange(e.red,0,1)&&o.numberRange(e.green,0,1)&&o.numberRange(e.blue,0,1)&&o.numberRange(e.alpha,0,1)},(Ne=De||(De={})).create=function(t,e){return{range:t,color:e}},Ne.is=function(t){const e=t;return o.objectLiteral(e)&&w.is(e.range)&&ce.is(e.color)},(Ue=Oe||(Oe={})).create=function(t,e,r){return{label:t,textEdit:e,additionalTextEdits:r}},Ue.is=function(t){const e=t;return o.objectLiteral(e)&&o.string(e.label)&&(o.undefined(e.textEdit)||U.is(e))&&(o.undefined(e.additionalTextEdits)||o.typedArray(e.additionalTextEdits,U.is))},(ue=J||(J={})).Comment="comment",ue.Imports="imports",ue.Region="region",(Be=Ve||(Ve={})).create=function(t,e,r,n,a,s){const c={startLine:t,endLine:e};return o.defined(r)&&(c.startCharacter=r),o.defined(n)&&(c.endCharacter=n),o.defined(a)&&(c.kind=a),o.defined(s)&&(c.collapsedText=s),c},Be.is=function(t){const e=t;return o.objectLiteral(e)&&o.uinteger(e.startLine)&&o.uinteger(e.startLine)&&(o.undefined(e.startCharacter)||o.uinteger(e.startCharacter))&&(o.undefined(e.endCharacter)||o.uinteger(e.endCharacter))&&(o.undefined(e.kind)||o.string(e.kind))},(Ke=de||(de={})).create=function(t,e){return{location:t,message:e}},Ke.is=function(t){let e=t;return o.defined(e)&&ee.is(e.location)&&o.string(e.message)},(te=W||(W={})).Error=1,te.Warning=2,te.Information=3,te.Hint=4,(He=We||(We={})).Unnecessary=1,He.Deprecated=2,(Xe||(Xe={})).is=function(t){const e=t;return o.objectLiteral(e)&&o.string(e.href)},(ze=ne||(ne={})).create=function(t,e,r,n,a,s){let c={range:t,message:e};return o.defined(r)&&(c.severity=r),o.defined(n)&&(c.code=n),o.defined(a)&&(c.source=a),o.defined(s)&&(c.relatedInformation=s),c},ze.is=function(t){var e;let r=t;return o.defined(r)&&w.is(r.range)&&o.string(r.message)&&(o.number(r.severity)||o.undefined(r.severity))&&(o.integer(r.code)||o.string(r.code)||o.undefined(r.code))&&(o.undefined(r.codeDescription)||o.string((e=r.codeDescription)===null||e===void 0?void 0:e.href))&&(o.string(r.source)||o.undefined(r.source))&&(o.undefined(r.relatedInformation)||o.typedArray(r.relatedInformation,de.is))},(qe=O||(O={})).create=function(t,e,...r){let n={title:t,command:e};return o.defined(r)&&r.length>0&&(n.arguments=r),n},qe.is=function(t){let e=t;return o.defined(e)&&o.string(e.title)&&o.string(e.command)},(re=U||(U={})).replace=function(t,e){return{range:t,newText:e}},re.insert=function(t,e){return{range:{start:t,end:t},newText:e}},re.del=function(t){return{range:t,newText:""}},re.is=function(t){const e=t;return o.objectLiteral(e)&&o.string(e.newText)&&w.is(e.range)},($e=le||(le={})).create=function(t,e,r){const n={label:t};return e!==void 0&&(n.needsConfirmation=e),r!==void 0&&(n.description=r),n},$e.is=function(t){const e=t;return o.objectLiteral(e)&&o.string(e.label)&&(o.boolean(e.needsConfirmation)||e.needsConfirmation===void 0)&&(o.string(e.description)||e.description===void 0)},(V||(V={})).is=function(t){const e=t;return o.string(e)},(ie=Qe||(Qe={})).replace=function(t,e,r){return{range:t,newText:e,annotationId:r}},ie.insert=function(t,e,r){return{range:{start:t,end:t},newText:e,annotationId:r}},ie.del=function(t,e){return{range:t,newText:"",annotationId:e}},ie.is=function(t){const e=t;return U.is(e)&&(le.is(e.annotationId)||V.is(e.annotationId))},(Je=ge||(ge={})).create=function(t,e){return{textDocument:t,edits:e}},Je.is=function(t){let e=t;return o.defined(e)&&ve.is(e.textDocument)&&Array.isArray(e.edits)},(Ge=fe||(fe={})).create=function(t,e,r){let n={kind:"create",uri:t};return e===void 0||e.overwrite===void 0&&e.ignoreIfExists===void 0||(n.options=e),r!==void 0&&(n.annotationId=r),n},Ge.is=function(t){let e=t;return e&&e.kind==="create"&&o.string(e.uri)&&(e.options===void 0||(e.options.overwrite===void 0||o.boolean(e.options.overwrite))&&(e.options.ignoreIfExists===void 0||o.boolean(e.options.ignoreIfExists)))&&(e.annotationId===void 0||V.is(e.annotationId))},(Ye=he||(he={})).create=function(t,e,r,n){let a={kind:"rename",oldUri:t,newUri:e};return r===void 0||r.overwrite===void 0&&r.ignoreIfExists===void 0||(a.options=r),n!==void 0&&(a.annotationId=n),a},Ye.is=function(t){let e=t;return e&&e.kind==="rename"&&o.string(e.oldUri)&&o.string(e.newUri)&&(e.options===void 0||(e.options.overwrite===void 0||o.boolean(e.options.overwrite))&&(e.options.ignoreIfExists===void 0||o.boolean(e.options.ignoreIfExists)))&&(e.annotationId===void 0||V.is(e.annotationId))},(Ze=me||(me={})).create=function(t,e,r){let n={kind:"delete",uri:t};return e===void 0||e.recursive===void 0&&e.ignoreIfNotExists===void 0||(n.options=e),r!==void 0&&(n.annotationId=r),n},Ze.is=function(t){let e=t;return e&&e.kind==="delete"&&o.string(e.uri)&&(e.options===void 0||(e.options.recursive===void 0||o.boolean(e.options.recursive))&&(e.options.ignoreIfNotExists===void 0||o.boolean(e.options.ignoreIfNotExists)))&&(e.annotationId===void 0||V.is(e.annotationId))},(pe||(pe={})).is=function(t){let e=t;return e&&(e.changes!==void 0||e.documentChanges!==void 0)&&(e.documentChanges===void 0||e.documentChanges.every(r=>o.string(r.kind)?fe.is(r)||he.is(r)||me.is(r):ge.is(r)))},(tt=et||(et={})).create=function(t){return{uri:t}},tt.is=function(t){let e=t;return o.defined(e)&&o.string(e.uri)},(rt=nt||(nt={})).create=function(t,e){return{uri:t,version:e}},rt.is=function(t){let e=t;return o.defined(e)&&o.string(e.uri)&&o.integer(e.version)},(it=ve||(ve={})).create=function(t,e){return{uri:t,version:e}},it.is=function(t){let e=t;return o.defined(e)&&o.string(e.uri)&&(e.version===null||o.integer(e.version))},(at=ot||(ot={})).create=function(t,e,r,n){return{uri:t,languageId:e,version:r,text:n}},at.is=function(t){let e=t;return o.defined(e)&&o.string(e.uri)&&o.string(e.languageId)&&o.integer(e.version)&&o.string(e.text)},(z=be||(be={})).PlainText="plaintext",z.Markdown="markdown",z.is=function(t){const e=t;return e===z.PlainText||e===z.Markdown},(q||(q={})).is=function(t){const e=t;return o.objectLiteral(t)&&be.is(e.kind)&&o.string(e.value)},(k=x||(x={})).Text=1,k.Method=2,k.Function=3,k.Constructor=4,k.Field=5,k.Variable=6,k.Class=7,k.Interface=8,k.Module=9,k.Property=10,k.Unit=11,k.Value=12,k.Enum=13,k.Keyword=14,k.Snippet=15,k.Color=16,k.File=17,k.Reference=18,k.Folder=19,k.EnumMember=20,k.Constant=21,k.Struct=22,k.Event=23,k.Operator=24,k.TypeParameter=25,(st=Ee||(Ee={})).PlainText=1,st.Snippet=2,(ct||(ct={})).Deprecated=1,(dt=ut||(ut={})).create=function(t,e,r){return{newText:t,insert:e,replace:r}},dt.is=function(t){const e=t;return e&&o.string(e.newText)&&w.is(e.insert)&&w.is(e.replace)},(gt=lt||(lt={})).asIs=1,gt.adjustIndentation=2,(ft||(ft={})).is=function(t){const e=t;return e&&(o.string(e.detail)||e.detail===void 0)&&(o.string(e.description)||e.description===void 0)},(ht||(ht={})).create=function(t){return{label:t}},(mt||(mt={})).create=function(t,e){return{items:t||[],isIncomplete:!!e}},(pt=oe||(oe={})).fromPlainText=function(t){return t.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},pt.is=function(t){const e=t;return o.string(e)||o.objectLiteral(e)&&o.string(e.language)&&o.string(e.value)},(vt||(vt={})).is=function(t){let e=t;return!!e&&o.objectLiteral(e)&&(q.is(e.contents)||oe.is(e.contents)||o.typedArray(e.contents,oe.is))&&(t.range===void 0||w.is(t.range))},(bt||(bt={})).create=function(t,e){return e?{label:t,documentation:e}:{label:t}},(kt||(kt={})).create=function(t,e,...r){let n={label:t};return o.defined(e)&&(n.documentation=e),o.defined(r)?n.parameters=r:n.parameters=[],n},(ke=G||(G={})).Text=1,ke.Read=2,ke.Write=3,(Ct||(Ct={})).create=function(t,e){let r={range:t};return o.number(e)&&(r.kind=e),r},(v=E||(E={})).File=1,v.Module=2,v.Namespace=3,v.Package=4,v.Class=5,v.Method=6,v.Property=7,v.Field=8,v.Constructor=9,v.Enum=10,v.Interface=11,v.Function=12,v.Variable=13,v.Constant=14,v.String=15,v.Number=16,v.Boolean=17,v.Array=18,v.Object=19,v.Key=20,v.Null=21,v.EnumMember=22,v.Struct=23,v.Event=24,v.Operator=25,v.TypeParameter=26,(_t||(_t={})).Deprecated=1,(wt||(wt={})).create=function(t,e,r,n,a){let s={name:t,kind:e,location:{uri:n,range:r}};return a&&(s.containerName=a),s},(yt||(yt={})).create=function(t,e,r,n){return n!==void 0?{name:t,kind:e,location:{uri:r,range:n}}:{name:t,kind:e,location:{uri:r}}},(Et=xt||(xt={})).create=function(t,e,r,n,a,s){let c={name:t,detail:e,kind:r,range:n,selectionRange:a};return s!==void 0&&(c.children=s),c},Et.is=function(t){let e=t;return e&&o.string(e.name)&&o.number(e.kind)&&w.is(e.range)&&w.is(e.selectionRange)&&(e.detail===void 0||o.string(e.detail))&&(e.deprecated===void 0||o.boolean(e.deprecated))&&(e.children===void 0||Array.isArray(e.children))&&(e.tags===void 0||Array.isArray(e.tags))},(F=It||(It={})).Empty="",F.QuickFix="quickfix",F.Refactor="refactor",F.RefactorExtract="refactor.extract",F.RefactorInline="refactor.inline",F.RefactorRewrite="refactor.rewrite",F.Source="source",F.SourceOrganizeImports="source.organizeImports",F.SourceFixAll="source.fixAll",(At=ae||(ae={})).Invoked=1,At.Automatic=2,(Tt=St||(St={})).create=function(t,e,r){let n={diagnostics:t};return e!=null&&(n.only=e),r!=null&&(n.triggerKind=r),n},Tt.is=function(t){let e=t;return o.defined(e)&&o.typedArray(e.diagnostics,ne.is)&&(e.only===void 0||o.typedArray(e.only,o.string))&&(e.triggerKind===void 0||e.triggerKind===ae.Invoked||e.triggerKind===ae.Automatic)},(Rt=Lt||(Lt={})).create=function(t,e,r){let n={title:t},a=!0;return typeof e=="string"?(a=!1,n.kind=e):O.is(e)?n.command=e:n.edit=e,a&&r!==void 0&&(n.kind=r),n},Rt.is=function(t){let e=t;return e&&o.string(e.title)&&(e.diagnostics===void 0||o.typedArray(e.diagnostics,ne.is))&&(e.kind===void 0||o.string(e.kind))&&(e.edit!==void 0||e.command!==void 0)&&(e.command===void 0||O.is(e.command))&&(e.isPreferred===void 0||o.boolean(e.isPreferred))&&(e.edit===void 0||pe.is(e.edit))},(Ft=Mt||(Mt={})).create=function(t,e){let r={range:t};return o.defined(e)&&(r.data=e),r},Ft.is=function(t){let e=t;return o.defined(e)&&w.is(e.range)&&(o.undefined(e.command)||O.is(e.command))},(jt=Pt||(Pt={})).create=function(t,e){return{tabSize:t,insertSpaces:e}},jt.is=function(t){let e=t;return o.defined(e)&&o.uinteger(e.tabSize)&&o.boolean(e.insertSpaces)},(Nt=Dt||(Dt={})).create=function(t,e,r){return{range:t,target:e,data:r}},Nt.is=function(t){let e=t;return o.defined(e)&&w.is(e.range)&&(o.undefined(e.target)||o.string(e.target))},(Ce=Ot||(Ot={})).create=function(t,e){return{range:t,parent:e}},Ce.is=function(t){let e=t;return o.objectLiteral(e)&&w.is(e.range)&&(e.parent===void 0||Ce.is(e.parent))},(_=Ut||(Ut={})).namespace="namespace",_.type="type",_.class="class",_.enum="enum",_.interface="interface",_.struct="struct",_.typeParameter="typeParameter",_.parameter="parameter",_.variable="variable",_.property="property",_.enumMember="enumMember",_.event="event",_.function="function",_.method="method",_.macro="macro",_.keyword="keyword",_.modifier="modifier",_.comment="comment",_.string="string",_.number="number",_.regexp="regexp",_.operator="operator",_.decorator="decorator",(M=Vt||(Vt={})).declaration="declaration",M.definition="definition",M.readonly="readonly",M.static="static",M.deprecated="deprecated",M.abstract="abstract",M.async="async",M.modification="modification",M.documentation="documentation",M.defaultLibrary="defaultLibrary",(Bt||(Bt={})).is=function(t){const e=t;return o.objectLiteral(e)&&(e.resultId===void 0||typeof e.resultId=="string")&&Array.isArray(e.data)&&(e.data.length===0||typeof e.data[0]=="number")},(Wt=Kt||(Kt={})).create=function(t,e){return{range:t,text:e}},Wt.is=function(t){const e=t;return e!=null&&w.is(e.range)&&o.string(e.text)},(Xt=Ht||(Ht={})).create=function(t,e,r){return{range:t,variableName:e,caseSensitiveLookup:r}},Xt.is=function(t){const e=t;return e!=null&&w.is(e.range)&&o.boolean(e.caseSensitiveLookup)&&(o.string(e.variableName)||e.variableName===void 0)},(qt=zt||(zt={})).create=function(t,e){return{range:t,expression:e}},qt.is=function(t){const e=t;return e!=null&&w.is(e.range)&&(o.string(e.expression)||e.expression===void 0)},(Qt=$t||($t={})).create=function(t,e){return{frameId:t,stoppedLocation:e}},Qt.is=function(t){const e=t;return o.defined(e)&&w.is(t.stoppedLocation)},(we=_e||(_e={})).Type=1,we.Parameter=2,we.is=function(t){return t===1||t===2},(Jt=ye||(ye={})).create=function(t){return{value:t}},Jt.is=function(t){const e=t;return o.objectLiteral(e)&&(e.tooltip===void 0||o.string(e.tooltip)||q.is(e.tooltip))&&(e.location===void 0||ee.is(e.location))&&(e.command===void 0||O.is(e.command))},(Yt=Gt||(Gt={})).create=function(t,e,r){const n={position:t,label:e};return r!==void 0&&(n.kind=r),n},Yt.is=function(t){const e=t;return o.objectLiteral(e)&&R.is(e.position)&&(o.string(e.label)||o.typedArray(e.label,ye.is))&&(e.kind===void 0||_e.is(e.kind))&&e.textEdits===void 0||o.typedArray(e.textEdits,U.is)&&(e.tooltip===void 0||o.string(e.tooltip)||q.is(e.tooltip))&&(e.paddingLeft===void 0||o.boolean(e.paddingLeft))&&(e.paddingRight===void 0||o.boolean(e.paddingRight))},(Zt||(Zt={})).createSnippet=function(t){return{kind:"snippet",value:t}},(en||(en={})).create=function(t,e,r,n){return{insertText:t,filterText:e,range:r,command:n}},(tn||(tn={})).create=function(t){return{items:t}},(rn=nn||(nn={})).Invoked=0,rn.Automatic=1,(on||(on={})).create=function(t,e){return{range:t,text:e}},(an||(an={})).create=function(t,e){return{triggerKind:t,selectedCompletionInfo:e}},(sn||(sn={})).is=function(t){const e=t;return o.objectLiteral(e)&&se.is(e.uri)&&o.string(e.name)},function(t){function e(r,n){if(r.length<=1)return r;const a=r.length/2|0,s=r.slice(0,a),c=r.slice(a);e(s,n),e(c,n);let u=0,b=0,l=0;for(;u<s.length&&b<c.length;){let p=n(s[u],c[b]);r[l++]=p<=0?s[u++]:c[b++]}for(;u<s.length;)r[l++]=s[u++];for(;b<c.length;)r[l++]=c[b++];return r}t.create=function(r,n,a,s){return new Mn(r,n,a,s)},t.is=function(r){let n=r;return!!(o.defined(n)&&o.string(n.uri)&&(o.undefined(n.languageId)||o.string(n.languageId))&&o.uinteger(n.lineCount)&&o.func(n.getText)&&o.func(n.positionAt)&&o.func(n.offsetAt))},t.applyEdits=function(r,n){let a=r.getText(),s=e(n,(u,b)=>{let l=u.range.start.line-b.range.start.line;return l===0?u.range.start.character-b.range.start.character:l}),c=a.length;for(let u=s.length-1;u>=0;u--){let b=s[u],l=r.offsetAt(b.range.start),p=r.offsetAt(b.range.end);if(!(p<=c))throw new Error("Overlapping edit");a=a.substring(0,l)+b.newText+a.substring(p,a.length),c=l}return a}}(cn||(cn={}));var o,Mn=class{constructor(t,e,r,n){this._uri=t,this._languageId=e,this._version=r,this._content=n,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(t){if(t){let e=this.offsetAt(t.start),r=this.offsetAt(t.end);return this._content.substring(e,r)}return this._content}update(t,e){this._content=t.text,this._version=e,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let t=[],e=this._content,r=!0;for(let n=0;n<e.length;n++){r&&(t.push(n),r=!1);let a=e.charAt(n);r=a==="\r"||a===`
`,a==="\r"&&n+1<e.length&&e.charAt(n+1)===`
`&&n++}r&&e.length>0&&t.push(e.length),this._lineOffsets=t}return this._lineOffsets}positionAt(t){t=Math.max(Math.min(t,this._content.length),0);let e=this.getLineOffsets(),r=0,n=e.length;if(n===0)return R.create(0,t);for(;r<n;){let s=Math.floor((r+n)/2);e[s]>t?n=s:r=s+1}let a=r-1;return R.create(a,t-e[a])}offsetAt(t){let e=this.getLineOffsets();if(t.line>=e.length)return this._content.length;if(t.line<0)return 0;let r=e[t.line],n=t.line+1<e.length?e[t.line+1]:this._content.length;return Math.max(Math.min(r+t.character,n),r)}get lineCount(){return this.getLineOffsets().length}};(function(t){const e=Object.prototype.toString;t.defined=function(r){return r!==void 0},t.undefined=function(r){return r===void 0},t.boolean=function(r){return r===!0||r===!1},t.string=function(r){return e.call(r)==="[object String]"},t.number=function(r){return e.call(r)==="[object Number]"},t.numberRange=function(r,n,a){return e.call(r)==="[object Number]"&&n<=r&&r<=a},t.integer=function(r){return e.call(r)==="[object Number]"&&-2147483648<=r&&r<=2147483647},t.uinteger=function(r){return e.call(r)==="[object Number]"&&0<=r&&r<=2147483647},t.func=function(r){return e.call(r)==="[object Function]"},t.objectLiteral=function(r){return r!==null&&typeof r=="object"},t.typedArray=function(r,n){return Array.isArray(r)&&r.every(n)}})(o||(o={}));var Fn=class{constructor(t,e,r){this._languageId=t,this._worker=e,this._disposables=[],this._listener=Object.create(null);const n=s=>{let c,u=s.getLanguageId();u===this._languageId&&(this._listener[s.uri.toString()]=s.onDidChangeContent(()=>{window.clearTimeout(c),c=window.setTimeout(()=>this._doValidate(s.uri,u),500)}),this._doValidate(s.uri,u))},a=s=>{d.editor.setModelMarkers(s,this._languageId,[]);let c=s.uri.toString(),u=this._listener[c];u&&(u.dispose(),delete this._listener[c])};this._disposables.push(d.editor.onDidCreateModel(n)),this._disposables.push(d.editor.onWillDisposeModel(a)),this._disposables.push(d.editor.onDidChangeModelLanguage(s=>{a(s.model),n(s.model)})),this._disposables.push(r(s=>{d.editor.getModels().forEach(c=>{c.getLanguageId()===this._languageId&&(a(c),n(c))})})),this._disposables.push({dispose:()=>{d.editor.getModels().forEach(a);for(let s in this._listener)this._listener[s].dispose()}}),d.editor.getModels().forEach(n)}dispose(){this._disposables.forEach(t=>t&&t.dispose()),this._disposables.length=0}_doValidate(t,e){this._worker(t).then(r=>r.doValidation(t.toString())).then(r=>{const n=r.map(s=>function(c,u){let b=typeof u.code=="number"?String(u.code):u.code;return{severity:Pn(u.severity),startLineNumber:u.range.start.line+1,startColumn:u.range.start.character+1,endLineNumber:u.range.end.line+1,endColumn:u.range.end.character+1,message:u.message,code:b,source:u.source}}(0,s));let a=d.editor.getModel(t);a&&a.getLanguageId()===e&&d.editor.setModelMarkers(a,e,n)}).then(void 0,r=>{console.error(r)})}};function Pn(t){switch(t){case W.Error:return d.MarkerSeverity.Error;case W.Warning:return d.MarkerSeverity.Warning;case W.Information:return d.MarkerSeverity.Info;case W.Hint:return d.MarkerSeverity.Hint;default:return d.MarkerSeverity.Info}}var jn=class{constructor(t,e){this._worker=t,this._triggerCharacters=e}get triggerCharacters(){return this._triggerCharacters}provideCompletionItems(t,e,r,n){const a=t.uri;return this._worker(a).then(s=>s.doComplete(a.toString(),j(e))).then(s=>{if(!s)return;const c=t.getWordUntilPosition(e),u=new d.Range(e.lineNumber,c.startColumn,e.lineNumber,c.endColumn),b=s.items.map(l=>{const p={label:l.label,insertText:l.insertText||l.label,sortText:l.sortText,filterText:l.filterText,documentation:l.documentation,detail:l.detail,command:(h=l.command,h&&h.command==="editor.action.triggerSuggest"?{id:h.command,title:h.title,arguments:h.arguments}:void 0),range:u,kind:Dn(l.kind)};var h,P;return l.textEdit&&((P=l.textEdit).insert!==void 0&&P.replace!==void 0?p.range={insert:S(l.textEdit.insert),replace:S(l.textEdit.replace)}:p.range=S(l.textEdit.range),p.insertText=l.textEdit.newText),l.additionalTextEdits&&(p.additionalTextEdits=l.additionalTextEdits.map(Y)),l.insertTextFormat===Ee.Snippet&&(p.insertTextRules=d.languages.CompletionItemInsertTextRule.InsertAsSnippet),p});return{isIncomplete:s.isIncomplete,suggestions:b}})}};function j(t){if(t)return{character:t.column-1,line:t.lineNumber-1}}function kn(t){if(t)return{start:{line:t.startLineNumber-1,character:t.startColumn-1},end:{line:t.endLineNumber-1,character:t.endColumn-1}}}function S(t){if(t)return new d.Range(t.start.line+1,t.start.character+1,t.end.line+1,t.end.character+1)}function Dn(t){const e=d.languages.CompletionItemKind;switch(t){case x.Text:return e.Text;case x.Method:return e.Method;case x.Function:return e.Function;case x.Constructor:return e.Constructor;case x.Field:return e.Field;case x.Variable:return e.Variable;case x.Class:return e.Class;case x.Interface:return e.Interface;case x.Module:return e.Module;case x.Property:return e.Property;case x.Unit:return e.Unit;case x.Value:return e.Value;case x.Enum:return e.Enum;case x.Keyword:return e.Keyword;case x.Snippet:return e.Snippet;case x.Color:return e.Color;case x.File:return e.File;case x.Reference:return e.Reference}return e.Property}function Y(t){if(t)return{range:S(t.range),text:t.newText}}var Nn=class{constructor(t){this._worker=t}provideHover(t,e,r){let n=t.uri;return this._worker(n).then(a=>a.doHover(n.toString(),j(e))).then(a=>{if(a)return{range:S(a.range),contents:On(a.contents)}})}};function un(t){return typeof t=="string"?{value:t}:(e=t)&&typeof e=="object"&&typeof e.kind=="string"?t.kind==="plaintext"?{value:t.value.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}:{value:t.value}:{value:"```"+t.language+`
`+t.value+"\n```\n"};var e}function On(t){if(t)return Array.isArray(t)?t.map(un):[un(t)]}var sr=class{constructor(t){this._worker=t}provideDocumentHighlights(t,e,r){const n=t.uri;return this._worker(n).then(a=>a.findDocumentHighlights(n.toString(),j(e))).then(a=>{if(a)return a.map(s=>({range:S(s.range),kind:Un(s.kind)}))})}};function Un(t){switch(t){case G.Read:return d.languages.DocumentHighlightKind.Read;case G.Write:return d.languages.DocumentHighlightKind.Write;case G.Text:return d.languages.DocumentHighlightKind.Text}return d.languages.DocumentHighlightKind.Text}var cr=class{constructor(t){this._worker=t}provideDefinition(t,e,r){const n=t.uri;return this._worker(n).then(a=>a.findDefinition(n.toString(),j(e))).then(a=>{if(a)return[Cn(a)]})}};function Cn(t){return{uri:d.Uri.parse(t.uri),range:S(t.range)}}var ur=class{constructor(t){this._worker=t}provideReferences(t,e,r,n){const a=t.uri;return this._worker(a).then(s=>s.findReferences(a.toString(),j(e))).then(s=>{if(s)return s.map(Cn)})}},dr=class{constructor(t){this._worker=t}provideRenameEdits(t,e,r,n){const a=t.uri;return this._worker(a).then(s=>s.doRename(a.toString(),j(e),r)).then(s=>function(c){if(!c||!c.changes)return;let u=[];for(let b in c.changes){const l=d.Uri.parse(b);for(let p of c.changes[b])u.push({resource:l,versionId:void 0,textEdit:{range:S(p.range),text:p.newText}})}return{edits:u}}(s))}},Vn=class{constructor(t){this._worker=t}provideDocumentSymbols(t,e){const r=t.uri;return this._worker(r).then(n=>n.findDocumentSymbols(r.toString())).then(n=>{if(n)return n.map(a=>"children"in a?_n(a):{name:a.name,detail:"",containerName:a.containerName,kind:wn(a.kind),range:S(a.location.range),selectionRange:S(a.location.range),tags:[]})})}};function _n(t){return{name:t.name,detail:t.detail??"",kind:wn(t.kind),range:S(t.range),selectionRange:S(t.selectionRange),tags:t.tags??[],children:(t.children??[]).map(e=>_n(e))}}function wn(t){let e=d.languages.SymbolKind;switch(t){case E.File:return e.File;case E.Module:return e.Module;case E.Namespace:return e.Namespace;case E.Package:return e.Package;case E.Class:return e.Class;case E.Method:return e.Method;case E.Property:return e.Property;case E.Field:return e.Field;case E.Constructor:return e.Constructor;case E.Enum:return e.Enum;case E.Interface:return e.Interface;case E.Function:return e.Function;case E.Variable:return e.Variable;case E.Constant:return e.Constant;case E.String:return e.String;case E.Number:return e.Number;case E.Boolean:return e.Boolean;case E.Array:return e.Array}return e.Function}var lr=class{constructor(t){this._worker=t}provideLinks(t,e){const r=t.uri;return this._worker(r).then(n=>n.findDocumentLinks(r.toString())).then(n=>{if(n)return{links:n.map(a=>({range:S(a.range),url:a.target}))}})}},Bn=class{constructor(t){this._worker=t}provideDocumentFormattingEdits(t,e,r){const n=t.uri;return this._worker(n).then(a=>a.format(n.toString(),null,yn(e)).then(s=>{if(s&&s.length!==0)return s.map(Y)}))}},Kn=class{constructor(t){this._worker=t,this.canFormatMultipleRanges=!1}provideDocumentRangeFormattingEdits(t,e,r,n){const a=t.uri;return this._worker(a).then(s=>s.format(a.toString(),kn(e),yn(r)).then(c=>{if(c&&c.length!==0)return c.map(Y)}))}};function yn(t){return{tabSize:t.tabSize,insertSpaces:t.insertSpaces}}var Wn=class{constructor(t){this._worker=t}provideDocumentColors(t,e){const r=t.uri;return this._worker(r).then(n=>n.findDocumentColors(r.toString())).then(n=>{if(n)return n.map(a=>({color:a.color,range:S(a.range)}))})}provideColorPresentations(t,e,r){const n=t.uri;return this._worker(n).then(a=>a.getColorPresentations(n.toString(),e.color,kn(e.range))).then(a=>{if(a)return a.map(s=>{let c={label:s.label};return s.textEdit&&(c.textEdit=Y(s.textEdit)),s.additionalTextEdits&&(c.additionalTextEdits=s.additionalTextEdits.map(Y)),c})})}},Hn=class{constructor(t){this._worker=t}provideFoldingRanges(t,e,r){const n=t.uri;return this._worker(n).then(a=>a.getFoldingRanges(n.toString(),e)).then(a=>{if(a)return a.map(s=>{const c={start:s.startLine+1,end:s.endLine+1};return s.kind!==void 0&&(c.kind=function(u){switch(u){case J.Comment:return d.languages.FoldingRangeKind.Comment;case J.Imports:return d.languages.FoldingRangeKind.Imports;case J.Region:return d.languages.FoldingRangeKind.Region}}(s.kind)),c})})}},dn,i,Xn=class{constructor(t){this._worker=t}provideSelectionRanges(t,e,r){const n=t.uri;return this._worker(n).then(a=>a.getSelectionRanges(n.toString(),e.map(j))).then(a=>{if(a)return a.map(s=>{const c=[];for(;s;)c.push({range:S(s.range)}),s=s.parent;return c})})}};function xe(t){return t===32||t===9}function $(t){return t===10||t===13}function B(t){return t>=48&&t<=57}(i=dn||(dn={}))[i.lineFeed=10]="lineFeed",i[i.carriageReturn=13]="carriageReturn",i[i.space=32]="space",i[i._0=48]="_0",i[i._1=49]="_1",i[i._2=50]="_2",i[i._3=51]="_3",i[i._4=52]="_4",i[i._5=53]="_5",i[i._6=54]="_6",i[i._7=55]="_7",i[i._8=56]="_8",i[i._9=57]="_9",i[i.a=97]="a",i[i.b=98]="b",i[i.c=99]="c",i[i.d=100]="d",i[i.e=101]="e",i[i.f=102]="f",i[i.g=103]="g",i[i.h=104]="h",i[i.i=105]="i",i[i.j=106]="j",i[i.k=107]="k",i[i.l=108]="l",i[i.m=109]="m",i[i.n=110]="n",i[i.o=111]="o",i[i.p=112]="p",i[i.q=113]="q",i[i.r=114]="r",i[i.s=115]="s",i[i.t=116]="t",i[i.u=117]="u",i[i.v=118]="v",i[i.w=119]="w",i[i.x=120]="x",i[i.y=121]="y",i[i.z=122]="z",i[i.A=65]="A",i[i.B=66]="B",i[i.C=67]="C",i[i.D=68]="D",i[i.E=69]="E",i[i.F=70]="F",i[i.G=71]="G",i[i.H=72]="H",i[i.I=73]="I",i[i.J=74]="J",i[i.K=75]="K",i[i.L=76]="L",i[i.M=77]="M",i[i.N=78]="N",i[i.O=79]="O",i[i.P=80]="P",i[i.Q=81]="Q",i[i.R=82]="R",i[i.S=83]="S",i[i.T=84]="T",i[i.U=85]="U",i[i.V=86]="V",i[i.W=87]="W",i[i.X=88]="X",i[i.Y=89]="Y",i[i.Z=90]="Z",i[i.asterisk=42]="asterisk",i[i.backslash=92]="backslash",i[i.closeBrace=125]="closeBrace",i[i.closeBracket=93]="closeBracket",i[i.colon=58]="colon",i[i.comma=44]="comma",i[i.dot=46]="dot",i[i.doubleQuote=34]="doubleQuote",i[i.minus=45]="minus",i[i.openBrace=123]="openBrace",i[i.openBracket=91]="openBracket",i[i.plus=43]="plus",i[i.slash=47]="slash",i[i.formFeed=12]="formFeed",i[i.tab=9]="tab",new Array(20).fill(0).map((t,e)=>" ".repeat(e));var ln,K=200;new Array(K).fill(0).map((t,e)=>`
`+" ".repeat(e)),new Array(K).fill(0).map((t,e)=>"\r"+" ".repeat(e)),new Array(K).fill(0).map((t,e)=>`\r
`+" ".repeat(e)),new Array(K).fill(0).map((t,e)=>`
`+"	".repeat(e)),new Array(K).fill(0).map((t,e)=>"\r"+"	".repeat(e)),new Array(K).fill(0).map((t,e)=>`\r
`+"	".repeat(e)),(ln||(ln={})).DEFAULT={allowTrailingComma:!1};var gn,T,fn,g,hn,f,zn=function(t,e=!1){const r=t.length;let n=0,a="",s=0,c=16,u=0,b=0,l=0,p=0,h=0;function P(m,N){let C=0,y=0;for(;C<m||!N;){let I=t.charCodeAt(n);if(I>=48&&I<=57)y=16*y+I-48;else if(I>=65&&I<=70)y=16*y+I-65+10;else{if(!(I>=97&&I<=102))break;y=16*y+I-97+10}n++,C++}return C<m&&(y=-1),y}function D(){if(a="",h=0,s=n,b=u,p=l,n>=r)return s=r,c=17;let m=t.charCodeAt(n);if(xe(m)){do n++,a+=String.fromCharCode(m),m=t.charCodeAt(n);while(xe(m));return c=15}if($(m))return n++,a+=String.fromCharCode(m),m===13&&t.charCodeAt(n)===10&&(n++,a+=`
`),u++,l=n,c=14;switch(m){case 123:return n++,c=1;case 125:return n++,c=2;case 91:return n++,c=3;case 93:return n++,c=4;case 58:return n++,c=6;case 44:return n++,c=5;case 34:return n++,a=function(){let C="",y=n;for(;;){if(n>=r){C+=t.substring(y,n),h=2;break}const I=t.charCodeAt(n);if(I===34){C+=t.substring(y,n),n++;break}if(I!==92){if(I>=0&&I<=31){if($(I)){C+=t.substring(y,n),h=2;break}h=6}n++}else{if(C+=t.substring(y,n),n++,n>=r){h=2;break}switch(t.charCodeAt(n++)){case 34:C+='"';break;case 92:C+="\\";break;case 47:C+="/";break;case 98:C+="\b";break;case 102:C+="\f";break;case 110:C+=`
`;break;case 114:C+="\r";break;case 116:C+="	";break;case 117:const Ae=P(4,!0);Ae>=0?C+=String.fromCharCode(Ae):h=4;break;default:h=5}y=n}}return C}(),c=10;case 47:const N=n-1;if(t.charCodeAt(n+1)===47){for(n+=2;n<r&&!$(t.charCodeAt(n));)n++;return a=t.substring(N,n),c=12}if(t.charCodeAt(n+1)===42){n+=2;const C=r-1;let y=!1;for(;n<C;){const I=t.charCodeAt(n);if(I===42&&t.charCodeAt(n+1)===47){n+=2,y=!0;break}n++,$(I)&&(I===13&&t.charCodeAt(n)===10&&n++,u++,l=n)}return y||(n++,h=1),a=t.substring(N,n),c=13}return a+=String.fromCharCode(m),n++,c=16;case 45:if(a+=String.fromCharCode(m),n++,n===r||!B(t.charCodeAt(n)))return c=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return a+=function(){let C=n;if(t.charCodeAt(n)===48)n++;else for(n++;n<t.length&&B(t.charCodeAt(n));)n++;if(n<t.length&&t.charCodeAt(n)===46){if(n++,!(n<t.length&&B(t.charCodeAt(n))))return h=3,t.substring(C,n);for(n++;n<t.length&&B(t.charCodeAt(n));)n++}let y=n;if(n<t.length&&(t.charCodeAt(n)===69||t.charCodeAt(n)===101))if(n++,(n<t.length&&t.charCodeAt(n)===43||t.charCodeAt(n)===45)&&n++,n<t.length&&B(t.charCodeAt(n))){for(n++;n<t.length&&B(t.charCodeAt(n));)n++;y=n}else h=3;return t.substring(C,y)}(),c=11;default:for(;n<r&&A(m);)n++,m=t.charCodeAt(n);if(s!==n){switch(a=t.substring(s,n),a){case"true":return c=8;case"false":return c=9;case"null":return c=7}return c=16}return a+=String.fromCharCode(m),n++,c=16}}function A(m){if(xe(m)||$(m))return!1;switch(m){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(m){n=m,a="",s=0,c=16,h=0},getPosition:()=>n,scan:e?function(){let m;do m=D();while(m>=12&&m<=15);return m}:D,getToken:()=>c,getTokenValue:()=>a,getTokenOffset:()=>s,getTokenLength:()=>n-s,getTokenStartLine:()=>b,getTokenStartCharacter:()=>s-p,getTokenError:()=>h}};function qn(t){return{getInitialState:()=>new vn(null,null,!1,null),tokenize:(e,r)=>function(n,a,s,c=0){let u=0,b=!1;switch(s.scanError){case 2:a='"'+a,u=1;break;case 1:a="/*"+a,u=2}const l=zn(a);let p=s.lastWasColon,h=s.parents;const P={tokens:[],endState:s.clone()};for(;;){let D=c+l.getPosition(),A="";const m=l.scan();if(m===17)break;if(D===c+l.getPosition())throw new Error("Scanner did not advance, next 3 characters are: "+a.substr(l.getPosition(),3));switch(b&&(D-=u),b=u>0,m){case 1:h=Q.push(h,0),A=mn,p=!1;break;case 2:h=Q.pop(h),A=mn,p=!1;break;case 3:h=Q.push(h,1),A=pn,p=!1;break;case 4:h=Q.pop(h),A=pn,p=!1;break;case 6:A=$n,p=!0;break;case 5:A=Qn,p=!1;break;case 8:case 9:A=Jn,p=!1;break;case 7:A=Gn,p=!1;break;case 10:const N=h?h.type:0;A=p||N===1?Yn:er,p=!1;break;case 11:A=Zn,p=!1}switch(m){case 12:A=nr;break;case 13:A=tr}P.endState=new vn(s.getStateData(),l.getTokenError(),p,h),P.tokens.push({startIndex:D,scopes:A})}return P}(0,e,r)}}(T=gn||(gn={}))[T.None=0]="None",T[T.UnexpectedEndOfComment=1]="UnexpectedEndOfComment",T[T.UnexpectedEndOfString=2]="UnexpectedEndOfString",T[T.UnexpectedEndOfNumber=3]="UnexpectedEndOfNumber",T[T.InvalidUnicode=4]="InvalidUnicode",T[T.InvalidEscapeCharacter=5]="InvalidEscapeCharacter",T[T.InvalidCharacter=6]="InvalidCharacter",(g=fn||(fn={}))[g.OpenBraceToken=1]="OpenBraceToken",g[g.CloseBraceToken=2]="CloseBraceToken",g[g.OpenBracketToken=3]="OpenBracketToken",g[g.CloseBracketToken=4]="CloseBracketToken",g[g.CommaToken=5]="CommaToken",g[g.ColonToken=6]="ColonToken",g[g.NullKeyword=7]="NullKeyword",g[g.TrueKeyword=8]="TrueKeyword",g[g.FalseKeyword=9]="FalseKeyword",g[g.StringLiteral=10]="StringLiteral",g[g.NumericLiteral=11]="NumericLiteral",g[g.LineCommentTrivia=12]="LineCommentTrivia",g[g.BlockCommentTrivia=13]="BlockCommentTrivia",g[g.LineBreakTrivia=14]="LineBreakTrivia",g[g.Trivia=15]="Trivia",g[g.Unknown=16]="Unknown",g[g.EOF=17]="EOF",(f=hn||(hn={}))[f.InvalidSymbol=1]="InvalidSymbol",f[f.InvalidNumberFormat=2]="InvalidNumberFormat",f[f.PropertyNameExpected=3]="PropertyNameExpected",f[f.ValueExpected=4]="ValueExpected",f[f.ColonExpected=5]="ColonExpected",f[f.CommaExpected=6]="CommaExpected",f[f.CloseBraceExpected=7]="CloseBraceExpected",f[f.CloseBracketExpected=8]="CloseBracketExpected",f[f.EndOfFileExpected=9]="EndOfFileExpected",f[f.InvalidCommentToken=10]="InvalidCommentToken",f[f.UnexpectedEndOfComment=11]="UnexpectedEndOfComment",f[f.UnexpectedEndOfString=12]="UnexpectedEndOfString",f[f.UnexpectedEndOfNumber=13]="UnexpectedEndOfNumber",f[f.InvalidUnicode=14]="InvalidUnicode",f[f.InvalidEscapeCharacter=15]="InvalidEscapeCharacter",f[f.InvalidCharacter=16]="InvalidCharacter";var L,mn="delimiter.bracket.json",pn="delimiter.array.json",$n="delimiter.colon.json",Qn="delimiter.comma.json",Jn="keyword.json",Gn="keyword.json",Yn="string.value.json",Zn="number.json",er="string.key.json",tr="comment.block.json",nr="comment.line.json",Q=class xn{constructor(e,r){this.parent=e,this.type=r}static pop(e){return e?e.parent:null}static push(e,r){return new xn(e,r)}static equals(e,r){if(!e&&!r)return!0;if(!e||!r)return!1;for(;e&&r;){if(e===r)return!0;if(e.type!==r.type)return!1;e=e.parent,r=r.parent}return!0}},vn=class Ie{constructor(e,r,n,a){this._state=e,this.scanError=r,this.lastWasColon=n,this.parents=a}clone(){return new Ie(this._state,this.scanError,this.lastWasColon,this.parents)}equals(e){return e===this||!!(e&&e instanceof Ie)&&this.scanError===e.scanError&&this.lastWasColon===e.lastWasColon&&Q.equals(this.parents,e.parents)}getStateData(){return this._state}setStateData(e){this._state=e}};function gr(){return new Promise((t,e)=>{if(!L)return e("JSON not registered!");t(L)})}var rr=class extends Fn{constructor(t,e,r){super(t,e,r.onDidChange),this._disposables.push(d.editor.onWillDisposeModel(n=>{this._resetSchema(n.uri)})),this._disposables.push(d.editor.onDidChangeModelLanguage(n=>{this._resetSchema(n.model.uri)}))}_resetSchema(t){this._worker().then(e=>{e.resetSchema(t.toString())})}};function fr(t){const e=[],r=[],n=new Rn(t);function a(){const{languageId:c,modeConfiguration:u}=t;En(r),u.documentFormattingEdits&&r.push(d.languages.registerDocumentFormattingEditProvider(c,new Bn(L))),u.documentRangeFormattingEdits&&r.push(d.languages.registerDocumentRangeFormattingEditProvider(c,new Kn(L))),u.completionItems&&r.push(d.languages.registerCompletionItemProvider(c,new jn(L,[" ",":",'"']))),u.hovers&&r.push(d.languages.registerHoverProvider(c,new Nn(L))),u.documentSymbols&&r.push(d.languages.registerDocumentSymbolProvider(c,new Vn(L))),u.tokens&&r.push(d.languages.setTokensProvider(c,qn())),u.colors&&r.push(d.languages.registerColorProvider(c,new Wn(L))),u.foldingRanges&&r.push(d.languages.registerFoldingRangeProvider(c,new Hn(L))),u.diagnostics&&r.push(new rr(c,L,t)),u.selectionRanges&&r.push(d.languages.registerSelectionRangeProvider(c,new Xn(L)))}e.push(n),L=(...c)=>n.getLanguageServiceWorker(...c),a(),e.push(d.languages.setLanguageConfiguration(t.languageId,ir));let s=t.modeConfiguration;return t.onDidChange(c=>{c.modeConfiguration!==s&&(s=c.modeConfiguration,a())}),e.push(bn(r)),bn(e)}function bn(t){return{dispose:()=>En(t)}}function En(t){for(;t.length;)t.pop().dispose()}var ir={wordPattern:/(-?\d*\.\d\w*)|([^\[\{\]\}\:\"\,\s]+)/g,comments:{lineComment:"//",blockComment:["/*","*/"]},brackets:[["{","}"],["[","]"]],autoClosingPairs:[{open:"{",close:"}",notIn:["string"]},{open:"[",close:"]",notIn:["string"]},{open:'"',close:'"',notIn:["string"]}]};export{jn as CompletionAdapter,cr as DefinitionAdapter,Fn as DiagnosticsAdapter,Wn as DocumentColorAdapter,Bn as DocumentFormattingEditProvider,sr as DocumentHighlightAdapter,lr as DocumentLinkAdapter,Kn as DocumentRangeFormattingEditProvider,Vn as DocumentSymbolAdapter,Hn as FoldingRangeAdapter,Nn as HoverAdapter,ur as ReferenceAdapter,dr as RenameAdapter,Xn as SelectionRangeAdapter,Rn as WorkerManager,j as fromPosition,kn as fromRange,gr as getWorker,fr as setupMode,S as toRange,Y as toTextEdit};

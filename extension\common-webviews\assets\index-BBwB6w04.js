import{c as b,g as E}from"./SpinnerAugment-uKUHz-bK.js";var $=NaN,M="[object Symbol]",N=/^\s+|\s+$/g,S=/^[-+]0x[0-9a-f]+$/i,W=/^0b[01]+$/i,D=/^0o[0-7]+$/i,F=parseInt,I=typeof b=="object"&&b&&b.Object===Object&&b,k=typeof self=="object"&&self&&self.Object===Object&&self,q=I||k||Function("return this")(),z=Object.prototype.toString,A=Math.max,B=Math.min,g=function(){return q.Date.now()};function O(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function w(e){if(typeof e=="number")return e;if(function(i){return typeof i=="symbol"||function(f){return!!f&&typeof f=="object"}(i)&&z.call(i)==M}(e))return $;if(O(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=O(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(N,"");var o=W.test(e);return o||D.test(e)?F(e.slice(2),o?2:8):S.test(e)?$:+e}const G=E(function(e,t,o){var i,f,p,s,n,c,v=0,h=!1,l=!1,y=!0;if(typeof e!="function")throw new TypeError("Expected a function");function d(r){var u=i,a=f;return i=f=void 0,v=r,s=e.apply(a,u)}function x(r){var u=r-c;return c===void 0||u>=t||u<0||l&&r-v>=p}function m(){var r=g();if(x(r))return T(r);n=setTimeout(m,function(u){var a=t-(u-c);return l?B(a,p-(u-v)):a}(r))}function T(r){return n=void 0,y&&i?d(r):(i=f=void 0,s)}function j(){var r=g(),u=x(r);if(i=arguments,f=this,c=r,u){if(n===void 0)return function(a){return v=a,n=setTimeout(m,t),h?d(a):s}(c);if(l)return n=setTimeout(m,t),d(c)}return n===void 0&&(n=setTimeout(m,t)),s}return t=w(t)||0,O(o)&&(h=!!o.leading,p=(l="maxWait"in o)?A(w(o.maxWait)||0,t):p,y="trailing"in o?!!o.trailing:y),j.cancel=function(){n!==void 0&&clearTimeout(n),v=0,i=c=f=n=void 0},j.flush=function(){return n===void 0?s:T(g())},j});export{G as d};
